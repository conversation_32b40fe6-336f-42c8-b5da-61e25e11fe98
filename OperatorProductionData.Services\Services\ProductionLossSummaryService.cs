﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class ProductionLossSummaryService : RepositoryBase<MonthlyProductionLossSummaryEntity, ProductionLossSummary>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public ProductionLossSummaryService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitProductionLossSummary(ProductionLossSummary ProductionLossSummary)
        {
            _dbContext.ProductionLossSummary.Add(
                _mapper.Map<ProductionLossSummary, MonthlyProductionLossSummaryEntity>(ProductionLossSummary)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
