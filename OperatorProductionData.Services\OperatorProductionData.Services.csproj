﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\OperatorProductionData.Models\OperatorProductionData.Models.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Ae.Stratus.Core.Backend" Version="1.5.5-prerelease" />
    <PackageReference Include="Ae.Stratus.Core.Middleware" Version="1.5.3-prerelease" />
    <PackageReference Include="Ae.Stratus.Core.Backend.CodeGenerator" Version="1.1.7" />
	<PackageReference Include="Ae.Stratus.Services.FileRepository.WebApiServicesV6" Version="2025.3.25.1" />
  </ItemGroup>

	<!--<ItemGroup Condition="'$(Configuration)'=='Debug'">
		<ProjectReference Include="..\Ae.Stratus.core.CodeGenerator\Ae.Stratus.Core.CodeGenerator.csproj" OutputItemType="Analyzer" />
	</ItemGroup>-->

	<ItemGroup>
		<None Include="Database\Scripts\*.json" CopyToOutputDirectory="PreserveNewest" />
	</ItemGroup>
	
	<PropertyGroup>
		<EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
		<CompilerGeneratedFilesOutputPath>Generated</CompilerGeneratedFilesOutputPath>
	</PropertyGroup>

	<ItemGroup>
		<!-- Don't include the output from a previous source generator execution float o future runs; the */** trick here ensures that there's
		at least one subdirectory, which is our key that it's coming from a source generator as opposed to something that is coming from
		some other tool. -->
		<Compile Remove="$(CompilerGeneratedFilesOutputPath)/*/**/*.cs" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Services\AllocationsService.cs" />
	  <Compile Remove="Services\CommodityConsumptionSummaryService.cs" />
	  <Compile Remove="Services\DetailedGasInjectionService.cs" />
	  <Compile Remove="Services\DetailedProductionService.cs" />
	  <Compile Remove="Services\DetailedWaterInjectionService.cs" />
	  <Compile Remove="Services\EquipmentStatusService.cs" />
	  <Compile Remove="Services\LiftingDetailService.cs" />
	  <Compile Remove="Services\LiftingsService.cs" />
	  <Compile Remove="Services\OilStorageByPartnerService.cs" />
	  <Compile Remove="Services\OilStorageSummaryService.cs" />
	  <Compile Remove="Services\OperationCommentsService.cs" />
	  <Compile Remove="Services\OperationSummaryByFacilityService.cs" />
	  <Compile Remove="Services\OperationSummaryByFieldService.cs" />
	  <Compile Remove="Services\OperationSummaryService.cs" />
	  <Compile Remove="Services\ProductionLossSummaryService.cs" />
	  <Compile Remove="Services\WaterQualityService.cs" />
	</ItemGroup>

</Project>
