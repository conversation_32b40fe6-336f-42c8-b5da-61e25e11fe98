﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataDaily
{
    public class DailyWellStatusValidator : AbstractValidator<DailyWellStatus>
    {
        static readonly DateTime _inceptionDate = DateTime.Parse("2022-10-01 00:00:00.0000000");
        public DailyWellStatusValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr011 = validationRuleService.GetValidationRule("OPD-VR011");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr083 = validationRuleService.GetValidationRule("OPD-VR083");
            var vr128 = validationRuleService.GetValidationRule("OPD-VR128");

            var wells = infrastructure?.Blocks?
                .Where(b => b.Id.Equals(blockId))
                .SelectMany(b => b.DevelopmentAreas
                    .SelectMany(da => da.Fields
                        .SelectMany(f => f.Reservoirs.
                            SelectMany(f => f.Wells.Select(f => f.Id)))))
                .ToList();

            RuleFor(x => x.WellId)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => wells.Any(w => w.Equals(x)))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.StatusId)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => infrastructure.ReferenceTables.WellStatus.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.StatusDate)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedStatusDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr128.Message,
                            ErrorCode = vr128.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedStatusDate < _inceptionDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr011.Message,
                            ErrorCode = vr011.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    if (c.InstanceToValidate.ParsedStatusDate >= DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr083.Message,
                            ErrorCode = vr083.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                        .WithMessage(vr023.Message)
                        .WithErrorCode(vr023.Number.ToString())
                        .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.Comments))
                        .WithMessage(vr024.Message)
                        .WithErrorCode(vr024.Number.ToString())
                        .WithName(vr024.Type.ToString());
        }
    }
}
