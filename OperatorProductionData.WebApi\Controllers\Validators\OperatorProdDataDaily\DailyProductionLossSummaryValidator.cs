﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataDaily
{
    public class DailyProductionLossSummaryValidator : AbstractValidator<DailyProductionLossSummary>
    {
        //There can be two 'Others' of Planned or Unplanned Loss Category
        static readonly List<string> Other_Loss_Motive_Id = ["TL_NPL_00006", "TL_PL_00007"];

        public DailyProductionLossSummaryValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr016 = validationRuleService.GetValidationRule("OPD-VR016");
            var vr017 = validationRuleService.GetValidationRule("OPD-VR017");
            var vr018 = validationRuleService.GetValidationRule("OPD-VR018");
            var vr019 = validationRuleService.GetValidationRule("OPD-VR019");
            var vr020 = validationRuleService.GetValidationRule("OPD-VR020");
            var vr021 = validationRuleService.GetValidationRule("OPD-VR021");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr040 = validationRuleService.GetValidationRule("OPD-VR040");
            var vr041 = validationRuleService.GetValidationRule("OPD-VR041");
            var vr126 = validationRuleService.GetValidationRule("OPD-VR126");
            var vr158 = validationRuleService.GetValidationRule("OPD-VR158");

            var complexes = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes;

            var wells = infrastructure?.Blocks?
                .Where(b => b.Id.Equals(blockId))
                .SelectMany(b => b.DevelopmentAreas
                    .SelectMany(da => da.Fields
                        .SelectMany(f => f.Reservoirs.
                            SelectMany(f => f.Wells.Select(f => f.Id)))));

            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => complexes != null && complexes.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr016.Message, blockId))
                    .WithErrorCode(vr016.Number.ToString())
                    .WithName(vr016.Type.ToString());

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    var facilities = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities;
                    if (facilities == null || !facilities.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr017.Message, c.InstanceToValidate.ComplexId),
                            ErrorCode = vr017.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.SystemId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var systems = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems.ToList();
                    if (!string.IsNullOrEmpty(x) && (systems != null && !systems.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr019.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr019.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                    if (string.IsNullOrEmpty(x) && !string.IsNullOrEmpty(c.InstanceToValidate.EquipmentId))
                    {

                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr020.Message,
                            ErrorCode = vr020.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });

                    };
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId)), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.EquipmentId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var equipments = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems?.
                                                     FirstOrDefault(s => s.Id == c.InstanceToValidate.SystemId)?.Equipment.ToList();
                    if (!string.IsNullOrEmpty(x) && (equipments != null && !equipments.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr021.Message, c.InstanceToValidate.SystemId),
                            ErrorCode = vr021.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId && z.Systems.Any(s => s.Id == x.SystemId))), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.WellId)
                .Cascade(CascadeMode.Stop)
                .Empty()
                    .When(x => !(!string.IsNullOrEmpty(x.WellId) && (wells != null && wells.Any(w => w == x.WellId))))
                        .WithMessage(vr010.Message)
                        .WithErrorCode(vr010.Number.ToString());

            RuleFor(x => x.PlannedOilLoss_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                     .WithMessage(vr001.Message)
                     .WithErrorCode(vr001.Number.ToString())
                     .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.PlannedOilLoss_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr040.Message)
                        .WithErrorCode(vr040.Number.ToString())
                        .WithName(vr041.Type.ToString());

            RuleFor(x => x.PlannedOilLoss_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                     .WithMessage(vr001.Message)
                     .WithErrorCode(vr001.Number.ToString())
                     .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.PlannedOilLoss_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr040.Message)
                        .WithErrorCode(vr040.Number.ToString())
                        .WithName(vr040.Type.ToString());

            RuleFor(x => x.UnplannedOilLoss_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                     .WithMessage(vr001.Message)
                     .WithErrorCode(vr001.Number.ToString())
                     .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.UnplannedOilLoss_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr041.Message)
                        .WithErrorCode(vr041.Number.ToString())
                        .WithName(vr041.Type.ToString());

            RuleFor(x => x.UnplannedOilLoss_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                     .WithMessage(vr001.Message)
                     .WithErrorCode(vr001.Number.ToString())
                     .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.UnplannedOilLoss_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr041.Message)
                        .WithErrorCode(vr041.Number.ToString())
                        .WithName(vr041.Type.ToString());

            RuleFor(x => x.LossMotiveId)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Must(x => infrastructure != null && infrastructure.ReferenceTables.LossMotives.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString());

            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                        .WithMessage(vr023.Message)
                        .WithErrorCode(vr023.Number.ToString())
                        .WithName(vr023.Type.ToString());

            //Nested .Wheres on .NotEmpty() doesn't work well
            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => Other_Loss_Motive_Id.Contains(x.LossMotiveId))
                        .WithMessage(vr158.Message)
                        .WithErrorCode(vr158.Number.ToString())
                        .WithName(vr158.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.Comments))
                        .WithMessage(vr024.Message)
                        .WithErrorCode(vr024.Number.ToString())
                        .WithName(vr024.Type.ToString());


        }
    }
}
