﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class OperationCommentsService : RepositoryBase<OperationCommentsEntity, OperationComments>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public OperationCommentsService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOperationComments(OperationComments OperationComments)
        {
            _dbContext.OperationComments.Add(
                _mapper.Map<OperationComments, OperationCommentsEntity>(OperationComments)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
