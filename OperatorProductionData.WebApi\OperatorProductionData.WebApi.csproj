﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Properties\PublishProfiles\**" />
    <Content Remove="Properties\PublishProfiles\**" />
    <EmbeddedResource Remove="Properties\PublishProfiles\**" />
    <None Remove="Properties\PublishProfiles\**" />
    <_WebToolingArtifacts Remove="Properties\PublishProfiles\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Ae.Stratus.Core.Middleware" Version="1.5.3-prerelease" />
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="IdentityModel" Version="6.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.19" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.19" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.19">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.MongoDB" Version="5.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OperatorProductionData.Services\OperatorProductionData.Services.csproj" />
  </ItemGroup>

</Project>
