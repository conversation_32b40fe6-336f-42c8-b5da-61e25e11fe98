﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyGasProduction
    {
        public string FacilityId { get; set; }
        public string LpgTypeId { get; set; }
        public decimal TotalMonthlyProductionMass { get; set; }
        public decimal TotalMonthlyProductionEnergy_OFU { get; set; }
        public decimal TotalMonthlyProductionEnergy_SI { get; set; }
        public decimal TotalMonthlyProductionEnergy_BOE { get; set; }
        public decimal TotalMonthlyProductionVolume_OFU { get; set; }
        public decimal TotalMonthlyProductionVolume_SI { get; set; }
        public decimal TotalMonthlySnlProductionMassShare { get; set; }
        public decimal TotalMonthlySnlProductionEnergyShare_OFU { get; set; }
        public decimal TotalMonthlySnlProductionEnergyShare_SI { get; set; }
        public decimal TotalMonthlySnlProductionEnergyShare_BOE { get; set; }
        public decimal TotalMonthlySnlProductionVolumeShare_OFU { get; set; }
        public decimal TotalMonthlySnlProductionVolumeShare_SI { get; set; }
        public decimal TotalMonthlyDeliveyToFutilaEnergy_BOE { get; set; }
        public decimal TotalMonthlyDeliveyToAngolaEnergy_BOE { get; set; }
    }
}
