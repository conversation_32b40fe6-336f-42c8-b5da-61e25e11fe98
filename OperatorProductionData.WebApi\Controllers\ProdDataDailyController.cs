﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Ae.Stratus.Core.Middleware.Services;
using AutoMapper;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.Notifications;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.Services.DashboardServices.Daily;
using OperatorProductionData.Services.SignalRHubs;
using OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataDaily;
using OperatorProductionData.WebApi.Enums;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataDailyController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataDailyService _service { get; set; }
        private ProdDataDailyRejectedService _rejectedService { get; set; }
        private ProdDataDailyViewService _viewService { get; set; }
        //private ProdDataDailyVerificationViewService _verificationViewService { get; set; }
        private DailyDashboardChartsService _dailyDashboardChartsService { get; set; }
        private ProdDataDailyValidationReportService _validationReportService { get; set; }
        private ValidationRuleCoreService _validationRuleService { get; set; }
        private readonly IHubContext<ProdDataNotificationsHub> _prodDataDailyNotificationsHub;
        private IMapper _mapper { get; set; }

        public ProdDataDailyController(
                ILogger<ProdDataDailyController> logger,
                ProdDataDailyService service,
                ProdDataDailyRejectedService rejectedService,
                ProdDataDailyViewService viewService,
                //ProdDataDailyVerificationViewService verificationViewService,
                DailyDashboardChartsService dailyDashboardChartsService,
                ValidationRuleCoreService validationRuleService,
                IHubContext<ProdDataNotificationsHub> prodDataDailyNotificationsHub,
                IMapper mapper,
                ProdDataDailyValidationReportService validationReportService
        )
        {
            _logger = logger;
            _service = service;
            _rejectedService = rejectedService;
            _viewService = viewService;
            //_verificationViewService = verificationViewService;
            _dailyDashboardChartsService = dailyDashboardChartsService;
            _validationRuleService = validationRuleService;
            _prodDataDailyNotificationsHub = prodDataDailyNotificationsHub;
            _mapper = mapper;
            _validationReportService = validationReportService;
        }

        [HttpPost]
        [Route("SubmitProdDataDaily")]
        public async Task<IActionResult> SubmitProdDataDaily([FromBody] InfrastructureProdDataDaily infrastructureProdDataDaily)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                //ValidationReport
                var report = new ProdDataDailyValidationReport()
                {
                    Block = infrastructureProdDataDaily.ProdDataDaily.BlockId,
                    ProductionDate = infrastructureProdDataDaily.ProdDataDaily.ProductionDate,
                };


                var validator = new ProdDataDailyValidator(EnumAction.Add, infrastructureProdDataDaily.Infrastructure, _validationRuleService, report);
                var result = validator.Validate(infrastructureProdDataDaily.ProdDataDaily);

                if (!result.IsValid)
                {
                    if (result.Errors.Any(e => e.Severity == Severity.Error))
                    {
                        apiResponse.Status = ApiResponseStatus.Error;
                    }
                    else
                    {
                        apiResponse.Status = ApiResponseStatus.Warning;
                    }

                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );

                }

                if (apiResponse.Status == ApiResponseStatus.Error)
                {
                    //Register Rejected ProdDataDaily
                    var prodDataDailyRejected = _mapper.Map<ProdDataDaily, ProdDataDailyRejected>(infrastructureProdDataDaily.ProdDataDaily);
                    prodDataDailyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataDailyRejected);
                    await GenerateNotification(infrastructureProdDataDaily, infrastructureProdDataDaily.ProdDataDaily.ParsedProductionDate, NotificationStyle.Danger, infrastructureProdDataDaily.ProdDataDaily.ProductionDate, false);
                }
                else
                {
                    var newModel = await _service.Add(infrastructureProdDataDaily.ProdDataDaily);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    //Send OPD Daily SignalR notification
                    await GenerateNotification(infrastructureProdDataDaily, infrastructureProdDataDaily.ProdDataDaily.ParsedProductionDate, NotificationStyle.Success, infrastructureProdDataDaily.ProdDataDaily.ProductionDate, true);

                    //report validation
                    report.ProdDataDailyId = newModel.Id;
                    report.LastUpdate = DateTime.Now;
                    report.Version = newModel.Version;
                    await _validationReportService.Add(report);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem submitting Product Data Daily.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPut]
        [Route("ChangeProdDataDaily")]
        public async Task<IActionResult> ChangeProdDataDaily([FromBody] InfrastructureProdDataDaily infrastructureProdDataDaily)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                //ValidationReport
                var report = new ProdDataDailyValidationReport()
                {
                    Block = infrastructureProdDataDaily.ProdDataDaily.BlockId,
                    ProductionDate = infrastructureProdDataDaily.ProdDataDaily.ProductionDate,
                };

                var validator = new ProdDataDailyValidator(EnumAction.Update, infrastructureProdDataDaily.Infrastructure, _validationRuleService, report);
                var result = validator.Validate(infrastructureProdDataDaily.ProdDataDaily);

                if (!result.IsValid)
                {
                    if (result.Errors.Any(e => e.Severity == Severity.Error))
                    {
                        apiResponse.Status = ApiResponseStatus.Error;
                    }
                    else
                    {
                        apiResponse.Status = ApiResponseStatus.Warning;
                    }

                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );

                }

                if (apiResponse.Status == ApiResponseStatus.Error)
                {
                    var prodDataDailyRejected = _mapper.Map<ProdDataDaily, ProdDataDailyRejected>(infrastructureProdDataDaily.ProdDataDaily);
                    prodDataDailyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataDailyRejected);
                    await GenerateNotification(infrastructureProdDataDaily, infrastructureProdDataDaily.ProdDataDaily.ParsedProductionDate, NotificationStyle.Danger, infrastructureProdDataDaily.ProdDataDaily.ProductionDate, false);
                }
                else
                {
                    var newModel = await _service.Update(infrastructureProdDataDaily.ProdDataDaily);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    await GenerateNotification(infrastructureProdDataDaily, infrastructureProdDataDaily.ProdDataDaily.ParsedProductionDate, NotificationStyle.Success, infrastructureProdDataDaily.ProdDataDaily.ProductionDate, true);

                    //report validation
                    report.ProdDataDailyId = newModel.Id;
                    report.LastUpdate = DateTime.Now;
                    report.Version = newModel.Version;
                    await _validationReportService.Add(report);
                }


            }
            catch (Exception ex)
            {
                var errorTitle = "Problem updating Product Data Daily.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataDaily Post - getlist");
            ApiResponse<GridDataLoadResponse<ProdDataDaily>> res = new ApiResponse<GridDataLoadResponse<ProdDataDaily>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataDaily>> apiResponse = res;

                options.SortingOptions = new List<SortingOptions> {
                    new SortingOptions
                    {
                        PropertyName = "ProductionDate",
                        IsAscending = false
                    }
                };

                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error getting sorted, filtered and paged list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily Post - getlist");
                return Ok(res);
            }
        }

        [HttpPost]
        [Route("getviewlist")]
        public async Task<IActionResult> GetViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataDaily Post - getviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataDailyView>> res = new ApiResponse<GridDataLoadResponse<ProdDataDailyView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataDailyView>> apiResponse = res;

                options.SortingOptions = new List<SortingOptions> {
                    new SortingOptions
                    {
                        PropertyName = "ProductionDate",
                        IsAscending = false
                    }
                };

                apiResponse.Response = await _viewService.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error getting sorted, filtered and paged view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily Post - getviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getviewdetailslist")]
        public async Task<IActionResult> GetViewListDetails()
        {
            _logger.LogInformation("Enter ProdDataDaily GET - getviewdetailslist");
            ApiResponse<ICollection<ProdDataDailyViewDetails>> res = new ApiResponse<ICollection<ProdDataDailyViewDetails>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {

                res.Response = await _viewService.GetAllViewDetails();

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error getting sorted, filtered and paged view details list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily GET - getviewdetailslist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getdailydashboarddata")]
        public async Task<IActionResult> GetDailyDashboardData(
            [FromQuery] string productionDateStart,
            [FromQuery] string productionDateEnd, 
            [FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataDaily - GetDailyDashboardData");
            ApiResponse<IEnumerable<ProductionDashboardDto>> res = new ApiResponse<IEnumerable<ProductionDashboardDto>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<IEnumerable<ProductionDashboardDto>> apiResponse = res;
                //apiResponse.Response = await _verificationViewService.GetDailyDashboardData(blockIds);
                apiResponse.Response = await _dailyDashboardChartsService.GetDailyProductionDashboardCharts(productionDateStart, productionDateEnd, blockIds);
                
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error GetDailyDashboardData");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily Get - GetDailyDashboardData");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getproddatadailyviewjson")]
        public async Task<IActionResult> GetProdDataDailyViewJson([FromQuery] Guid id)
        {
            _logger.LogInformation("Enter ProdDataDaily - GetProdDataDailyViewJson");
            ApiResponse<ProdDataDailyGeneric> res = new ApiResponse<ProdDataDailyGeneric>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataDailyGeneric> apiResponse = res;
                var json = await _viewService.GetJson(id);

                //if (json != null)
                    apiResponse.Response = json;
                
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error GetProdDataDailyViewJson");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily Get - GetProdDataDailyViewJson");
                return Ok(res);
            }
        }

        //[HttpGet]
        //[Route("getdailyoverviewdata")]
        //public async Task<IActionResult> GetDailyOverviewData(
        //            [FromQuery] DashboardOverviewDataTypes overviewDataType,
        //            [FromQuery] string[] blockIds,
        //            [FromQuery] string[] oilGasFieldIds
        //)
        //{
        //    _logger.LogInformation("Enter ProdDataDaily - GetDailyOverviewData");
        //    ApiResponse<IEnumerable<OverviewDailyData>> res = new ApiResponse<IEnumerable<OverviewDailyData>>
        //    {
        //        Status = ApiResponseStatus.Success
        //    };
        //    try
        //    {
        //        ApiResponse<IEnumerable<OverviewDailyData>> apiResponse = res;
        //        apiResponse.Response = await _verificationViewService.GetDailyOverviewData(overviewDataType, blockIds, oilGasFieldIds);
        //        return Ok(res);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "ProdDataDaily - Error GetDailyOverviewData");
        //        List<ApiProblemDetails> problems = new List<ApiProblemDetails>
        //        {
        //            new ApiProblemDetails
        //            {
        //                Status = StatusCodes.Status400BadRequest,
        //                Title = ex.Message,
        //                Detail = ex.StackTrace
        //            }
        //        };
        //        res.Status = ApiResponseStatus.Error;
        //        res.Problems = problems;
        //        _logger.LogInformation("Exit ProdDataDaily Get - GetDailyOverviewData");
        //        return Ok(res);
        //    }
        //}


        [HttpGet]
        [Route("getbyid")]
        public async Task<IActionResult> GetById([FromQuery] string blockId, [FromQuery] string productionDate)
        {
            _logger.LogInformation("Enter ProdDataDailyController - GetById");
            ApiResponse<ProdDataDailyView> res = new ApiResponse<ProdDataDailyView>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                var filteringOptions = new List<FilteringOptions>
                {
                    new() {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "BlockId",
                        Value = blockId,
                    },
                    new() {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "ProductionDate",
                        Value = productionDate,
                    }
                };

                var options = new GridDataLoadOptions
                {
                    FilteringOptions = filteringOptions,
                    PageIndex = 0,
                    PageSize = 1
                };

                ApiResponse<GridDataLoadResponse<ProdDataDailyView>> apiResponse = new ApiResponse<GridDataLoadResponse<ProdDataDailyView>>();
                apiResponse.Response = await _viewService.GetSortedFilteredPagedList(options);

                if (apiResponse.Response.Models.Count == 1) {
                    res.Response = apiResponse.Response.Models.ElementAt(0);

                    return Ok(res);
                }
                return BadRequest(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDailyController - Error GetById");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDailyController Get - GetById");
                return Ok(res);
            }
        }

        private async Task GenerateNotification(InfrastructureProdDataDaily infrastructureProdDataDaily, DateTime productionDate, NotificationStyle style, string productionDateFilter, bool isValid)
        {
            try
            {
                await _service.UpdateProdDataDailyRejectedMaterializedView(productionDateFilter);

                if (isValid)
                {
                    await _service.UpdateProdDataDailyVerificationMaterializedView(productionDateFilter);
                }

                var methodName = "NotifyProdDataSubmission";
                await _prodDataDailyNotificationsHub.Clients.All.SendAsync(
                    methodName,
                    new ProdDataNotification
                    {
                        Type = "ProdDataDaily",
                        BlockId = infrastructureProdDataDaily.ProdDataDaily.BlockId,
                        ProductionDate = productionDate,
                        Date = DateTime.Now,
                        NotificationStyle = style
                    });
            }
            catch (Exception ex)
            {
                //
            }
        }

        [HttpGet]
        [Route("getbyblockandproductiondate")]
        public async Task<IActionResult> GetByBlockAndProductionDate([FromQuery] string blockId, [FromQuery] string productionDate)
        {
            _logger.LogInformation("Enter ProdDataDaily - GetByBlockAndProductionDate");
            ApiResponse<ProdDataInfo?> res = new ApiResponse<ProdDataInfo?>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataInfo?> apiResponse = res;
                apiResponse.Response = await _service.GetByBlockAndProductionDate(blockId, productionDate);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error GetByBlockAndProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily Get - GetByBlockAndProductionDate");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getvalidationreport")]
        public async Task<IActionResult> GetValidationReport([FromQuery] string productionDate)
        {
            _logger.LogInformation($"GetValidationReport - {productionDate}");
            ApiResponse<List<ProdDataDailyValidationReport>?> res = new ApiResponse<List<ProdDataDailyValidationReport>?>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<List<ProdDataDailyValidationReport>?> apiResponse = res;
                apiResponse.Response = await _validationReportService.GetValidationReport(productionDate);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDaily - Error GetValidationReport");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily Get - GetValidationReport");
                return Ok(res);
            }
        }
    }
}
