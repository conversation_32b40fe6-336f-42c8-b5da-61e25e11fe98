﻿using OperatorProductionData.Models.Utilities;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{
    public class DetailedProductionForFacility : DetailedProduction
    {
        public string FacilityId { get; set; }
    }

    public class DetailedProductionForFacilityAndField : DetailedProduction
    {
        public string FacilityId { get; set; }
        public string FieldId { get; set; }
    }

    public class DetailedProduction
    {
        public ValidationResult<decimal> ExpectedOilProd_OFU { get; set; }
        public ValidationResult<decimal> ExpectedOilProd_SI { get; set; }
        public ValidationResult<decimal> ActualOilProd_OFU { get; set; }
        public ValidationResult<decimal> ActualOilProd_SI { get; set; }
        public ValidationResult<decimal> GasProd_OFU { get; set; }
        public ValidationResult<decimal> GasProd_SI { get; set; }
        public ValidationResult<decimal> GasLift_OFU { get; set; }
        public ValidationResult<decimal> GasLift_SI { get; set; }
        public ValidationResult<decimal> WaterProd_OFU { get; set; }
        public ValidationResult<decimal> WaterProd_SI { get; set; }
    }
}


