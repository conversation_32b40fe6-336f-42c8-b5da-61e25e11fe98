﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Ae.Stratus.Core.Middleware.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.Notifications;
using OperatorProductionData.Models.Models.OperatorProdDataOnline;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.SignalRHubs;
using OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataOnline;
using OperatorProductionData.WebApi.Enums;
using System.Text;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataOnlineController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataOnlineService _service { get; set; }
        private ValidationRuleCoreService _validationRuleService { get; set; }
        private readonly IHubContext<ProdDataNotificationsHub> _prodDataOnlineNotificationsHub;

        public ProdDataOnlineController(
            ILogger<ProdDataOnlineController> logger,
            ProdDataOnlineService service,
            ValidationRuleCoreService validationRuleService,
            IHubContext<ProdDataNotificationsHub> prodDataOnlineNotificationsHub
        )
        {
            _logger = logger;
            _service = service;
            _validationRuleService = validationRuleService;
            _prodDataOnlineNotificationsHub = prodDataOnlineNotificationsHub;
        }


        [HttpPost]
        [Route("SubmitProdDataOnline")]
        public async Task<ActionResult<ApiResponse<Guid>>> SubmitProdDataOnline([FromBody] InfrastructureProdDataOnline infrastructureProdDataOnline)
        {
            var apiResponse = new ApiResponse<Guid>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                try
                {
                    //Validate data content
                    var validator = new ProdDataOnlineValidator(EnumAction.Add, infrastructureProdDataOnline.Infrastructure, _validationRuleService);
                    var validationResult = validator.Validate(infrastructureProdDataOnline.ProdDataOnline);
                    if (!validationResult.IsValid)
                    {
                        StringBuilder strAux = new();
                        validationResult.Errors.ForEach(x => strAux.Append($"[{x.ErrorCode}-{x.ErrorMessage}-{x.PropertyName}]"));
                        throw new Exception(strAux.ToString());
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"Error validating Prod Data Online (invalid JSON business content):{ex.Message}");
                }

                var modelResult = await _service.Add(infrastructureProdDataOnline.ProdDataOnline);
                apiResponse.Response = modelResult.Id;

                //Send OPD Online SignalR notification - Only if Comment if filled
                try
                {
                    var firstProductionStopElem = infrastructureProdDataOnline.ProdDataOnline.OperationSummary
                        .FirstOrDefault(x => !string.IsNullOrEmpty(x.Comments) && x.GasProd_OFU == 0 && x.OilProd_OFU == 0 && x.WaterProd_OFU == 0);

                    if (firstProductionStopElem != null)
                    {
                        var methodName = "NotifyProdDataSubmission";
                        await _prodDataOnlineNotificationsHub.Clients.All.SendAsync(
                            methodName,
                            new ProdDataNotification
                            {
                                Type = "ProdDataOnline",
                                BlockId = infrastructureProdDataOnline.ProdDataOnline.BlockId,
                                ProductionDate = firstProductionStopElem.ParsedTimestamp,
                                Date = DateTime.Now,
                                CustomMessage = firstProductionStopElem.Comments,
                                NotificationStyle = NotificationStyle.Success
                            });
                    }
                }
                catch (Exception ex)
                {
                    //
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Error processing submit Operator Production Data Online";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetList([FromBody] GridDataLoadOptions options)
        {
            var apiResponse = new ApiResponse<GridDataLoadResponse<ProdDataOnline>>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
            }
            catch (Exception ex)
            {
                var errorTitle = "Error processing getlist Operator Production Data Online";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(new ApiProblemDetails()
                {
                    Status = StatusCodes.Status400BadRequest,
                    Title = errorTitle,
                    Detail = ex?.Message
                });
            }

            return Ok(apiResponse);
        }

        [HttpGet]
        [Route("GetBlockLastDate")]
        public async Task<IActionResult> GetBlockLastDate([FromQuery] string blockId)
        {
            var apiResponse = new ApiResponse<DateTime>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                apiResponse.Response = await _service.GetBlockLastDate(blockId);
            }
            catch (Exception ex)
            {
                var errorTitle = "Error processing getBlockLastDate Operator Production Data Online";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(new ApiProblemDetails()
                {
                    Status = StatusCodes.Status400BadRequest,
                    Title = errorTitle,
                    Detail = ex?.Message
                });
            }

            return Ok(apiResponse);
        }

        [HttpGet]
        [Route("getonlinedashboarddataaccumulated")]
        public async Task<IActionResult> GetProdDataOnlineDashboardDataAccumulated([FromQuery] string productionDate, [FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataOnline - GetProdDataOnlineDashboardDataAccumulated");
            ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>> res = new ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>> apiResponse = res;
                apiResponse.Response = await _service.GetProdDataOnlineDashboardDataAccumulated(productionDate, blockIds);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataOnline - Error GetProdDataOnlineDashboardDataAccumulated");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataOnline Get - GetProdDataOnlineDashboardDataAccumulated");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getonlinedashboarddata")]
        public async Task<IActionResult> GetProdDataOnlineDashboardData([FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataOnline - GetProdDataOnlineDashboardDataAccumulated");
            ApiResponse<IEnumerable<OnlineDashboardData>> res = new ApiResponse<IEnumerable<OnlineDashboardData>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<IEnumerable<OnlineDashboardData>> apiResponse = res;
                apiResponse.Response = await _service.GetProdDataOnlineDashboardData(blockIds);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataOnline - Error GetProdDataOnlineDashboardData");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataOnline Get - GetProdDataOnlineDashboardData");
                return Ok(res);
            }
        }

        [HttpDelete]
        [Route("DeleteById")]
        public async Task<IActionResult> DeleteById([FromQuery] Guid id)
        {
            var apiResponse = new ApiResponse<bool>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                apiResponse.Response = await _service.Delete(id);
            }
            catch (Exception ex)
            {
                var errorTitle = "Error processing DeleteById Operator Production Data Online";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(new ApiProblemDetails()
                {
                    Status = StatusCodes.Status400BadRequest,
                    Title = errorTitle,
                    Detail = ex?.Message
                });
            }

            return Ok(apiResponse);
        }
    }
}
