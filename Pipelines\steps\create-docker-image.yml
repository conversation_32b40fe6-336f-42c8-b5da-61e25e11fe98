parameters:
- name: dockerfilePath
  type: string
  default: '**/Dockerfile'

- name: repositoryName
  type: string
  default: ''

steps:
  - task: qetza.replacetokens.replacetokens-task.replacetokens@3
    displayName: 'Replace tokens'
    inputs:
        rootDirectory: ''
        targetFiles: |
          **/appsettings.json
          **/Dockerfile
          **/identityserverdata.json
          **/identitydata.json
        encoding: 'auto'
        tokenPattern: 'custom'
        tokenPrefix: '#{'
        tokenSuffix: '}#'
        writeBOM: true
        escapeType: 'auto'
        verbosity: 'normal'

  - task: Docker@2
    inputs:
        containerRegistry: 'Azure Container Registry'
        repository: ${{parameters.repositoryName}}
        command: 'buildAndPush'
        Dockerfile: ${{parameters.dockerfilePath}}
        buildContext: $(Build.Repository.LocalPath)
        tags: '$(Build.SourceBranchName)-$(Build.BuildNumber)'