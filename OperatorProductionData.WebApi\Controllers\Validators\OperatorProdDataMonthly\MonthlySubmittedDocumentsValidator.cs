﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly
{
    public class MonthlySubmittedDocumentsValidator : AbstractValidator<MonthlySubmittedDocuments>
    {
        public MonthlySubmittedDocumentsValidator(ValidationRuleCoreService validationRuleService, ReferenceData infrastructure)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr128 = validationRuleService.GetValidationRule("OPD-VR128");

            RuleFor(x => x.DocumentId)
              .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.DocumentTypeId)
              .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Must(x => (infrastructure.ReferenceTables.DocumentTypes.Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString());

            RuleFor(x => x.Date)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr128.Message,
                            ErrorCode = vr128.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.Ref)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());
        }

    }
}
