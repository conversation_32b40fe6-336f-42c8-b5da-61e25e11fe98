﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities;

namespace OperatorProductionData.Services.Services
{
    public class OilStorageByPartnerService : RepositoryBase<OilStorageByPartner, OilStorageByPartner>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public OilStorageByPartnerService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOilStorageByPartner(OilStorageByPartner OilStorageByPartner)
        {
            _dbContext.OilStorageByPartner.Add(
                _mapper.Map<OilStorageByPartner, MonthlyOilStorageByPartnerEntity>(OilStorageByPartner)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
