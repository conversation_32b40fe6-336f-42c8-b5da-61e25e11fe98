﻿using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.Extensions.Configuration;
using OperatorProductionData.Models.Models;

namespace OperatorProductionData.Services.Services
{
    public class GasValidationRuleCoreService
    {
        private readonly Dictionary<string, GasValidationRule> _validationRuleDic;

        private readonly IRepositoryNoSql<GasValidationRule>? _noSqlRepository;

        public GasValidationRuleCoreService(IConfiguration configuration, IRepositoryNoSql<GasValidationRule>? noSqlRepository = null)
        {
            _validationRuleDic = new Dictionary<string, GasValidationRule>();
            _noSqlRepository = noSqlRepository;
            Task.WaitAll(SetValidationRules());
        }

        public GasValidationRule GetValidationRule(string id)
        {
            return _validationRuleDic[id];
        }

        private async Task SetValidationRules()
        {
            GridDataLoadOptions filter = new GridDataLoadOptions
            {
                PageIndex = 0,
                PageSize = 0,
                Culture = "",
                WithTranslation = false
            };

            if (_noSqlRepository != null)
            {
                SetValidationRules((await _noSqlRepository.GetSortedFilteredPagedList(filter)).Models);
            }
        }

        private void SetValidationRules(ICollection<GasValidationRule>? vrList)
        {
            if (vrList == null)
            {
                return;
            }

            foreach (GasValidationRule vr in vrList)
            {
                _validationRuleDic.Add(vr.Id, vr);
            }
        }
    }
}