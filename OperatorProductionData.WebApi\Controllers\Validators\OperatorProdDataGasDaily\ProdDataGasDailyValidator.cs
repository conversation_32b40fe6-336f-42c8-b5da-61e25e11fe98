﻿using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;
using OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasMonthly;
using OperatorProductionData.WebApi.Enums;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasDaily
{
    public class ProdDataGasDailyValidator : AbstractValidator<ProdDataGasDaily>
    {
        readonly DateTime _inceptionDate = DateTime.Parse("2022-10-01 00:00:00.0000000");

        public ProdDataGasDailyValidator(EnumAction action, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");

            switch (action)
            {
                case EnumAction.Add:

                    ValidateContent(referenceData, validationRuleService);

                    break;
                case EnumAction.Update:
                    var updateReasons = referenceData.ReferenceTables.UpdateReasons;

                    RuleFor(x => x.Id)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString());

                    RuleFor(x => x.ReasonId)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString())
                        .Must(x => updateReasons.Any(y => y.Id == x))
                            .WithMessage(vr010.Message)
                            .WithErrorCode(vr010.Number.ToString())
                            .WithName(vr010.Type.ToString());

                    ValidateContent(referenceData, validationRuleService);

                    break;
            }
        }

        private void ValidateContent(GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr009 = validationRuleService.GetValidationRule("OPG-VR009");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr018 = validationRuleService.GetValidationRule("OPG-VR018");
            var vr019 = validationRuleService.GetValidationRule("OPG-VR019");
            var vr024 = validationRuleService.GetValidationRule("OPG-VR024");
            var vr025 = validationRuleService.GetValidationRule("OPG-VR025");
            var vr034 = validationRuleService.GetValidationRule("OPG-VR034");


            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => referenceData.Complexes.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.ProductionDate)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedProductionDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr034.Message,
                            ErrorCode = vr034.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedProductionDate < _inceptionDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr018.Message,
                            ErrorCode = vr018.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedProductionDate >= DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr019.Message,
                            ErrorCode = vr019.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                   .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                       .WithMessage(vr024.Message)
                       .WithErrorCode(vr024.Number.ToString())
                       .WithName(vr024.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                  .When(x => !string.IsNullOrEmpty(x.Comments))
                       .WithMessage(vr025.Message)
                       .WithErrorCode(vr025.Number.ToString())
                       .WithName(vr025.Type.ToString());

            RuleFor(x => x.DailyOperationGasSummary)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyOperationGasSummary)
                .SetValidator(x => new DailyOperationGasSummaryValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

           
            RuleFor(x => x.DailyOperationGasSummaryByType)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyOperationGasSummaryByType)
                .SetValidator(x => new DailyOperationGasSummaryByTypeValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyOperationGasSummaryTotal)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyOperationGasSummaryTotal)
                .SetValidator(x => new DailyOperationGasSummaryTotalValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyStreamFeed)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyStreamFeed)
                .SetValidator(x => new DailyStreamFeedValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyPerformanceMetrics)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyPerformanceMetrics)
                .SetValidator(x => new DailyPerformanceMetricsValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyFeedGasUsageGasComposition)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyFeedGasUsageGasComposition)
                .SetValidator(x => new DailyFeedGasUsageGasCompositionValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyFeedGasUsage)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyFeedGasUsage)
                .SetValidator(x => new DailyFeedGasUsageValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyFeedGasUsageGasCompositionTotal)
               .Cascade(CascadeMode.Stop)
              .Must(x => x != null)
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .Must(x => x != null && x.Count > 0)
                  .WithMessage(vr009.Message)
                  .WithErrorCode(vr009.Number.ToString())
                  .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyFeedGasUsageGasCompositionTotal)
                .SetValidator(x => new DailyFeedGasUsageGasCompositionTotalValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyStorageLoading)
               .Cascade(CascadeMode.Stop)
              .Must(x => x != null)
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .Must(x => x != null && x.Count > 0)
                  .WithMessage(vr009.Message)
                  .WithErrorCode(vr009.Number.ToString())
                  .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyStorageLoading)
                .SetValidator(x => new DailyStorageLoadingValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyStorageLoadingSummary)
               .Cascade(CascadeMode.Stop)
              .Must(x => x != null)
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .Must(x => x != null && x.Count > 0)
                  .WithMessage(vr009.Message)
                  .WithErrorCode(vr009.Number.ToString())
                  .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyStorageLoadingSummary)
                .SetValidator(x => new DailyStorageLoadingSummaryValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyDomesticGasDeliverySummary)
              .Cascade(CascadeMode.Stop)
             .Must(x => x != null)
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .Must(x => x != null && x.Count > 0)
                 .WithMessage(vr009.Message)
                 .WithErrorCode(vr009.Number.ToString())
                 .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyDomesticGasDeliverySummary)
                .SetValidator(x => new DailyDomesticGasDeliverySummaryValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyGasSupply)
             .Cascade(CascadeMode.Stop)
            .Must(x => x != null)
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .Must(x => x != null && x.Count > 0)
                .WithMessage(vr009.Message)
                .WithErrorCode(vr009.Number.ToString())
                .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyGasSupply)
                .SetValidator(x => new DailyGasSupplyValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.DailyGasSupplySummary)
             .Cascade(CascadeMode.Stop)
            .Must(x => x != null)
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .Must(x => x != null && x.Count > 0)
                .WithMessage(vr009.Message)
                .WithErrorCode(vr009.Number.ToString())
                .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyGasSupplySummary)
                .SetValidator(x => new DailyGasSupplySummaryValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyGasDelivery)
             .Cascade(CascadeMode.Stop)
            .Must(x => x != null)
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .Must(x => x != null && x.Count > 0)
                .WithMessage(vr009.Message)
                .WithErrorCode(vr009.Number.ToString())
                .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyGasDelivery)
                .SetValidator(x => new DailyGasDeliveryValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.DailyShipping)
             .Cascade(CascadeMode.Stop)
            .Must(x => x != null)
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .Must(x => x != null && x.Count > 0)
                .WithMessage(vr009.Message)
                .WithErrorCode(vr009.Number.ToString())
                .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DailyShipping)
                .SetValidator(x => new DailyShippingValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.DailyGasSubmittedDocuments)
                .SetValidator(x => new DailyGasSubmittedDocumentsValidator(referenceData, validationRuleService))
            .When(x =>
                x.DailyGasSubmittedDocuments is not null &&
                x.DailyGasSubmittedDocuments.Count > 0, ApplyConditionTo.CurrentValidator);
        }
    }
}