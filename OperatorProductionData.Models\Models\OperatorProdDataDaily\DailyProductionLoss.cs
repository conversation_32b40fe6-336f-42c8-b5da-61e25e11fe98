﻿namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyProductionLoss
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? WellId { get; set; }
        public decimal OilLoss_OFU { get; set; }
        public decimal OilLoss_SI { get; set; }
        public string LossMotiveId { get; set; }
        public int? LossProductionHours { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
