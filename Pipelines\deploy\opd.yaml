apiVersion: 2023-05-01
type: Microsoft.ContainerInstance/containerGroups
name: "opd-aci"
location: westeurope
tags:
  Purpose: OperatorProductionData
properties:
  sku: Standard
  containers:
    - name: "opd"
      properties:
        image: "#{AEEconomics.Container.Registry.Endpoint}#/library/opd:#{Build.SourceBranchName}#-#{Build.BuildNumber}#"
        ports:
          - protocol: TCP
            port: 80
        resources:
          requests:
            memoryInGB: 1
            cpu: 1
  imageRegistryCredentials:
    - server: #{AEEconomics.Container.Registry.Endpoint}#
      username: #{AEEconomics.Container.Registry.Username}#
      password: #{AEEconomics.Container.Registry.Password}#
  restartPolicy: OnFailure
  osType: Linux
  subnetIds:
    - id: "/subscriptions/#{AEEconomics.Subscription.ID}#/resourceGroups/#{AEEconomics.Environment.ResourceGroup}#/providers/Microsoft.Network/virtualNetworks/vnet-private-#{AEEconomics.Environment}#-westeu-001/subnets/snet-private-aci-#{AEEconomics.Environment}#-westeu-001"