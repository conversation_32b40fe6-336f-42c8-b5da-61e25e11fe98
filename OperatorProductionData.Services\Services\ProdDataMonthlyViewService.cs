﻿using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataMonthlyViewService : BaseRepository<ProdDataMonthlyView>
    {
        public ProdDataMonthlyViewService(IMongoCollection<ProdDataMonthlyView> container) : base(container)
        {
        }

        public async Task<ProdDataMonthlyGeneric?> GetJson(Guid id)
        {
            List<ProdDataMonthlyView> returnList =
                    (await _container.FindAsync(x => x.Id == id
                                                )
                    )
                    .ToList();

            return returnList.FirstOrDefault()?.Json;
        }

        public async Task<ICollection<ProdDataMonthlyViewDetails>> GetAllViewDetails()
        {
            var filterBuilder = Builders<ProdDataMonthlyView>.Filter;
            var filterDefinition = filterBuilder.Empty;

            var list = _container.Find(filterDefinition)
                .Project(x => new ProdDataMonthlyViewDetails
                {
                    BlockId = x.BlockId,
                    ProductionYear = x.ProductionYear,
                    ProductionMonth = x.ProductionMonth,
                    CreatedDate = x.CreatedDate,
                    ErrorCount = x.ErrorCount,
                    ErrorCountStr = x.ErrorCount > 0 ? x.ErrorCount.ToString() : string.Empty,
                    Status = x.ErrorCount > 0 ? "Recebido / Não Aceite" : "Recebido",
                    Version = x.Version,
                    OperatorReference = x.Json.OperatorReference,
                    Id = x.Id
                })
                .ToList();

            return list;
        }
    }
}
