﻿using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Common;
using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.ValidationRules;
using Ae.Stratus.Core.Middleware.Base;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ValidationRuleController : NoSqlControllerBase<ValidationRule, ValidationRuleNoSqlService, ValidationRuleController>
    {
        public ValidationRuleController(ILogger<ValidationRuleController> logger, IRepositoryNoSql<ValidationRule> service) : base(logger, (ValidationRuleNoSqlService)service)
        {
        }

        [HttpPost]
        [Route("addbulk")]
        public async Task<IActionResult> AddBulk([FromBody] List<ValidationRule> validationRules)
        {
            _logger.LogInformation("Enter Get - addbulk");
            ApiResponse<bool> res = new ApiResponse<bool>
            {
                Status = ApiResponseStatus.Success,
                Response = true
            };
            try
            {
                await _service.AddBulk(validationRules);

                _logger.LogInformation("Exit Get - addbulk");
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Saving Bulk");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = 400,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };

                res.Response = false;
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit Get - addbulk");
                return Ok(res);
            }
        }
    }
}
