﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class DetailedProductionService : RepositoryBase<DetailedProductionEntity, DetailedProduction>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public DetailedProductionService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitDetailedProduction(DetailedProduction DetailedProduction)
        {
            _dbContext.DetailedProduction.Add(
                _mapper.Map<DetailedProduction, DetailedProductionEntity>(DetailedProduction)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
