using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Utilities;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataDailyVerificationViewController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataDailyVerificationViewService _service { get; set; }
        private IMapper _mapper { get; set; }

        public ProdDataDailyVerificationViewController(
                ILogger<ProdDataDailyController> logger,
                ProdDataDailyVerificationViewService verificationViewService,
                IMapper mapper
        )
        {
            _logger = logger;
            _service = verificationViewService;
            _mapper = mapper;
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetVerificationViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataDailyVerificationViewController Post - getverificationviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataDailyVerificationView>> res = new ApiResponse<GridDataLoadResponse<ProdDataDailyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataDailyVerificationView>> apiResponse = res;

                options.SortingOptions = new List<SortingOptions> {
                    new SortingOptions
                    {
                        PropertyName = "ProductionDate",
                        IsAscending = false
                    }
                };

                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDailyVerificationViewController - Error getting sorted, filtered and paged verification view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDailyVerificationViewController Post - getverificationviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getallbyproductiondaterange")]
        public async Task<IActionResult> GetAllByProductionDateRange([FromQuery] string productionDateStart, [FromQuery] string productionDateEnd)
        {
            _logger.LogInformation("Enter ProdDataDailyVerificationViewController - GetAllByProductionDate");
            ApiResponse<ICollection<ProdDataDailyVerificationView>> res = new ApiResponse<ICollection<ProdDataDailyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdDataDailyVerificationView>> apiResponse = res;
                apiResponse.Response = await _service.GetAllByProductionDateRange(productionDateStart, productionDateEnd);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDailyVerificationViewController - Error GetAllByProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDailyVerificationViewController Get - GetAllByProductionDate");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getbyid")]
        public async Task<IActionResult> GetById([FromQuery] string blockId, [FromQuery] string productionDate)
        {
            _logger.LogInformation("Enter ProdDataDailyVerificationViewController - GetById");
            ApiResponse<ProdDataDailyVerificationView> res = new ApiResponse<ProdDataDailyVerificationView>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataDailyVerificationView> apiResponse = res;
                apiResponse.Response = await _service.GetById(new ProdDataDailyVerificationViewId
                {
                    BlockId = blockId,
                    ProductionDate = productionDate,
                    ParsedProductionDate = DateUtils.ConvertDateTimeString(productionDate, "yyyy-MM-dd")
                });
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataDailyVerificationViewController - Error GetById");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDailyVerificationViewController Get - GetById");
                return Ok(res);
            }
        }
    }
}
