﻿using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataGasMonthlyViewService : BaseRepository<ProdDataGasMonthlyView>
    {
        public ProdDataGasMonthlyViewService(IMongoCollection<ProdDataGasMonthlyView> container) : base(container)
        {
        }

        public async Task<ProdDataGasMonthlyGeneric?> GetJson(Guid id)
        {
            List<ProdDataGasMonthlyView> returnList =
                    (await _container.FindAsync(x => x.Id == id
                                                )
                    )
                    .ToList();

            return returnList.FirstOrDefault()?.Json;
        }

        public async Task<ICollection<ProdDataGasMonthlyViewDetails>> GetAllViewDetails()
        {
            var filterBuilder = Builders<ProdDataGasMonthlyView>.Filter;
            var filterDefinition = filterBuilder.Empty;

            var list = _container.Find(filterDefinition)
                .Project(x => new ProdDataGasMonthlyViewDetails
                {
                    ComplexId = x.ComplexId,
                    ProductionYear = x.ProductionYear,
                    ProductionMonth = x.ProductionMonth,
                    CreatedDate = x.CreatedDate,
                    ErrorCount = x.ErrorCount,
                    ErrorCountStr = x.ErrorCount > 0 ? x.ErrorCount.ToString() : string.Empty,
                    Status = x.ErrorCount > 0 ? "Recebido / Não Aceite" : "Recebido",
                    Version = x.Version,
                    OperatorReference = x.Json.OperatorReference,
                    Id = x.Id
                })
                .ToList();

            return list;
        }
    }
}
