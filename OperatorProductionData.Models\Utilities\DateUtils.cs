﻿using System;
using System.Globalization;

namespace OperatorProductionData.Models.Utilities
{
    public static class DateUtils
    {
        public static DateTime ConvertDateTimeString(string date, string format, DateTimeStyles style = DateTimeStyles.AssumeUniversal)
        {
            DateTime.TryParseExact(
                date,
                format,
                CultureInfo.InvariantCulture,
                style,
                out DateTime parsedDate);
            return parsedDate;
        }
    }
}
