﻿using Ae.Stratus.Core.Backend.Interfaces.NoSQL.Attributes;
using Ae.Stratus.Core.Backend.NoSQL.Base;
using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper.Internal;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.ManualData;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.Reflection;

namespace OperatorProductionData.Services.Services
{
    public class ProdAlngForecastDataService : RepositoryBaseMongoDB<ProdAlngForecastData>
    {
        public ProdAlngForecastDataService(string connectionString, string databaseName) : base(connectionString, databaseName, "ProdAlngForecastData")
        {
        }

        public async Task<bool> AddBulk(List<ProdAlngForecastData> validationRules)
        {
            await _container.InsertManyAsync(validationRules);

            return true;
        }
        public override async Task<bool> Delete(object id)
        {
            Type[] types = typeof(ProdAlngForecastData).GetInterfaces();
            if (!types.Any((Type x) => x == typeof(ISoftDeletable)))
            {
                FilterDefinition<ProdAlngForecastData> filter = ((!types.Any((Type x) => x == typeof(IVersionable))) ? GetFilterForId(id) : GetFilterForVersionsCollectionId((Guid)id, softDeletable: false));
                await _container.FindOneAndDeleteAsync<ProdAlngForecastData>(filter);
            }
            else
            {
                FilterDefinition<ProdAlngForecastData> filter = ((!types.Any((Type x) => x == typeof(IVersionable))) ? GetFilterForId(id) : GetFilterForVersionsCollectionId((Guid)id, softDeletable: true));
                IAsyncCursor<ProdAlngForecastData> modelList = await _container.FindAsync<ProdAlngForecastData>(filter);
                PropertyInfo vpPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault((PropertyInfo x) => x.Has<VersionPropertyAttribute>());
                ParameterExpression parameter = Expression.Parameter(typeof(ProdAlngForecastData));
                ProdAlngForecastData model = modelList.ToList().AsQueryable().MaxBy(Expression.Lambda<Func<ProdAlngForecastData, int>>(Expression.Property(parameter, vpPropertyInfo.Name), new ParameterExpression[1] { parameter }));
                PropertyInfo deletedPropertyInfo = typeof(ISoftDeletable).GetProperties().SingleOrDefault((PropertyInfo x) => x.Has<SoftDeleteAttribute>());
                await IMongoCollectionExtensions.FindOneAndUpdateAsync(update: Builders<ProdAlngForecastData>.Update.Set(Expression.Lambda<Func<ProdAlngForecastData, bool>>(Expression.Property(parameter, deletedPropertyInfo.Name), new ParameterExpression[1] { parameter }), value: true), filter: GetFilterForId(typeof(ProdAlngForecastData).GetProperties().SingleOrDefault((PropertyInfo x) => x.Has<KeyAttribute>()).GetValue(model)), collection: _container);
            }

            return true;
        }

        public async Task<ICollection<ProdAlngForecastData>> GetAllByProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            if (productionYearStart == productionYearEnd)
            {
                return await GetAllByYearProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
            }
            else
            {
                var prodDataMonthlyVerificationViewLilst = new List<ProdAlngForecastData>();
                var referenceYear = productionYearStart;
                while (referenceYear != productionYearEnd)
                {
                    if (referenceYear == productionYearStart)
                    {
                        prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(productionMonthStart, 12, productionYearStart, referenceYear));
                    }
                    else
                    {
                        if (referenceYear == productionMonthEnd)
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, productionMonthEnd, referenceYear, productionYearEnd));
                        }
                        else
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, 12, referenceYear, referenceYear));
                        }
                    }

                    referenceYear++;
                }

                return prodDataMonthlyVerificationViewLilst;
            }
        }

        private async Task<ICollection<ProdAlngForecastData>> GetAllByYearProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            var filteringOptions = new List<FilteringOptions>
                {
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "Year",
                        Value = productionYearStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "Year",
                        Value = productionYearEnd,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "Month",
                        Value = productionMonthStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "Month",
                        Value = productionMonthEnd,
                    }
                };

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions,
                //SortingOptions = sortingOptions,
                PageIndex = 0,
                PageSize = 0,
            };

            return (await GetSortedFilteredPagedList(options)).Models;
        }

        private static FilterDefinition<ProdAlngForecastData> GetFilterForId(object id)
        {
            PropertyInfo propertyInfo = typeof(ProdAlngForecastData).GetProperties().SingleOrDefault((PropertyInfo x) => x.Has<KeyAttribute>());
            ParameterExpression parameterExpression = Expression.Parameter(typeof(ProdAlngForecastData));
            if (id.GetType() == typeof(Guid))
            {
                return Builders<ProdAlngForecastData>.Filter.Eq(Expression.Lambda<Func<ProdAlngForecastData, Guid>>(Expression.Property(parameterExpression, propertyInfo.Name), new ParameterExpression[1] { parameterExpression }), (Guid)id);
            }

            if (id.GetType() == typeof(string))
            {
                return Builders<ProdAlngForecastData>.Filter.Eq(Expression.Lambda<Func<ProdAlngForecastData, string>>(Expression.Property(parameterExpression, propertyInfo.Name), new ParameterExpression[1] { parameterExpression }), (string)id);
            }

            throw new Exception("[Key] property type not valid or implemented.");
        }

        private static FilterDefinition<ProdAlngForecastData> GetFilterForVersionsCollectionId(Guid id, bool softDeletable)
        {
            PropertyInfo propertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault((PropertyInfo x) => x.Has<VersionsCollectionIdAttribute>());
            PropertyInfo propertyInfo2 = typeof(ISoftDeletable).GetProperties().SingleOrDefault((PropertyInfo x) => x.Has<SoftDeleteAttribute>());
            ParameterExpression parameterExpression = Expression.Parameter(typeof(ProdAlngForecastData));
            FilterDefinitionBuilder<ProdAlngForecastData> filter = Builders<ProdAlngForecastData>.Filter;
            FilterDefinition<ProdAlngForecastData> filterDefinition = filter.Eq(Expression.Lambda<Func<ProdAlngForecastData, Guid>>(Expression.Property(parameterExpression, propertyInfo.Name), new ParameterExpression[1] { parameterExpression }), id);
            if (softDeletable)
            {
                FilterDefinition<ProdAlngForecastData> filterDefinition2 = filter.Eq(Expression.Lambda<Func<ProdAlngForecastData, bool>>(Expression.Property(parameterExpression, propertyInfo2.Name), new ParameterExpression[1] { parameterExpression }), value: false);
                return filter.And(filterDefinition, filterDefinition2);
            }

            return filterDefinition;
        }
    }
}
