﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyOilStorageSummary
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string BlendId { get; set; }
        public decimal InitialStock_OFU { get; set; }
        public decimal InitialStock_SI { get; set; }
        public decimal InitialStock_MT { get; set; }
        public decimal Production_OFU { get; set; }
        public decimal Production_SI { get; set; }
        public decimal Production_MT { get; set; }
        public decimal Liftings_OFU { get; set; }
        public decimal Liftings_SI { get; set; }
        public decimal Liftings_MT { get; set; }
        public decimal FinalStock_OFU { get; set; }
        public decimal FinalStock_SI { get; set; }
        public decimal FinalStock_MT { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
