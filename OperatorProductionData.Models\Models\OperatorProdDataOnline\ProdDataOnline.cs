﻿using OperatorProductionData.Models.Utilities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataOnline
{
    public class ProdDataOnline
    {
        [Key]
        public Guid Id { get; set; }
        public string? BlockId { get; set; }
        public string? DateFrom { get; set; }
        public virtual DateTime ParsedDateFrom
        {
            set
            {
                _tempDateFrom = DateUtils.ConvertDateTimeString(DateFrom, "yyyy-MM-dd HH:mm:ss", System.Globalization.DateTimeStyles.AssumeLocal);
            }

            get
            {
                return _tempDateFrom;
            }
        }

        private DateTime _tempDateFrom;

        public string? DateTo { get; set; }
        public virtual DateTime ParsedDateTo
        {
            set
            {
                _tempDateTo = DateUtils.ConvertDateTimeString(DateTo, "yyyy-MM-dd HH:mm:ss", System.Globalization.DateTimeStyles.AssumeLocal);
            }

            get
            {
                return _tempDateTo;
            }
        }

        private DateTime _tempDateTo;

        public string? OperatorReference { get; set; }
        public ICollection<OnlineOperationSummary>? OperationSummary { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public bool? CumulatedValues { get; set; }
    }
}
