﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataGasDailyWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataGasDailyWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> SubmitProdDataGasDaily(GasReferenceData referenceData, ProdDataGasDaily prodDataGasDaily)
        {
            var obj = new ReferenceDataProdDataGasDaily
            {
                GasReferenceData = referenceData,
                ProdDataGasDaily = prodDataGasDaily
            };

            HttpResponseMessage response = await _client.PostAsJsonAsync("proddatagasdaily/SubmitProdDataGasDaily", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error adding model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }
            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error adding model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> ChangeProdDataGasDaily(GasReferenceData referenceData, ProdDataGasDaily prodDataGasDaily)
        {
            var obj = new ReferenceDataProdDataGasDaily
            {
                GasReferenceData = referenceData,
                ProdDataGasDaily = prodDataGasDaily
            };

            HttpResponseMessage response = await _client.PutAsJsonAsync("proddatagasdaily/ChangeProdDataGasDaily", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error changing model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }
            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();

            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error changing model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataGasDaily>>> GetList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatagasdaily/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDaily>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataGasDaily>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataGasDaily>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDaily>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>>> GetViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatagasdaily/getviewlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataGasDailyViewDetails>>> GetViewDetailsList(string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasdaily/getviewdetailslist");
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataGasDailyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataGasDailyViewDetails>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataGasDailyViewDetails>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataGasDailyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataGasDailyGeneric>> GetProdDataGasDailyViewJson(Guid id)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "id", id.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasdaily/getproddatagasdailyviewjson" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataGasDailyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting daily dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataGasDailyGeneric> apiResponse = await httpResponseMessage
                                            .Content
                                            .ReadFromJsonAsync<ApiResponse<ProdDataGasDailyGeneric>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataGasDailyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily dashboard list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>> GetVerificationViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatagasdaily/getverificationviewlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting verification view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting verification view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<Guid?>> GetByComplexAndProductionDate(string complexId, string productionDate)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "complexId", complexId },
                { "productionDate", productionDate }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasdaily/getbycomplexandproductiondate" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<Guid?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetByComplexAndProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<Guid?> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<Guid?>>();
            if (apiResponse == null)
            {
                return new ApiResponse<Guid?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily GetByComplexAndProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;

        }
    }
}
