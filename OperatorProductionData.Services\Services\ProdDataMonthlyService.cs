﻿using MongoDB.Bson;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataMonthlyService : BaseRepository<ProdDataMonthly>
    {
        public ProdDataMonthlyService(IMongoCollection<ProdDataMonthly> container) : base(container)
        {
        }

        /// <summary>
        /// Method to get Info about existing ProdDataMonthly's for the given BlockId and ProductionYear and ProductionMonth
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionYear"></param>
        /// <param name="productionMonth"></param>
        /// <returns></returns>
        public async Task<ProdDataInfo?> GetByBlockAndProductionDate(string blockId, int productionYear, int productionMonth)
        {
            var filterBuilder = Builders<ProdDataMonthly>.Filter;
            var filter = filterBuilder.Eq(x => x.BlockId, blockId)
                            & filterBuilder.Eq(x => x.ProductionYear, productionYear)
                            & filterBuilder.Eq(x => x.ProductionMonth, productionMonth)
                            ;
            var prodDataMonthly = await GetAll(filter);

            return prodDataMonthly
                    .OrderByDescending(x => x.CreatedDate)
                    .Select(x => new ProdDataInfo(x.VersionsCollectionId, x.Version)).FirstOrDefault();
        }
        public async Task<IEnumerable<MonthlyDashboardData>> GetMonthlyDashboardData(ICollection<string> blockIds, bool useActualDate = true)
        {

            int nMonthsAgo = 1;   //TODO :: the number of months should be parametrized
            //Get Database Records
            var initialbaseDate = DateTime.UtcNow.AddMonths(-nMonthsAgo);
            var finalBaseDate = useActualDate == true ? DateTime.UtcNow : DateTime.UtcNow.AddMonths(-1);

            IEnumerable<ProdDataMonthly> result;

            List<MonthlyDashboardData> returnList = new List<MonthlyDashboardData>();

            if (DateTime.UtcNow.Year > initialbaseDate.Year)
            {
                returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && ((x.ProductionMonth >= initialbaseDate.Month && x.ProductionYear == initialbaseDate.Year) ||
                                                        (x.ProductionYear > initialbaseDate.Year && x.ProductionYear < DateTime.UtcNow.Year) ||
                                                        (x.ProductionYear == DateTime.UtcNow.Year && x.ProductionMonth <= finalBaseDate.Month))
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList()
                    .GroupBy(x => new { x.BlockId, x.ProductionMonth, x.ProductionYear })
                    .Select(r => new MonthlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Month = r.Key.ProductionMonth,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();
            }
            else
            {
                returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && x.ProductionMonth >= initialbaseDate.Month
                                                    && (useActualDate == true ? x.ProductionMonth <= DateTime.UtcNow.Month : x.ProductionMonth < DateTime.UtcNow.Month)
                                                    && x.ProductionYear == initialbaseDate.Year
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    )
                    .ToList()
                    .GroupBy(x => new { x.BlockId, x.ProductionMonth, x.ProductionYear })
                    .Select(r => new MonthlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Month = r.Key.ProductionMonth,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();
            }
            this.FillMonthlyMissingValues(blockIds, initialbaseDate, finalBaseDate, returnList, useActualDate);

            return returnList.OrderBy(x => x.BlockId);
        }

        public async Task<IEnumerable<QuarterlyDashboardData>> GetQuarterlyDashboardData(ICollection<string> blockIds, bool useActualQuarter = true)
        {
            int nQuaterlyAgo = 1;   //TODO :: the number of months should be parametrized
            int nQuaterlyAgoMod = nQuaterlyAgo % 4;
            //Get Database Records
            var actualQuater = (int)Math.Ceiling(DateTime.UtcNow.Month / 3M);
            int finalQuater;
            int initialQuarter;
            int finalQuarterYear;
            int initialQuarterYear;

            if (useActualQuarter)
            {
                finalQuater = actualQuater;
                finalQuarterYear = DateTime.UtcNow.Year;
            }
            else
            {
                finalQuater = actualQuater != 1 ? (int)Math.Ceiling(DateTime.UtcNow.Month / 3M) - 1 : 4;
                finalQuarterYear = actualQuater != 1 ? DateTime.UtcNow.Year : DateTime.UtcNow.Year - 1;
            }

            initialQuarter = finalQuater - nQuaterlyAgoMod > 0 ? finalQuater - nQuaterlyAgoMod : 4 - (finalQuater - nQuaterlyAgoMod);
            initialQuarterYear = (finalQuater > initialQuarter ? DateTime.UtcNow.Year : DateTime.UtcNow.Year - 1) - (int)(nQuaterlyAgo / 4);

            int initialQuaterMonth = ((initialQuarter - 1) * 3) + 1;
            var finalQuaterMonth = finalQuater * 3;

            var initialBaseDate = new DateTime(initialQuarterYear, initialQuaterMonth, 1);
            var finalBaseDate = new DateTime(finalQuarterYear, finalQuaterMonth, 1);

            List<QuarterlyDashboardData> returnList = new List<QuarterlyDashboardData>();

            if (finalBaseDate.Year > initialBaseDate.Year)
            {
                returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && ((x.ProductionMonth >= initialBaseDate.Month && x.ProductionYear == initialBaseDate.Year) ||
                                                        (x.ProductionYear > initialBaseDate.Year && x.ProductionYear < finalBaseDate.Year) ||
                                                        (x.ProductionYear == finalBaseDate.Year && x.ProductionMonth <= finalBaseDate.Month))
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList()
                    .Select(x => new
                    {
                        x.BlockId,
                        ProductionQuarter = (int)Math.Ceiling(x.ProductionMonth / 3M),
                        x.ProductionYear,
                        x.OperationSummaryByField,
                    })
                    .GroupBy(x => new { x.BlockId, x.ProductionQuarter, x.ProductionYear }).Select(r => new QuarterlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Quarter = r.Key.ProductionQuarter,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();
            }
            else
            {
                returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && x.ProductionMonth >= initialBaseDate.Month
                                                    && x.ProductionMonth <= finalBaseDate.Month
                                                    && x.ProductionYear == finalBaseDate.Year
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList()
                    .Select(x => new
                    {
                        x.BlockId,
                        ProductionQuarter = (int)Math.Ceiling(x.ProductionMonth / 3M),
                        x.ProductionYear,
                        x.OperationSummaryByField,
                    })
                    .GroupBy(x => new { x.BlockId, x.ProductionQuarter, x.ProductionYear }).Select(r => new QuarterlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Quarter = r.Key.ProductionQuarter,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();
            }
            this.FillQuarterlyMissingValues(blockIds, initialBaseDate, finalBaseDate, returnList, useActualQuarter);

            return returnList.OrderBy(x => x.BlockId);
        }

        public async Task<IEnumerable<YearlyDashboardData>> GetYearlyDashboardData(ICollection<string> blockIds, bool useActualDate = true)
        {

            int nMonthsAgo = 1;   //TODO :: the number of months should be parametrized
            //Get Database Records
            var initialBaseDate = DateTime.UtcNow.AddYears(-nMonthsAgo);
            var finalBaseDate = useActualDate == true ? DateTime.UtcNow : DateTime.UtcNow.AddYears(-1);

            IEnumerable<ProdDataMonthly> result;

            List<YearlyDashboardData> returnList = new List<YearlyDashboardData>();

            returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && x.ProductionYear >= initialBaseDate.Year
                                                    && (useActualDate == true ? x.ProductionYear <= DateTime.UtcNow.Year : x.ProductionYear < DateTime.UtcNow.Year)
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList().GroupBy(x => new { x.BlockId, x.ProductionYear }).Select(r => new YearlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();

            this.FillYearlyMissingValues(blockIds, initialBaseDate, finalBaseDate, returnList, useActualDate);

            return returnList.OrderBy(x => x.BlockId);
        }

        private void FillMonthlyMissingValues(ICollection<string> blockIds, DateTime initialBaseDate, DateTime finalBaseDate, List<MonthlyDashboardData> returnList, bool useActualDate)
        {
            while ((initialBaseDate.Month <= finalBaseDate.Month && initialBaseDate.Year == finalBaseDate.Year) ||
                    (initialBaseDate.Year < finalBaseDate.Year))
            {
                var valuesList = blockIds.Select(blockId => new
                {
                    BlockId = blockId,
                    initialBaseDate.Month,
                    initialBaseDate.Year,
                }).Except(
                returnList.Select(foundValue => new
                {
                    foundValue.BlockId,
                    foundValue.Month,
                    foundValue.Year,
                })).ToList().Select(value => new MonthlyDashboardData
                {
                    Year = value.Year,
                    Month = value.Month,
                    BlockId = value.BlockId,
                });

                returnList.AddRange(valuesList);
                initialBaseDate = initialBaseDate.AddMonths(1);
            }
        }

        private void FillQuarterlyMissingValues(ICollection<string> blockIds, DateTime initialBaseDate, DateTime finalBaseDate, List<QuarterlyDashboardData> returnList, bool useActualDate)
        {
            while ((initialBaseDate.Month <= finalBaseDate.Month && initialBaseDate.Year == finalBaseDate.Year) ||
                    (initialBaseDate.Month > finalBaseDate.Month && initialBaseDate.Year < finalBaseDate.Year))
            {
                var valuesList = blockIds.Select(blockId => new
                {
                    BlockId = blockId,
                    Quarter = (int)Math.Ceiling(initialBaseDate.Month / 3M),
                    initialBaseDate.Year,
                }).Except(
                returnList.Select(foundValue => new
                {
                    foundValue.BlockId,
                    foundValue.Quarter,
                    foundValue.Year,
                })).ToList().Select(value => new QuarterlyDashboardData
                {
                    Year = value.Year,
                    Quarter = value.Quarter,
                    BlockId = value.BlockId,
                });

                returnList.AddRange(valuesList);
                initialBaseDate = initialBaseDate.AddMonths(3);
            }
        }

        private void FillYearlyMissingValues(ICollection<string> blockIds, DateTime initialBaseDate, DateTime finalBaseDate, List<YearlyDashboardData> returnList, bool useActualDate)
        {
            while (initialBaseDate.Year <= finalBaseDate.Year)
            {
                var valuesList = blockIds.Select(blockId => new
                {
                    BlockId = blockId,
                    initialBaseDate.Year,
                }).Except(
                returnList.Select(foundValue => new
                {
                    foundValue.BlockId,
                    foundValue.Year,
                })).ToList().Select(value => new YearlyDashboardData
                {
                    Year = value.Year,
                    BlockId = value.BlockId,
                });

                returnList.AddRange(valuesList);
                initialBaseDate = initialBaseDate.AddYears(1);
            }
        }

        public async Task UpdateProdDataMonthlyRejectedMaterializedView(int productionMonthFilter, int productionYearFilter)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionMonth", productionMonthFilter },
                    { "ProductionYear", productionYearFilter }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataMonthlyRejected" },
                    { "pipeline",
                        new BsonArray
                        {
                            new BsonDocument
                            {
                                { "$match", new BsonDocument
                                    {
                                        { "ProductionMonth", productionMonthFilter },
                                        { "ProductionYear", productionYearFilter }
                                    }
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "BlockId", "$BlockId"},
                    { "ProductionYear", "$ProductionYear"},
                    { "ProductionMonth", "$ProductionMonth"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }

                        }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataMonthlyView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }

        public async Task UpdateProdDataMonthlyVerificationMaterializedView(int productionMonthFilter, int productionYearFilter)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionMonth", productionMonthFilter },
                    { "ProductionYear", productionYearFilter }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"BlockId", "$BlockId"},
                            {"ProductionYear", "$ProductionYear"},
                            {"ProductionMonth", "$ProductionMonth"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "BlockId", "$_id.BlockId" },
                    { "ProductionYear", "$_id.ProductionYear" },
                    { "ProductionMonth", "$_id.ProductionMonth" },
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataMonthlyVerificationView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }
    }
}
