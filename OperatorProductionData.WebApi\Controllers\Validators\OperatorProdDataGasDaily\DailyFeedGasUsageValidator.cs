﻿using FluentValidation;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;
using Upstream.Models.Models;
using FluentValidation.Results;


namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasDaily
{

    public class DailyFeedGasUsageValidator : AbstractValidator<DailyFeedGasUsage>
    {

        public DailyFeedGasUsageValidator(string complexId, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {

            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPG-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr027 = validationRuleService.GetValidationRule("OPG-VR027");
            var vr028 = validationRuleService.GetValidationRule("OPG-VR028");

            var facilities = referenceData?.Complexes?.FirstOrDefault(x => x.Id == complexId)?.Facilities;

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Must(x => facilities.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr027.Message, complexId))
                    .WithErrorCode(vr027.Number.ToString())
                    .WithName(vr027.Type.ToString());


            RuleFor(x => x.GasFeedBlockId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).SelectMany(c => c.GasFeedBlock).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Custom((x, c) =>
                {
                    var gasFeedBlocks = facilities?.FirstOrDefault(y => y.Id == c.InstanceToValidate.FacilityId)?.GasFeedBlock;
                    if (gasFeedBlocks == null || !gasFeedBlocks.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr028.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr028.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => facilities.Any(y => y.Id == x.FacilityId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.FeedGasUsageEnergy_OFU)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageEnergy_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageVolume_OFU)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageVolume_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.FeedGasUsageMonthToDateEnergy_OFU)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageMonthToDateEnergy_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageMonthToDateVolume_OFU)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageMonthToDateVolume_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasUsageGRHV)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



        }


    }


}