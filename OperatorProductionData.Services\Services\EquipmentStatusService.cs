﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class EquipmentStatusService : RepositoryBase<EquipmentStatusEntity, EquipmentStatus>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public EquipmentStatusService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitEquipmentStatus(EquipmentStatus EquipmentStatus)
        {
            _dbContext.EquipmentStatus.Add(
                _mapper.Map<EquipmentStatus, EquipmentStatusEntity>(EquipmentStatus)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
