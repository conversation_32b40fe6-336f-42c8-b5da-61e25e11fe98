﻿using FluentValidation;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Services.Services;
using OperatorProductionData.WebApi.Enums;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasMonthly
{
    public class ProdDataGasMonthlyValidator : AbstractValidator<ProdDataGasMonthly>
    {
        readonly DateTime _inceptionDate = DateTime.Parse("2022-10-01 00:00:00.0000000");

        public ProdDataGasMonthlyValidator(EnumAction action, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");

            switch (action)
            {
                case EnumAction.Add:

                    ValidateContent(referenceData, validationRuleService);

                    break;
                case EnumAction.Update:
                    var updateReasons = referenceData.ReferenceTables.UpdateReasons;

                    RuleFor(x => x.Id)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString());

                    RuleFor(x => x.ReasonId)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString())
                        .Must(x => updateReasons.Any(y => y.Id == x))
                            .WithMessage(vr010.Message)
                            .WithErrorCode(vr010.Number.ToString())
                            .WithName(vr010.Type.ToString());

                    ValidateContent(referenceData, validationRuleService);

                    break;
            }
        }

        private void ValidateContent(GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr009 = validationRuleService.GetValidationRule("OPG-VR009");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr020 = validationRuleService.GetValidationRule("OPG-VR020");
            var vr021 = validationRuleService.GetValidationRule("OPG-VR021");
            var vr022 = validationRuleService.GetValidationRule("OPG-VR022");
            var vr024 = validationRuleService.GetValidationRule("OPG-VR024");
            var vr025 = validationRuleService.GetValidationRule("OPG-VR025");


            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
            .WithName(vr001.Type.ToString())
                .Must(x => referenceData.Complexes.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());


            RuleFor(x => x.ProductionMonth)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .LessThanOrEqualTo(12)
                    .WithMessage(vr020.Message)
                    .WithErrorCode(vr020.Number.ToString())
                    .WithName(vr020.Number.ToString())
                .GreaterThanOrEqualTo(_inceptionDate.Month)
                     .When(x => x.ProductionYear <= _inceptionDate.Year)
                        .WithMessage(vr022.Message)
                        .WithErrorCode(vr022.Number.ToString())
                        .WithName(vr022.Type.ToString())
                .LessThanOrEqualTo(DateTime.Now.Month)
                     .When(x => x.ProductionYear >= DateTime.Now.Year)
                        .WithMessage(vr021.Message)
                        .WithErrorCode(vr021.Number.ToString())
                        .WithName(vr021.Type.ToString())
                .GreaterThanOrEqualTo(1)
                    .WithMessage(vr020.Message)
                    .WithErrorCode(vr020.Number.ToString())
                    .WithName(vr020.Number.ToString());

            RuleFor(x => x.ProductionYear)
              .Cascade(CascadeMode.Stop)
              .NotEmpty()
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .LessThanOrEqualTo(DateTime.Now.Year)
                  .WithMessage(vr021.Message)
                  .WithErrorCode(vr021.Number.ToString())
                  .WithName(vr021.Type.ToString())
               .GreaterThanOrEqualTo(_inceptionDate.Year)
                  .WithMessage(vr022.Message)
                  .WithErrorCode(vr022.Number.ToString())
                  .WithName(vr022.Type.ToString());

            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                   .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                       .WithMessage(vr024.Message)
                       .WithErrorCode(vr024.Number.ToString())
                       .WithName(vr024.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                  .When(x => !string.IsNullOrEmpty(x.Comments))
                       .WithMessage(vr025.Message)
                       .WithErrorCode(vr025.Number.ToString())
                       .WithName(vr025.Type.ToString());           

            RuleFor(x => x.MonthlyGasProduction)
                 .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.MonthlyGasProduction)
                .SetValidator(x => new MonthlyGasProductionValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.MonthlyGasShippingTotalByMonth)
                 .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.MonthlyGasShippingTotalByMonth)
                .SetValidator(x => new MonthlyGasShippingTotalByMonthValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.MonthlyGasShippingTotal)
                 .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.MonthlyGasShippingTotal)
                .SetValidator(x => new MonthlyGasShippingTotalValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.MonthlyGasSubmittedDocuments)
                .SetValidator(x => new MonthlyGasSubmittedDocumentsValidator(referenceData, validationRuleService))
            .When(x =>
                x.MonthlyGasSubmittedDocuments is not null &&
                x.MonthlyGasSubmittedDocuments.Count > 0, ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.MonthlyGasProductionClosingInventory)
                 .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.MonthlyGasProductionClosingInventory)
                .SetValidator(x => new MonthlyGasProductionClosingInventoryValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);


            RuleFor(x => x.MonthlyGasProductionAverageComposition)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.MonthlyGasProductionAverageComposition)
                .SetValidator(x => new MonthlyGasProductionAverageCompositionValidator(x.ComplexId, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.OperationGasForecastByDay)
                .Cascade(CascadeMode.Stop)
               .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.OperationGasForecastByDay)
                .SetValidator(x => new OperationGasForecastByDayValidator(x.ComplexId,_inceptionDate, referenceData, validationRuleService))
                .When(x => referenceData.Complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

        }
    }
}