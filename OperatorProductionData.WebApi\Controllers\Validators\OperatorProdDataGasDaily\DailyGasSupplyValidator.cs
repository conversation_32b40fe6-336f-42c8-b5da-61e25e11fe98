﻿using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;
using Upstream.Models.Models;


namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasDaily
{

    public class DailyGasSupplyValidator : AbstractValidator<DailyGasSupply>
    {

        public DailyGasSupplyValidator(string complexId, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {

            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPG-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr027 = validationRuleService.GetValidationRule("OPG-VR027");
            var vr030 = validationRuleService.GetValidationRule("OPG-VR030");

            var facilities = referenceData?.Complexes?.FirstOrDefault(x => x.Id == complexId)?.Facilities;

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Must(x => facilities.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr027.Message, complexId))
                    .WithErrorCode(vr027.Number.ToString())
                    .WithName(vr027.Type.ToString());

            RuleFor(x => x.DomesticGasSupplySourceId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).SelectMany(c => c.DomesticGasSupplySources).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Custom((x, c) =>
                {
                    var domesticGasSupplySources = facilities?.FirstOrDefault(y => y.Id == c.InstanceToValidate.FacilityId)?.DomesticGasSupplySources;
                    if (domesticGasSupplySources == null || !domesticGasSupplySources.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr030.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr030.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => facilities.Any(y => y.Id == x.FacilityId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.DomesticGasSupplyVolume_OFU)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString());

            RuleFor(x => x.DomesticGasSupplyVolume_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());


            RuleFor(x => x.DomesticGasSupplyEnergy_OFU)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.DomesticGasSupplyEnergy_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.DomesticGasSupplyEnergy_Boe)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.DomesticGasSupplyMass)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.DomesticGasSupplyGRHV)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

        }


    }


}