﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class ProdDataGasDailyView
    {
        [Key]
        public Guid Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public string ComplexId { get; set; }
        public string ProductionDate { get; set; }
        public int Version { get; set; }
        public ProdDataGasDailyGeneric Json { get; set; }
        public int ErrorCount { get; set; }
    }
}
