﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataOnline;
using OperatorProductionData.WebApi.Enums;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataOnline
{
    public class ProdDataOnlineValidator : AbstractValidator<ProdDataOnline>
    {
        readonly DateTime _inceptionDate = DateTime.Parse("2022-10-01 00:00:00.0000000");

        public ProdDataOnlineValidator(EnumAction action, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");

            switch (action)
            {
                case EnumAction.Add:

                    ValidateContent(infrastructure, validationRuleService);

                    break;
                case EnumAction.Update:
                    throw new Exception("Update not available when validating ProDataOnline");
            }
        }

        private void ValidateContent(ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr009 = validationRuleService.GetValidationRule("OPD-VR009");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr011 = validationRuleService.GetValidationRule("OPD-VR011");
            var vr012 = validationRuleService.GetValidationRule("OPD-VR012");
            var vr128 = validationRuleService.GetValidationRule("OPD-VR128");


            RuleFor(x => x.DateFrom)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedDateFrom == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr128.Message,
                            ErrorCode = vr128.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedDateFrom < _inceptionDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr011.Message,
                            ErrorCode = vr011.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedDateFrom >= DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr012.Message,
                            ErrorCode = vr012.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.DateTo)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedDateTo == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr128.Message,
                            ErrorCode = vr128.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedDateTo < _inceptionDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr011.Message,
                            ErrorCode = vr011.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedDateTo > DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr012.Message,
                            ErrorCode = vr012.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.BlockId)
             .Cascade(CascadeMode.Stop)
             .NotEmpty()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .Must(x => infrastructure.Blocks.Any(y => y.Id == x))
                 .WithMessage(vr010.Message)
                 .WithErrorCode(vr010.Number.ToString())
                 .WithName(vr010.Type.ToString());

            RuleForEach(x => x.OperationSummary)
                .SetValidator(x => new OnlineOperationSummaryValidator(x.BlockId, x.ParsedDateFrom, x.ParsedDateTo, infrastructure, validationRuleService))
                .When(x =>
                    x.OperationSummary is not null &&
                    x.OperationSummary.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId)
                , ApplyConditionTo.CurrentValidator);
        }
    }
}
