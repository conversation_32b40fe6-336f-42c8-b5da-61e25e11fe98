﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyShipping
    {
        public string FacilityId { get; set; }
        public string GasTypeId { get; set; }
        public string VesselIMO { get; set; }
        public string ShippingNumber { get; set; }
        public decimal DailyShippingVolumeLoaded_OFU { get; set; }
        public decimal DailyShippingVolumeLoaded_Sl { get; set; }
        public decimal DailyShippingVolumeLoaded_Gallons { get; set; }
        public decimal DailyShippingVolumeLoaded_Barrels { get; set; }
        public decimal DailyShippingEnergyLoaded_OFU { get; set; }
        public decimal DailyShippingEnergyLoaded_Sl { get; set; }
        public decimal DailyShippingEnergyLoaded_Boe { get; set; }
        public decimal DailyShippingMassLoaded { get; set; }
        public decimal GasDeliveryAverageGRHV { get; set; }
    }
}
