﻿using Ae.Stratus.Core.Common.ValidationRules;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Models.Models.Documents;
using OperatorProductionData.Models.Models.ManualData;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using OperatorProductionData.Models.Models.OperatorProdDataOnline;
using OperatorProductionData.Services.Services;
using System.Globalization;

namespace OperatorProductionData.Services.Database
{
    public class DBSeedHelper
    {
        private ValidationRuleNoSqlService _validationRuleService;
        private GasValidationRuleNoSqlService _gasValidationRuleService;
        private ProdDataInitialMetricService _prodDataInitialMetricService;
        private ProdForecastDataService _prodForecastDataService;
        private ProdHomologousDataService _prodHomologousDataService;
        private ProdAlngForecastDataService _prodAlngForecastDataService;
        private readonly IMongoDatabase _database;

        private readonly string BasePath = string.Format(
                CultureInfo.InvariantCulture,
                "{1}{0}Database{0}Scripts{0}",
                Path.DirectorySeparatorChar,
                AppContext.BaseDirectory);

        public DBSeedHelper(string mongoDBConnString, string NameDB, IMongoDatabase prodDataMongoDb)
        {
            _validationRuleService = new ValidationRuleNoSqlService(mongoDBConnString, NameDB);
            _gasValidationRuleService = new GasValidationRuleNoSqlService(mongoDBConnString, NameDB);
            _prodDataInitialMetricService = new ProdDataInitialMetricService(mongoDBConnString, NameDB);
            _prodForecastDataService = new ProdForecastDataService(mongoDBConnString, NameDB);
            _prodHomologousDataService = new ProdHomologousDataService(mongoDBConnString, NameDB);
            _prodAlngForecastDataService = new ProdAlngForecastDataService(mongoDBConnString, NameDB);
            _database = prodDataMongoDb;
        }

        public async void seedValidationRuleCollection()
        {
            string collectionName = "ValidationRule";
            var seed1 = "OperatorProductionDataDB." + collectionName + ".json";

            List<ValidationRule> validationRules = JsonConvert.DeserializeObject<List<ValidationRule>>(File.ReadAllText(BasePath + seed1));
            if (_validationRuleService._container.AsQueryable().Count() >= 0)
            {
                _ = _validationRuleService._container.Database.DropCollectionAsync(collectionName)
                    .ContinueWith(_ => _validationRuleService.AddBulk(validationRules));
            } 
            else
            {
                _ = _validationRuleService.AddBulk(validationRules);
            }
        }

        public async void seedGasValidationRuleCollection()
        {
            string collectionName = "GasValidationRule";
            var seed1 = "OperatorProductionDataDB." + collectionName + ".json";

            List<GasValidationRule> validationRules = JsonConvert.DeserializeObject<List<GasValidationRule>>(File.ReadAllText(BasePath + seed1));
            if (_gasValidationRuleService._container.AsQueryable().Count() >= 0)
            {
                _ = _gasValidationRuleService._container.Database.DropCollectionAsync(collectionName)
                    .ContinueWith(_ => _gasValidationRuleService.AddBulk(validationRules));
            } 
            else
            {
                _ = _gasValidationRuleService.AddBulk(validationRules);
            }
        }

        public async void seedProdDataInitialMetricCollection()
        {
            string collectionName = "ProdDataInitialMetric";
            var seed1 = "OperatorProductionDataDB." + collectionName + ".json";

            List<ProdDataInitialMetric> records = JsonConvert.DeserializeObject<List<ProdDataInitialMetric>>(File.ReadAllText(BasePath + seed1));

            if (_prodDataInitialMetricService._container.AsQueryable().Count() >= 0)
            {
                _ = _prodDataInitialMetricService._container.Database.DropCollectionAsync(collectionName)
                    .ContinueWith(_ => _prodDataInitialMetricService.AddBulk(records));
            }
            else
            {
                _ = _prodDataInitialMetricService.AddBulk(records);
            }
        }

        public async void seedProdForecastDataCollection()
        {
            string collectionName = "ProdForecastData";
            var seed1 = "OperatorProductionDataDB." + collectionName + ".json";

            List<ProdForecastData> records = JsonConvert.DeserializeObject<List<ProdForecastData>>(File.ReadAllText(BasePath + seed1));

            if (_prodForecastDataService._container.AsQueryable().Count() >= 0)
            {
                _ = _prodForecastDataService._container.Database.DropCollectionAsync(collectionName)
                    .ContinueWith(_ => _prodForecastDataService.AddBulk(records));
            }
            else
            {
                _ = _prodForecastDataService.AddBulk(records);
            }
        }

        public async void seedProdHomologousDataCollection()
        {
            string collectionName = "ProdHomologousData";
            var seed1 = "OperatorProductionDataDB." + collectionName + ".json";

            List<ProdHomologousData> records = JsonConvert.DeserializeObject<List<ProdHomologousData>>(File.ReadAllText(BasePath + seed1));

            if (_prodHomologousDataService._container.AsQueryable().Count() >= 0)
            {
                _ = _prodHomologousDataService._container.Database.DropCollectionAsync(collectionName)
                    .ContinueWith(_ => _prodHomologousDataService.AddBulk(records));
            }
            else
            {
                _ = _prodHomologousDataService.AddBulk(records);
            }
        }
        public async void seedProdAlngForecastDataCollection()
        {
            string collectionName = "ProdAlngForecastData";
            var seed1 = "OperatorProductionDataDB." + collectionName + ".json";

            List<ProdAlngForecastData> records = JsonConvert.DeserializeObject<List<ProdAlngForecastData>>(File.ReadAllText(BasePath + seed1));

            if (_prodAlngForecastDataService._container.AsQueryable().Count() >= 0)
            {
                _ = _prodAlngForecastDataService._container.Database.DropCollectionAsync(collectionName)
                    .ContinueWith(_ => _prodAlngForecastDataService.AddBulk(records));
            }
            else
            {
                _ = _prodAlngForecastDataService.AddBulk(records);
            }
        }

        public void CreateDbIndexes(
                IMongoCollection<ProdDataDaily> prodDataDailyContainer,
                IMongoCollection<ProdDataMonthly> prodDataMonthlyContainer,
                IMongoCollection<ProdDataDailyView> prodDataDailyViewContainer,
                IMongoCollection<ProdDataMonthlyView> prodDataMonthlyViewContainer,
                IMongoCollection<ProdDataDailyRejected> prodDataDailyRejectedContainer,
                IMongoCollection<ProdDataMonthlyRejected> prodDataMonthlyRejectedContainer,
                IMongoCollection<ProdDataDailyVerificationView> prodDataDailyVerificationViewContainer,
                IMongoCollection<ProdDataMonthlyVerificationView> prodDataMonthlyVerificationViewContainer,
                IMongoCollection<ProdDataGasDaily> prodDataGasDailyContainer,
                IMongoCollection<ProdDataGasMonthly> prodDataGasMonthlyContainer,
                IMongoCollection<ProdDataGasDailyView> prodDataGasDailyViewContainer,
                IMongoCollection<ProdDataGasMonthlyView> prodDataGasMonthlyViewContainer,
                IMongoCollection<ProdDataGasDailyRejected> prodDataGasDailyRejectedContainer,
                IMongoCollection<ProdDataGasMonthlyRejected> prodDataGasMonthlyRejectedContainer,
                IMongoCollection<ProdDataGasDailyVerificationView> prodDataGasDailyVerificationViewContainer,
                IMongoCollection<ProdDataGasMonthlyVerificationView> prodDataGasMonthlyVerificationViewContainer,
                IMongoCollection<DocumentReferences> prodDocumentReferencesContainer,
                IMongoCollection<ProdDataDailyValidationReport> prodDataDailyValidationReportContainer,
                IMongoCollection<ProdDataOnline> prodDataOnlineContainer
        )
        {
            //ProdDataDaily
            var blockIdIndexModel = new CreateIndexModel<ProdDataDaily>(Builders<ProdDataDaily>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModel = new CreateIndexModel<ProdDataDaily>(Builders<ProdDataDaily>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var blockIdProductionDateIndexModel = new CreateIndexModel<ProdDataDaily>(Builders<ProdDataDaily>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionDate));
            var blockIdProdDateVersionIndexModel = new CreateIndexModel<ProdDataDaily>(Builders<ProdDataDaily>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionDate)
                .Ascending(m => m.Version),
                new CreateIndexOptions
                {
                    Unique = true,
                    Name = "PrdDtDaily_Blck_PrdDt_Vers"
                });
            var parsedProductionDateIndexModel = new CreateIndexModel<ProdDataDaily>(Builders<ProdDataDaily>.IndexKeys
                .Ascending(m => m.ParsedProductionDate));
            var blockIdParsedProductionDateIndexModel = new CreateIndexModel<ProdDataDaily>(Builders<ProdDataDaily>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ParsedProductionDate));

            prodDataDailyContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModel,
                    productionDateIndexModel,
                    blockIdProductionDateIndexModel,
                    parsedProductionDateIndexModel,
                    blockIdParsedProductionDateIndexModel,
                    blockIdProdDateVersionIndexModel
                }
            );

            //ProdDataDailyView
            var blockIdIndexModelView = new CreateIndexModel<ProdDataDailyView>(Builders<ProdDataDailyView>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelView = new CreateIndexModel<ProdDataDailyView>(Builders<ProdDataDailyView>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var blockIdProductionDateIndexModelView = new CreateIndexModel<ProdDataDailyView>(Builders<ProdDataDailyView>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionDate));

            prodDataDailyViewContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelView,
                    productionDateIndexModelView,
                    blockIdProductionDateIndexModelView
                }
            );

            //ProdDataDailyRejected
            var blockIdIndexModelRej = new CreateIndexModel<ProdDataDailyRejected>(Builders<ProdDataDailyRejected>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelRej = new CreateIndexModel<ProdDataDailyRejected>(Builders<ProdDataDailyRejected>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var blockIdProductionDateIndexModelRej = new CreateIndexModel<ProdDataDailyRejected>(Builders<ProdDataDailyRejected>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionDate));

            prodDataDailyRejectedContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelRej,
                    productionDateIndexModelRej,
                    blockIdProductionDateIndexModelRej
                }
            );

            //ProdDataDailyVerificationView
            var blockIdIndexModelVerifView = new CreateIndexModel<ProdDataDailyVerificationView>(Builders<ProdDataDailyVerificationView>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelVerifView = new CreateIndexModel<ProdDataDailyVerificationView>(Builders<ProdDataDailyVerificationView>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var blockIdProductionDateIndexModelVerifView = new CreateIndexModel<ProdDataDailyVerificationView>(Builders<ProdDataDailyVerificationView>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionDate),
                new CreateIndexOptions
                {
                    Unique = true,
                    Name = "PrdDtDailyVerifView_Blck_PrdDt"
                });
            var parsedProductionDateIndexModelVerifView = new CreateIndexModel<ProdDataDailyVerificationView>(Builders<ProdDataDailyVerificationView>.IndexKeys
                .Ascending(m => m.ParsedProductionDate));
            var blockIdParsedProductionDateIndexModelVerifView = new CreateIndexModel<ProdDataDailyVerificationView>(Builders<ProdDataDailyVerificationView>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ParsedProductionDate));

            prodDataDailyVerificationViewContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelVerifView,
                    productionDateIndexModelVerifView,
                    blockIdProductionDateIndexModelVerifView,
                    parsedProductionDateIndexModelVerifView,
                    blockIdParsedProductionDateIndexModelVerifView
                }
            );

            //ProdDataMonthly
            var blockIdIndexModelMonthly = new CreateIndexModel<ProdDataMonthly>(Builders<ProdDataMonthly>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelMonthly = new CreateIndexModel<ProdDataMonthly>(Builders<ProdDataMonthly>.IndexKeys
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var blockIdProductionDateIndexModelMonthly = new CreateIndexModel<ProdDataMonthly>(Builders<ProdDataMonthly>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var blockIdProdDateVersIndexModelMonthly = new CreateIndexModel<ProdDataMonthly>(Builders<ProdDataMonthly>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth)
                .Ascending(m => m.Version),
                new CreateIndexOptions
                {
                    Unique = true,
                    Name = "PrdDtMonthly_Blck_PrdYr_PrdMnth_Vers"
                });

            prodDataMonthlyContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelMonthly,
                    productionDateIndexModelMonthly,
                    blockIdProductionDateIndexModelMonthly,
                    blockIdProdDateVersIndexModelMonthly
                }
            );

            //ProdDataMonthlyView
            var blockIdIndexModelMonthlyView = new CreateIndexModel<ProdDataMonthlyView>(Builders<ProdDataMonthlyView>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelMonthlyView = new CreateIndexModel<ProdDataMonthlyView>(Builders<ProdDataMonthlyView>.IndexKeys
             .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var blockIdProductionDateIndexModelMonthlyView = new CreateIndexModel<ProdDataMonthlyView>(Builders<ProdDataMonthlyView>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataMonthlyViewContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelMonthlyView,
                    productionDateIndexModelMonthlyView,
                    blockIdProductionDateIndexModelMonthlyView
                }
            );

            //ProdDataMonthlyRejected
            var blockIdIndexModelMonthlyRej = new CreateIndexModel<ProdDataMonthlyRejected>(Builders<ProdDataMonthlyRejected>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelMonthlyRej = new CreateIndexModel<ProdDataMonthlyRejected>(Builders<ProdDataMonthlyRejected>.IndexKeys
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var blockIdProductionDateIndexModelMonthlyRej = new CreateIndexModel<ProdDataMonthlyRejected>(Builders<ProdDataMonthlyRejected>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataMonthlyRejectedContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelMonthlyRej,
                    productionDateIndexModelMonthlyRej,
                    blockIdProductionDateIndexModelMonthlyRej
                }
            );

            //ProdDataMonthlyVerificationView
            var blockIdIndexModelMonthlyVerifView = new CreateIndexModel<ProdDataMonthlyVerificationView>(Builders<ProdDataMonthlyVerificationView>.IndexKeys
                .Ascending(m => m.BlockId));
            var productionDateIndexModelMonthlyVerifView = new CreateIndexModel<ProdDataMonthlyVerificationView>(Builders<ProdDataMonthlyVerificationView>.IndexKeys
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var blockIdProductionDateIndexModelMonthlyVerifView = new CreateIndexModel<ProdDataMonthlyVerificationView>(Builders<ProdDataMonthlyVerificationView>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataMonthlyVerificationViewContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelMonthlyVerifView,
                    productionDateIndexModelMonthlyVerifView,
                    blockIdProductionDateIndexModelMonthlyVerifView
                }
            );

            //ProdDataGasDaily
            var complexIdIndexModelGas = new CreateIndexModel<ProdDataGasDaily>(Builders<ProdDataGasDaily>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGas = new CreateIndexModel<ProdDataGasDaily>(Builders<ProdDataGasDaily>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var complexIdProductionDateIndexModelGas = new CreateIndexModel<ProdDataGasDaily>(Builders<ProdDataGasDaily>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionDate));
            var parsedProductionDateIndexModelGas = new CreateIndexModel<ProdDataGasDaily>(Builders<ProdDataGasDaily>.IndexKeys
                .Ascending(m => m.ParsedProductionDate));
            var complexIdParsedProductionDateIndexModelGas = new CreateIndexModel<ProdDataGasDaily>(Builders<ProdDataGasDaily>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ParsedProductionDate));

            prodDataGasDailyContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGas,
                    productionDateIndexModelGas,
                    complexIdProductionDateIndexModelGas,
                    parsedProductionDateIndexModelGas,
                    complexIdParsedProductionDateIndexModelGas
                }
            );

            //ProdDataGasDailyView
            var complexIdIndexModelGasView = new CreateIndexModel<ProdDataGasDailyView>(Builders<ProdDataGasDailyView>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasView = new CreateIndexModel<ProdDataGasDailyView>(Builders<ProdDataGasDailyView>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var complexIdProductionDateIndexModelGasView = new CreateIndexModel<ProdDataGasDailyView>(Builders<ProdDataGasDailyView>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionDate));

            prodDataGasDailyViewContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasView,
                    productionDateIndexModelGasView,
                    complexIdProductionDateIndexModelGasView
                }
            );

            //ProdDataGasDailyRejected
            var complexIdIndexModelGasRej = new CreateIndexModel<ProdDataGasDailyRejected>(Builders<ProdDataGasDailyRejected>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasRej = new CreateIndexModel<ProdDataGasDailyRejected>(Builders<ProdDataGasDailyRejected>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var complexIdProductionDateIndexModelGasRej = new CreateIndexModel<ProdDataGasDailyRejected>(Builders<ProdDataGasDailyRejected>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionDate));

            prodDataGasDailyRejectedContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasRej,
                    productionDateIndexModelGasRej,
                    complexIdProductionDateIndexModelGasRej
                }
            );

            //ProdDataGasDailyVerificationView
            var complexIdIndexModelGasVerifView = new CreateIndexModel<ProdDataGasDailyVerificationView>(Builders<ProdDataGasDailyVerificationView>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasVerifView = new CreateIndexModel<ProdDataGasDailyVerificationView>(Builders<ProdDataGasDailyVerificationView>.IndexKeys
                .Ascending(m => m.ProductionDate));
            var complexIdProductionDateIndexModelGasVerifView = new CreateIndexModel<ProdDataGasDailyVerificationView>(Builders<ProdDataGasDailyVerificationView>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionDate));
            var parsedProductionDateIndexModelGasVerifView = new CreateIndexModel<ProdDataGasDailyVerificationView>(Builders<ProdDataGasDailyVerificationView>.IndexKeys
                .Ascending(m => m.ParsedProductionDate));
            var complexIdParsedProductionDateIndexModelGasVerifView = new CreateIndexModel<ProdDataGasDailyVerificationView>(Builders<ProdDataGasDailyVerificationView>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ParsedProductionDate));

            prodDataGasDailyVerificationViewContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasVerifView,
                    productionDateIndexModelGasVerifView,
                    complexIdProductionDateIndexModelGasVerifView,
                    parsedProductionDateIndexModelGasVerifView,
                    complexIdParsedProductionDateIndexModelGasVerifView
                }
            );

            //ProdDataGasMonthly
            var complexIdIndexModelGasMonthly = new CreateIndexModel<ProdDataGasMonthly>(Builders<ProdDataGasMonthly>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasMonthly = new CreateIndexModel<ProdDataGasMonthly>(Builders<ProdDataGasMonthly>.IndexKeys
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var complexIdProductionDateIndexModelGasMonthly = new CreateIndexModel<ProdDataGasMonthly>(Builders<ProdDataGasMonthly>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataGasMonthlyContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasMonthly,
                    productionDateIndexModelGasMonthly,
                    complexIdProductionDateIndexModelGasMonthly
                }
            );

            //ProdDataGasMonthlyView
            var complexIdIndexModelGasMonthlyView = new CreateIndexModel<ProdDataGasMonthlyView>(Builders<ProdDataGasMonthlyView>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasMonthlyView = new CreateIndexModel<ProdDataGasMonthlyView>(Builders<ProdDataGasMonthlyView>.IndexKeys
             .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var complexIdProductionDateIndexModelGasMonthlyView = new CreateIndexModel<ProdDataGasMonthlyView>(Builders<ProdDataGasMonthlyView>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataGasMonthlyViewContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasMonthlyView,
                    productionDateIndexModelGasMonthlyView,
                    complexIdProductionDateIndexModelGasMonthlyView
                }
            );

            //ProdDataGasMonthlyRejected
            var complexIdIndexModelGasMonthlyRej = new CreateIndexModel<ProdDataGasMonthlyRejected>(Builders<ProdDataGasMonthlyRejected>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasMonthlyRej = new CreateIndexModel<ProdDataGasMonthlyRejected>(Builders<ProdDataGasMonthlyRejected>.IndexKeys
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var complexIdProductionDateIndexModelGasMonthlyRej = new CreateIndexModel<ProdDataGasMonthlyRejected>(Builders<ProdDataGasMonthlyRejected>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataGasMonthlyRejectedContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasMonthlyRej,
                    productionDateIndexModelGasMonthlyRej,
                    complexIdProductionDateIndexModelGasMonthlyRej
                }
            );

            //ProdDataGasMonthlyVerificationView
            var complexIdIndexModelGasMonthlyVerifView = new CreateIndexModel<ProdDataGasMonthlyVerificationView>(Builders<ProdDataGasMonthlyVerificationView>.IndexKeys
                .Ascending(m => m.ComplexId));
            var productionDateIndexModelGasMonthlyVerifView = new CreateIndexModel<ProdDataGasMonthlyVerificationView>(Builders<ProdDataGasMonthlyVerificationView>.IndexKeys
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));
            var complexIdProductionDateIndexModelGasMonthlyVerifView = new CreateIndexModel<ProdDataGasMonthlyVerificationView>(Builders<ProdDataGasMonthlyVerificationView>.IndexKeys
                .Ascending(m => m.ComplexId)
                .Ascending(m => m.ProductionYear)
                .Ascending(m => m.ProductionMonth));

            prodDataGasMonthlyVerificationViewContainer.Indexes.CreateMany(new[] {
                    complexIdIndexModelGasMonthlyVerifView,
                    productionDateIndexModelGasMonthlyVerifView,
                    complexIdProductionDateIndexModelGasMonthlyVerifView
                }
            );

            var documentCompanyCodeIndexModel = new CreateIndexModel<DocumentReferences>(Builders<DocumentReferences>.IndexKeys
                .Ascending(m => m.CompanyCode));
            prodDocumentReferencesContainer.Indexes.CreateOne(documentCompanyCodeIndexModel);


            //ProdDataDailyValidationReport
            var prodDateIndexModel = new CreateIndexModel<ProdDataDailyValidationReport>(Builders<ProdDataDailyValidationReport>.IndexKeys
                .Ascending(m => m.ProductionDate));

            prodDataDailyValidationReportContainer.Indexes.CreateMany(new[] {
                    prodDateIndexModel
                }
            );

            //ProdDataOnline
            var blockIdIndexModelOnline = new CreateIndexModel<ProdDataOnline>(Builders<ProdDataOnline>.IndexKeys
                .Ascending(m => m.BlockId));
            var parsedDateFromParsedDateToIndexModelOnline = new CreateIndexModel<ProdDataOnline>(Builders<ProdDataOnline>.IndexKeys
                .Ascending(m => m.ParsedDateFrom)
                .Ascending(m => m.ParsedDateTo));
            var blockIdParsedDateFromParsedDateToIndexModelOnline = new CreateIndexModel<ProdDataOnline>(Builders<ProdDataOnline>.IndexKeys
                .Ascending(m => m.BlockId)
                .Ascending(m => m.ParsedDateFrom)
                .Ascending(m => m.ParsedDateTo));

            prodDataOnlineContainer.Indexes.CreateMany(new[] {
                    blockIdIndexModelOnline,
                    parsedDateFromParsedDateToIndexModelOnline,
                    blockIdParsedDateFromParsedDateToIndexModelOnline
                }
            );

        }

        //Creates a new Materialized View in ProdDataDaily Collection
        public IMongoCollection<BsonDocument> CreateProdDataDailyView()
        {
            var prodDataDailyCollection = _database.GetCollection<BsonDocument>("ProdDataDaily");
            //var _prodDataDailyRejectedCollection = _database.GetCollection<BsonDocument>("ProdDataDailyRejected");

            var pipeline = new[]
            {
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataDailyRejected" }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionDate", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "BlockId", "$BlockId"},
                    { "ProductionDate", "$ProductionDate"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //{ "$size", "$ProblemDetails" }
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$out", "ProdDataDailyView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataDailyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataDailyView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataDaily Collection
        public IMongoCollection<BsonDocument> CreateProdDataMonthlyView()
        {
            var prodDataDailyCollection = _database.GetCollection<BsonDocument>("ProdDataMonthly");
            //var _prodDataDailyRejectedCollection = _database.GetCollection<BsonDocument>("ProdDataDailyRejected");

            var pipeline = new[]
            {
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataMonthlyRejected" }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "BlockId", "$BlockId"},
                    { "ProductionYear", "$ProductionYear"},
                    { "ProductionMonth", "$ProductionMonth"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$out", "ProdDataMonthlyView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataDailyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataMonthlyView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataDaily Collection for the Verification process
        public IMongoCollection<BsonDocument> CreateProdDataDailyVerificationView()
        {
            var prodDataDailyCollection = _database.GetCollection<BsonDocument>("ProdDataDaily");

            var pipeline = new[]
            {
                new BsonDocument("$sort", new BsonDocument
                {
                    { "productionDate", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"BlockId", "$BlockId"},
                            {"ProductionDate", "$ProductionDate"},
                            {"ParsedProductionDate", "$ParsedProductionDate"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "BlockId", "$_id.BlockId" },
                    { "ProductionDate", "$_id.ProductionDate" },
                    { "ParsedProductionDate", "$_id.ParsedProductionDate"},
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$out", "ProdDataDailyVerificationView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataDailyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataDailyVerificationView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataMonthly Collection for the Verification process
        public IMongoCollection<BsonDocument> CreateProdDataMonthlyVerificationView()
        {
            var prodDataMonthlyCollection = _database.GetCollection<BsonDocument>("ProdDataMonthly");

            var pipeline = new[]
            {
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"BlockId", "$BlockId"},
                            {"ProductionYear", "$ProductionYear"},
                            {"ProductionMonth", "$ProductionMonth"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "BlockId", "$_id.BlockId" },
                    { "ProductionYear", "$_id.ProductionYear" },
                    { "ProductionMonth", "$_id.ProductionMonth" },
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$out", "ProdDataMonthlyVerificationView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataMonthlyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataMonthlyVerificationView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataGasDaily Collection
        public IMongoCollection<BsonDocument> CreateProdDataGasDailyView()
        {
            var prodDataGasDailyCollection = _database.GetCollection<BsonDocument>("ProdDataGasDaily");
            //var _prodDataGasDailyRejectedCollection = _database.GetCollection<BsonDocument>("ProdDataGasDailyRejected");

            var pipeline = new[]
            {
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataGasDailyRejected" }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionDate", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "ComplexId", "$ComplexId"},
                    { "ProductionDate", "$ProductionDate"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$out", "ProdDataGasDailyView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataGasDailyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataGasDailyView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataGasMonthly Collection
        public IMongoCollection<BsonDocument> CreateProdDataGasMonthlyView()
        {
            var prodDataGasMonthlyCollection = _database.GetCollection<BsonDocument>("ProdDataGasMonthly");
            //var _prodDataGasMonthlyRejectedCollection = _database.GetCollection<BsonDocument>("ProdDataGasMonthlyRejected");

            var pipeline = new[]
            {
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataGasMonthlyRejected" }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "ComplexId", "$ComplexId"},
                    { "ProductionYear", "$ProductionYear"},
                    { "ProductionMonth", "$ProductionMonth"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$out", "ProdDataGasMonthlyView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataGasMonthlyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataGasMonthlyView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataGasDaily Collection for the Verification process
        public IMongoCollection<BsonDocument> CreateProdDataGasDailyVerificationView()
        {
            var prodDataGasDailyCollection = _database.GetCollection<BsonDocument>("ProdDataGasDaily");

            var pipeline = new[]
            {
                new BsonDocument("$sort", new BsonDocument
                {
                    { "productionDate", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"ComplexId", "$ComplexId"},
                            {"ProductionDate", "$ProductionDate"},
                            {"ParsedProductionDate", "$ParsedProductionDate"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "ComplexId", "$_id.ComplexId" },
                    { "ProductionDate", "$_id.ProductionDate" },
                    { "ParsedProductionDate", "$_id.ParsedProductionDate"},
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$out", "ProdDataGasDailyVerificationView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataGasDailyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataGasDailyVerificationView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }

        //Creates a new Materialized View in ProdDataMonthly Collection for the Verification process
        public IMongoCollection<BsonDocument> CreateProdDataGasMonthlyVerificationView()
        {
            var prodDataGasMonthlyCollection = _database.GetCollection<BsonDocument>("ProdDataGasMonthly");

            var pipeline = new[]
            {
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"ComplexId", "$ComplexId"},
                            {"ProductionYear", "$ProductionYear"},
                            {"ProductionMonth", "$ProductionMonth"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "ComplexId", "$_id.ComplexId" },
                    { "ProductionYear", "$_id.ProductionYear" },
                    { "ProductionMonth", "$_id.ProductionMonth" },
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$out", "ProdDataGasMonthlyVerificationView")
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            prodDataGasMonthlyCollection.Aggregate<BsonDocument>(pipeline, aggregateOptions);

            var materializedView = _database.GetCollection<BsonDocument>("ProdDataGasMonthlyVerificationView");
            //return await materializedView.Find(new BsonDocument()).FirstOrDefaultAsync();
            return materializedView;
        }
    }
}
