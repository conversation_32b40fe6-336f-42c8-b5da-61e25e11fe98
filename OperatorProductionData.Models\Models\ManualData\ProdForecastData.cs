﻿using System;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.ManualData
{
    public class ProdForecastData : ManualData
    {
        [Key]
        public Guid Id { get; set; }
        public string BlockId { get; set; }
        public decimal OperatorProductionForecast { get; set; }
        public decimal ProducedWaterForecast { get; set; }
        public decimal ProducedGasForecast { get; set; }
        public decimal GasLiftForecast { get; set; }
        public decimal FuelGasForecast { get; set; }
        public decimal FlaredGasForecast { get; set; }
        public decimal OperatorOperationalEficiencyForecast { get; set; }
        public decimal PlannedLossForecast { get; set; }
        public decimal UnplannedLossForecast { get; set; }
    }
}