﻿using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Common.ValidationRules;
using Ae.Stratus.Core.Middleware.Extensions;
using Ae.Stratus.Services.FileRepository.WebApiServicesV6;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using MongoDB.Driver;
using MongoDB.Driver.Core.Configuration;
using NSwag;
using NSwag.AspNetCore;
using NSwag.Generation.Processors.Security;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Models.Models.Documents;
using OperatorProductionData.Models.Models.ManualData;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using OperatorProductionData.Models.Models.OperatorProdDataOnline;
using OperatorProductionData.Services.BackgroundServices;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Interfaces;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.Services.DashboardServices.Daily;
using OperatorProductionData.Services.Services.DashboardServices.Monthly;
using OperatorProductionData.Services.SignalRHubs;
using OperatorProductionData.WebApi.Mappers;
using Serilog;
using System.Text.Json.Serialization;

namespace OperatorProductionData.WebApi
{
    public static class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            const string MongoDBConnectionStringName = "OperatorProductionDataMongoDB";
            const string MongoDBNameStringName = "OperatorProductionDataDBName";

            var mongoDBConnString = Environment.GetEnvironmentVariable("OPDDB_CONNECTIONSTRING") ?? builder.Configuration.GetConnectionString(MongoDBConnectionStringName);
            var NameDB = builder.Configuration.GetConnectionString(MongoDBNameStringName);

            var title = builder.Configuration.GetValue<string>("SwaggerConfigs:Title");
            var description = builder.Configuration.GetValue<string>("SwaggerConfigs:Description");
            var authorityUrl = builder.Configuration.GetValue<string>("SwaggerConfigs:AuthorityUrl");
            var fileServiceBaseURL = Environment.GetEnvironmentVariable("FILEREPOSITORY_URL") ?? builder.Configuration.GetValue<string>("FileServiceBaseURL");
            var basePath = builder.Configuration.GetValue<string>("SwaggerConfigs:BasePath");
            var scopes = builder.Configuration.GetSection("SwaggerConfigs:Scopes").Get<string[]>();

            builder.Services.AddOpenApiDocument(x =>
            {
                x.DocumentName = $"v1";
                x.Title = title;
                x.Description = description;

                if (!string.IsNullOrEmpty(authorityUrl))
                {
                    x.DocumentProcessors.Add(
                    new SecurityDefinitionAppender("oauth2",
                            scopes ?? (new[] { string.Empty }),
                            new OpenApiSecurityScheme
                            {
                                Type = OpenApiSecuritySchemeType.OAuth2,
                                Flow = OpenApiOAuth2Flow.Application,
                                AuthorizationUrl = $"{authorityUrl}/connect/authorize",
                                TokenUrl = $"{authorityUrl}/connect/token"
                            })
                        );

                    x.OperationProcessors.Add(new OperationSecurityScopeProcessor("oauth2"));
                }

                x.PostProcess = d =>
                {
                    d.Info.Title = title;
                    d.Info.Description = description;
                };
            });

            builder.Services.AddValidationRulesService();

            //---- Mongo Collection with Logger

            var seriLog = new LoggerConfiguration()
                      .ReadFrom.Configuration(builder?.Configuration, "Serilog")
                      .CreateLogger();

            ILoggerFactory loggerFactory = LoggerFactory.Create(logging =>
            {
                logging.AddSerilog(seriLog);
            });
            var settings = MongoClientSettings.FromConnectionString(mongoDBConnString);
            settings.LoggingSettings = new LoggingSettings(loggerFactory);
            //settings.LinqProvider = LinqProvider.V3;


            //----- CONTAINERS
            var ProdDataMongoClient = new MongoClient(settings);
            var ProdDataMongoDb = ProdDataMongoClient.GetDatabase(NameDB);

            var prodDataDailyContainer = ProdDataMongoDb.GetCollection<ProdDataDaily>("ProdDataDaily");
            var prodDataMonthlyContainer = ProdDataMongoDb.GetCollection<ProdDataMonthly>("ProdDataMonthly");
            var prodDataDailyViewContainer = ProdDataMongoDb.GetCollection<ProdDataDailyView>("ProdDataDailyView");
            var prodDataMonthlyViewContainer = ProdDataMongoDb.GetCollection<ProdDataMonthlyView>("ProdDataMonthlyView");
            var prodDataDailyRejectedContainer = ProdDataMongoDb.GetCollection<ProdDataDailyRejected>("ProdDataDailyRejected");
            var prodDataMonthlyRejectedContainer = ProdDataMongoDb.GetCollection<ProdDataMonthlyRejected>("ProdDataMonthlyRejected");
            var prodDataDailyVerificationViewContainer = ProdDataMongoDb.GetCollection<ProdDataDailyVerificationView>("ProdDataDailyVerificationView");
            var prodDataMonthlyVerificationViewContainer = ProdDataMongoDb.GetCollection<ProdDataMonthlyVerificationView>("ProdDataMonthlyVerificationView");
            var prodDataDailyValidationReportContainer = ProdDataMongoDb.GetCollection<ProdDataDailyValidationReport>("ProdDataDailyValidationReport");

            var prodDataGasDailyContainer = ProdDataMongoDb.GetCollection<ProdDataGasDaily>("ProdDataGasDaily");
            var prodDataGasMonthlyContainer = ProdDataMongoDb.GetCollection<ProdDataGasMonthly>("ProdDataGasMonthly");
            var prodDataGasDailyViewContainer = ProdDataMongoDb.GetCollection<ProdDataGasDailyView>("ProdDataGasDailyView");
            var prodDataGasMonthlyViewContainer = ProdDataMongoDb.GetCollection<ProdDataGasMonthlyView>("ProdDataGasMonthlyView");
            var prodDataGasDailyRejectedContainer = ProdDataMongoDb.GetCollection<ProdDataGasDailyRejected>("ProdDataGasDailyRejected");
            var prodDataGasMonthlyRejectedContainer = ProdDataMongoDb.GetCollection<ProdDataGasMonthlyRejected>("ProdDataGasMonthlyRejected");
            var prodDataGasDailyVerificationViewContainer = ProdDataMongoDb.GetCollection<ProdDataGasDailyVerificationView>("ProdDataGasDailyVerificationView");
            var prodDataGasMonthlyVerificationViewContainer = ProdDataMongoDb.GetCollection<ProdDataGasMonthlyVerificationView>("ProdDataGasMonthlyVerificationView");

            var prodDataOnlineContainer = ProdDataMongoDb.GetCollection<ProdDataOnline>("ProdDataOnline");

            var prodDocumentReferencesContainer = ProdDataMongoClient.GetDatabase(NameDB).GetCollection<DocumentReferences>("DocumentReferences");

            //-----SERVICES
            builder.Services.AddScoped<GasValidationRuleCoreService>();
            builder.Services.AddScoped(x => new ProdDataDailyService(prodDataDailyContainer));
            builder.Services.AddScoped(x => new ProdDataMonthlyService(prodDataMonthlyContainer));
            builder.Services.AddScoped(x => new ProdDataDailyViewService(prodDataDailyViewContainer));
            builder.Services.AddScoped(x => new ProdDataMonthlyViewService(prodDataMonthlyViewContainer));
            builder.Services.AddScoped(x => new ProdDataDailyRejectedService(prodDataDailyRejectedContainer));
            builder.Services.AddScoped(x => new ProdDataMonthlyRejectedService(prodDataMonthlyRejectedContainer));
            builder.Services.AddScoped(x => new ProdDataDailyVerificationViewService(prodDataDailyVerificationViewContainer));
            builder.Services.AddScoped(x => new ProdDataMonthlyVerificationViewService(prodDataMonthlyVerificationViewContainer));
            builder.Services.AddScoped(x => new ProdDataDailyValidationReportService(prodDataDailyValidationReportContainer));

            builder.Services.AddScoped(x => new ProdDataGasDailyService(mongoDBConnString, NameDB));
            builder.Services.AddScoped(x => new ProdDataGasMonthlyService(mongoDBConnString, NameDB));
            builder.Services.AddScoped(x => new ProdDataGasDailyViewService(prodDataGasDailyViewContainer));
            builder.Services.AddScoped(x => new ProdDataGasMonthlyViewService(prodDataGasMonthlyViewContainer));
            builder.Services.AddScoped(x => new ProdDataGasDailyRejectedService(prodDataGasDailyRejectedContainer));
            builder.Services.AddScoped(x => new ProdDataGasMonthlyRejectedService(prodDataGasMonthlyRejectedContainer));
            builder.Services.AddScoped(x => new ProdDataGasDailyVerificationViewService(prodDataGasDailyVerificationViewContainer));
            builder.Services.AddScoped(x => new ProdDataGasMonthlyVerificationViewService(prodDataGasMonthlyVerificationViewContainer));

            builder.Services.AddScoped(x => new ProdDataOnlineService(mongoDBConnString, NameDB));

            builder.Services.AddScoped<IRepositoryNoSql<ValidationRule>, ValidationRuleNoSqlService>(x => new ValidationRuleNoSqlService(mongoDBConnString, NameDB));
            builder.Services.AddScoped<IRepositoryNoSql<GasValidationRule>, GasValidationRuleNoSqlService>(x => new GasValidationRuleNoSqlService(mongoDBConnString, NameDB));

            builder.Services.AddScoped<IRepositoryNoSql<ProdDataInitialMetric>, ProdDataInitialMetricService>(x => new ProdDataInitialMetricService(mongoDBConnString, NameDB));
            builder.Services.AddScoped<IRepositoryNoSql<ProdForecastData>, ProdForecastDataService>(x => new ProdForecastDataService(mongoDBConnString, NameDB));
            builder.Services.AddScoped<IRepositoryNoSql<ProdAlngForecastData>, ProdAlngForecastDataService>(x => new ProdAlngForecastDataService(mongoDBConnString, NameDB));
            builder.Services.AddScoped<IRepositoryNoSql<ProdHomologousData>, ProdHomologousDataService>(x => new ProdHomologousDataService(mongoDBConnString, NameDB));

            //builder.Services.AddScoped(x => new DocumentService(mongoDBConnString, NameDB));
            builder.Services.AddScoped<DailyDashboardChartsService>();
            builder.Services.AddScoped<MonthlyDashboardChartsService>();
            builder.Services.AddScoped<IDocumentService, DocumentService>();
            builder.Services.AddScoped<IRepositoryNoSql<DocumentReferences>, DocumentReferencesService>(x => new DocumentReferencesService(mongoDBConnString, NameDB));
            builder.Services.AddScoped(x => new FileWebApiService(fileServiceBaseURL));

            builder.Services.AddAutoMapper(typeof(OPDMapper));
            //------

            builder.Services.AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                    options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
                });

            builder.Services.AddEndpointsApiExplorer();
            //builder.Services.AddSwaggerGen();

            //Health Checks
            builder.Services.AddHealthChecks();

            //Add SignalR Support:
            builder.Services.AddSignalR();
            //Register The IHostedService Background Service
            builder.Services.AddHostedService<ProdDataOnlineHubWorker>();

            var dbSeedHelper = new DBSeedHelper(mongoDBConnString, NameDB, ProdDataMongoDb);

            builder.Services.AddScoped(x => dbSeedHelper);

            var app = builder.Build();

            var swaggerUiEnabled = builder?.Configuration?.GetValue<bool>("SwaggerConfigs:SwaggerUi");
            var swaggerEnabled = swaggerUiEnabled != null && swaggerUiEnabled.Equals(true);

            if (swaggerEnabled)
            {
                //app.UseCoreSwaggerUi();
                var documentPath = basePath + "/swagger/{documentName}/swagger.json";
                app.UseOpenApi(options =>
                {
                    //options.Path = documentPath;
                    options.PostProcess = (document, request) =>
                    {
                        var serverUrl = $"https://{request.Host.Value}{(basePath)}";
                        document.Servers.Clear();
                        document.Servers.Add(new OpenApiServer { Url = serverUrl });
                    };
                });
                app.UseSwaggerUi(x =>
                {
                    x.DocumentPath = documentPath;
                    x.OAuth2Client = new OAuth2ClientSettings
                    {
                        ClientId = null,
                        ClientSecret = null,
                        AppName = null
                    };
                });
            }

            //------CREATE INDEXES AND VIEWS
            dbSeedHelper.seedValidationRuleCollection();
            dbSeedHelper.seedGasValidationRuleCollection();
            dbSeedHelper.seedProdDataInitialMetricCollection();
            dbSeedHelper.seedProdForecastDataCollection();
            dbSeedHelper.seedProdHomologousDataCollection();
            dbSeedHelper.seedProdAlngForecastDataCollection();
            dbSeedHelper.CreateDbIndexes(
                    prodDataDailyContainer,
                    prodDataMonthlyContainer,
                    prodDataDailyViewContainer,
                    prodDataMonthlyViewContainer,
                    prodDataDailyRejectedContainer,
                    prodDataMonthlyRejectedContainer,
                    prodDataDailyVerificationViewContainer,
                    prodDataMonthlyVerificationViewContainer,
                    prodDataGasDailyContainer,
                    prodDataGasMonthlyContainer,
                    prodDataGasDailyViewContainer,
                    prodDataGasMonthlyViewContainer,
                    prodDataGasDailyRejectedContainer,
                    prodDataGasMonthlyRejectedContainer,
                    prodDataGasDailyVerificationViewContainer,
                    prodDataGasMonthlyVerificationViewContainer,
                    prodDocumentReferencesContainer,
                    prodDataDailyValidationReportContainer,
                    prodDataOnlineContainer
                );
            dbSeedHelper.CreateProdDataDailyView();
            dbSeedHelper.CreateProdDataMonthlyView();
            dbSeedHelper.CreateProdDataDailyVerificationView();
            dbSeedHelper.CreateProdDataMonthlyVerificationView();
            dbSeedHelper.CreateProdDataGasDailyView();
            dbSeedHelper.CreateProdDataGasMonthlyView();
            dbSeedHelper.CreateProdDataGasDailyVerificationView();
            dbSeedHelper.CreateProdDataGasMonthlyVerificationView();

            //Enable HealthChecks and UI
            app.UseHealthChecks("/selfcheck", new HealthCheckOptions
            {
                Predicate = _ => true
            });

            bool useHttpsRedirection = Boolean.Parse(Environment.GetEnvironmentVariable("USE_HTTPS_REDIRECTION") ?? "true");
            if (useHttpsRedirection)
            {
                app.UseHttpsRedirection();
            }

            //app.UseAuthentication();
            //app.UseAuthorization();

            app.MapControllers();

            //app.MapHub<ProdDataOnlineHub>(Constants.ProdDataOnlineDashboardHub);
            app.MapHub<ProdDataNotificationsHub>(Constants.ProdDataNotificationsHub);

            app.Run();
        }
    }
}
