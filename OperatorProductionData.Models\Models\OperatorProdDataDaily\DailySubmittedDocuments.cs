﻿using OperatorProductionData.Models.Utilities;
using System;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailySubmittedDocuments
    {
        public Guid DocumentId { get; set; }
        public string DocumentTypeId { get; set; }
        public string Date { get; set; }
        public virtual DateTime? ParsedDate
        {
            set
            {
                _tempDate = DateUtils.ConvertDateTimeString(Date, "yyyy-MM-dd");
            }

            get
            {
                return _tempDate;
            }
        }

        private DateTime _tempDate;
        public string Ref { get; set; }
    }
}
