﻿using System;
using System.Collections.Generic;
using System.Text;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasMonthly
{
    public class MonthlyGasProductionClosingInventory
    {
        public string FacilityId { get; set; }
        public string GasTypeId { get; set; }
        public decimal GasProductionClosingInventoryMass { get; set; }
        public decimal GasProductionClosingInventoryEnergy_OFU { get; set; }
        public decimal GasProductionClosingInventoryEnergy_SI { get; set; }
        public decimal GasProductionClosingInventoryEnergy_BOE { get; set; }
        public decimal GasProductionClosingInventoryVolume_OFU { get; set; }
        public decimal GasProductionClosingInventoryVolume_SI { get; set; }

    }
}
