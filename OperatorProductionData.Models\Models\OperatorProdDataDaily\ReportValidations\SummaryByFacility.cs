﻿using OperatorProductionData.Models.Utilities;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{



    public class SummaryByFacilityForFacility : SummaryByFacility
    {
        public string FacilityId { get; set; }

    }

    public class SummaryByFacility
    {
        public ValidationResult<decimal> ExpectedOilProd_OFU { get; set; }
        public ValidationResult<decimal> ExpectedOilProd_SI { get; set; }
        public ValidationResult<decimal> ActualOilProd_OFU { get; set; }
        public ValidationResult<decimal> ActualOilProd_SI { get; set; }
        public ValidationResult<decimal> GasProd_OFU { get; set; }
        public ValidationResult<decimal> GasProd_SI { get; set; }
        public ValidationResult<decimal?> GasInjected_OFU { get; set; }
        public ValidationResult<decimal?> GasInjected_SI { get; set; }
        public ValidationResult<decimal> GasExported_OFU { get; set; }
        public ValidationResult<decimal> GasExported_SI { get; set; }
        public ValidationResult<decimal> GasFlared_OFU { get; set; }
        public ValidationResult<decimal> GasFlared_SI { get; set; }
        public ValidationResult<decimal?> GasImport_OFU { get; set; }
        public ValidationResult<decimal?> GasImport_SI { get; set; }
        public ValidationResult<decimal> GasFuel_OFU { get; set; }
        public ValidationResult<decimal> GasFuel_SI { get; set; }
        public ValidationResult<decimal> GasLift_OFU { get; set; }
        public ValidationResult<decimal> GasLift_SI { get; set; }
        public ValidationResult<decimal> WaterProd_OFU { get; set; }
        public ValidationResult<decimal> WaterProd_SI { get; set; }
        public ValidationResult<decimal?> WaterInjected_OFU { get; set; }
        public ValidationResult<decimal?> WaterInjected_SI { get; set; }
        public ValidationResult<decimal?> WaterDischarge_OFU { get; set; }
        public ValidationResult<decimal?> WaterDischarge_SI { get; set; }

        public ValidationResult<decimal?> PlantEfficiency { get; set; }
        public ValidationResult<decimal?> InstalledProdCapacity_OFU { get; set; }
        public ValidationResult<decimal?> InstalledProdCapacity_SI { get; set; }
    }
}


