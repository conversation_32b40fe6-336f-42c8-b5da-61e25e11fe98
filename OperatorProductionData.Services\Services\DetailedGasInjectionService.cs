﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class DetailedGasInjectionService : RepositoryBase<DetailedGasInjectionEntity, DetailedGasInjection>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public DetailedGasInjectionService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitDetailedGasInjection(DetailedGasInjection DetailedGasInjection)
        {
            _dbContext.DetailedGasInjection.Add(
                _mapper.Map<DetailedGasInjection, DetailedGasInjectionEntity>(DetailedGasInjection)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
