﻿namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyCommodityConsumptionSummary
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? FieldId { get; set; }
        public string? WellId { get; set; }
        public string CommodityTypeId { get; set; }
        public decimal CommodityConsumedQty { get; set; }
        public decimal CommodityStock { get; set; }
        public string CommodityUomId { get; set; }
        public string? CommodityBrand { get; set; }
        public string? CommodityManufacturer { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
