﻿using Ae.Stratus.Core.Common.GridDataLoad;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataGasMonthlyVerificationViewService : BaseRepository<ProdDataGasMonthlyVerificationView>
    {
        public ProdDataGasMonthlyVerificationViewService(IMongoCollection<ProdDataGasMonthlyVerificationView> container) : base(container)
        {
        }

        /// <summary>
        /// Method to get All ProdDataMonthly's for the given ProductionDate (Used in Daily Reports Methods)
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ICollection<ProdDataGasMonthlyVerificationView>> GetAllByProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            if (productionYearStart == productionYearEnd)
            {
                return await GetAllByYearProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
            }
            else
            {
                var prodDataMonthlyVerificationViewLilst = new List<ProdDataGasMonthlyVerificationView>();
                var referenceYear = productionYearStart;
                while (referenceYear != productionYearEnd)
                {
                    if (referenceYear == productionYearStart)
                    {
                        prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(productionMonthStart, 12, productionYearStart, referenceYear));
                    }
                    else
                    {
                        if (referenceYear == productionMonthEnd)
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, productionMonthEnd, referenceYear, productionYearEnd));
                        }
                        else
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, 12, referenceYear, referenceYear));
                        }
                    }

                    referenceYear++;
                }

                return prodDataMonthlyVerificationViewLilst;
            }
        }

        private async Task<ICollection<ProdDataGasMonthlyVerificationView>> GetAllByYearProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            var filteringOptions = new List<FilteringOptions>
                {
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "ProductionYear",
                        Value = productionYearStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "ProductionYear",
                        Value = productionYearEnd,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "ProductionMonth",
                        Value = productionMonthStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "ProductionMonth",
                        Value = productionMonthEnd,
                    }
                };

            /*var sortingOptions = new List<SortingOptions>
            {
                new SortingOptions
                {
                    PropertyName = "ProductionYear",
                    IsAscending = true
                },
                new SortingOptions
                {
                    PropertyName = "ProductionMonth",
                    IsAscending = true
                },
                new SortingOptions
                {
                    PropertyName = "ComplexId",
                    IsAscending = true
                }
            };*/

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions,
                //SortingOptions = sortingOptions,
                PageIndex = 0,
                PageSize = 0,
            };

            return (await GetSortedFilteredPagedList(options)).Models;
        }
    }
}