﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Notifications;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.SignalRHubs;
using OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasDaily;
using OperatorProductionData.WebApi.Enums;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataGasDailyController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataGasDailyService _service { get; set; }
        private ProdDataGasDailyRejectedService _rejectedService { get; set; }
        private GasValidationRuleCoreService _validationRuleService { get; set; }
        private ProdDataGasDailyViewService _viewService { get; set; }
        private ProdDataGasDailyVerificationViewService _verificationViewService { get; set; }
        private readonly IHubContext<ProdDataNotificationsHub> _prodDataGasDailyNotificationsHub;
        private IMapper _mapper { get; set; }

        public ProdDataGasDailyController(
            ILogger<ProdDataGasDailyController> logger,
            ProdDataGasDailyService service,
            ProdDataGasDailyRejectedService rejectedService,
            GasValidationRuleCoreService validationRuleService,
            IHubContext<ProdDataNotificationsHub> prodDataGasDailyNotificationsHub,
            ProdDataGasDailyViewService viewService,
            ProdDataGasDailyVerificationViewService verificationViewService,
            IMapper mapper
        )
        {
            _logger = logger;
            _service = service;
            _rejectedService = rejectedService;
            _validationRuleService = validationRuleService;
            _viewService = viewService;
            _verificationViewService = verificationViewService;
            _prodDataGasDailyNotificationsHub = prodDataGasDailyNotificationsHub;
            _mapper = mapper;

        }

        [HttpPost]
        [Route("SubmitProdDataGasDaily")]
        public async Task<IActionResult> SubmitProdDataGasDaily([FromBody] ReferenceDataProdDataGasDaily referenceDataProdDataGasDaily)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new ProdDataGasDailyValidator(EnumAction.Add, referenceDataProdDataGasDaily.GasReferenceData, _validationRuleService);
                var result = validator.Validate(referenceDataProdDataGasDaily.ProdDataGasDaily);
                if (!result.IsValid)
                {
                    apiResponse.Status = ApiResponseStatus.Error;
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );
                    //Register Rejected ProdDataGasDaily
                    var prodDataGasDailyRejected = _mapper.Map<ProdDataGasDaily, ProdDataGasDailyRejected>(referenceDataProdDataGasDaily.ProdDataGasDaily);
                    prodDataGasDailyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataGasDailyRejected);
                    await GenerateNotification(referenceDataProdDataGasDaily, referenceDataProdDataGasDaily.ProdDataGasDaily.ParsedProductionDate, NotificationStyle.Danger, referenceDataProdDataGasDaily.ProdDataGasDaily.ProductionDate, false);
                }
                else
                {
                    var newModel = await _service.Add(referenceDataProdDataGasDaily.ProdDataGasDaily);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    //Send OPD Daily SignalR notification
                    await GenerateNotification(referenceDataProdDataGasDaily, referenceDataProdDataGasDaily.ProdDataGasDaily.ParsedProductionDate, NotificationStyle.Success, referenceDataProdDataGasDaily.ProdDataGasDaily.ProductionDate, true);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem submitting Product Data Gas Daily.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPut]
        [Route("ChangeProdDataGasDaily")]
        public async Task<IActionResult> ChangeProdDataGasDaily([FromBody] ReferenceDataProdDataGasDaily referenceDataProdDataGasDaily)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new ProdDataGasDailyValidator(EnumAction.Update, referenceDataProdDataGasDaily.GasReferenceData, _validationRuleService);
                var result = validator.Validate(referenceDataProdDataGasDaily.ProdDataGasDaily);
                if (!result.IsValid)
                {
                    apiResponse.Status = ApiResponseStatus.Error;
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );
                    //Register Rejected ProdDataGasDaily
                    var prodDataGasDailyRejected = _mapper.Map<ProdDataGasDaily, ProdDataGasDailyRejected>(referenceDataProdDataGasDaily.ProdDataGasDaily);
                    prodDataGasDailyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataGasDailyRejected);
                    await GenerateNotification(referenceDataProdDataGasDaily, referenceDataProdDataGasDaily.ProdDataGasDaily.ParsedProductionDate, NotificationStyle.Danger, referenceDataProdDataGasDaily.ProdDataGasDaily.ProductionDate, false);
                }
                else
                {
                    var newModel = await _service.Update(referenceDataProdDataGasDaily.ProdDataGasDaily);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    await GenerateNotification(referenceDataProdDataGasDaily, referenceDataProdDataGasDaily.ProdDataGasDaily.ParsedProductionDate, NotificationStyle.Success, referenceDataProdDataGasDaily.ProdDataGasDaily.ProductionDate, true);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem updating Product Data Gas Daily.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataGasDaily Post - getlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasDaily>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasDaily>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasDaily>> apiResponse = res;
                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error getting sorted, filtered and paged list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDaily Post - getlist");
                return Ok(res);
            }
        }

        [HttpPost]
        [Route("getviewlist")]
        public async Task<IActionResult> GetViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataDailyView Post - getviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasDailyView>> apiResponse = res;

                /*options.SortingOptions = new List<SortingOptions> {
                    new SortingOptions
                    {
                        PropertyName = "ProductionDate",
                        IsAscending = false
                    }
                };*/

                apiResponse.Response = await _viewService.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDailyView - Error getting sorted, filtered and paged view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDailyView Post - getviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getviewdetailslist")]
        public async Task<IActionResult> GetViewListDetails()
        {
            _logger.LogInformation("Enter ProdDataGasDaily GET - getviewdetailslist");
            ApiResponse<ICollection<ProdDataGasDailyViewDetails>> res = new ApiResponse<ICollection<ProdDataGasDailyViewDetails>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {

                res.Response = await _viewService.GetAllViewDetails();

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error getting sorted, filtered and paged view details list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDaily GET - getviewdetailslist");
                return Ok(res);
            }
        }

        [HttpPost]
        [Route("getverificationviewlist")]
        public async Task<IActionResult> GetVerificationViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataGasDaily Post - getverificationviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>> apiResponse = res;

                options.SortingOptions = new List<SortingOptions> {
                    new SortingOptions
                    {
                        PropertyName = "ProductionDate",
                        IsAscending = false
                    }
                };

                apiResponse.Response = await _verificationViewService.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error getting sorted, filtered and paged verification view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDaily Post - getverificationviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getproddatagasdailyviewjson")]
        public async Task<IActionResult> GetProdDataGasDailyViewJson([FromQuery] Guid id)
        {
            _logger.LogInformation("Enter ProdDataGasDaily - GetProdDataGasDailyViewJson");
            ApiResponse<ProdDataGasDailyGeneric> res = new ApiResponse<ProdDataGasDailyGeneric>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataGasDailyGeneric> apiResponse = res;
                var json = await _viewService.GetJson(id);

                apiResponse.Response = json;

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error GetProdDataGasDailyViewJson");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDaily Get - GetProdDataGasDailyViewJson");
                return Ok(res);
            }
        }

        private async Task GenerateNotification(ReferenceDataProdDataGasDaily referenceDataProdDataGasDaily, DateTime productionDate, NotificationStyle style, string productionDateFilter, bool isValid)
        {
            try
            {
                await _service.UpdateProdDataGasDailyRejectedMaterializedView(productionDateFilter);

                if (isValid)
                {
                    await _service.UpdateProdDataGasDailyVerificationMaterializedView(productionDateFilter);
                }

                var methodName = "NotifyProdDataSubmission";
                await _prodDataGasDailyNotificationsHub.Clients.All.SendAsync(
                    methodName,
                    new ProdDataNotification
                    {
                        Type = "ProdDataGasDaily",
                        BlockId = referenceDataProdDataGasDaily.ProdDataGasDaily.ComplexId,
                        ProductionDate = productionDate,
                        Date = DateTime.Now,
                        NotificationStyle = style
                    });
            }
            catch (Exception ex)
            {
                //
            }
        }

        [HttpGet]
        [Route("getbycomplexandproductiondate")]
        public async Task<IActionResult> GetByComplexAndProductionDate([FromQuery] string complexId, [FromQuery] string productionDate)
        {
            _logger.LogInformation("Enter ProdDataGasDaily - GetByComplexAndProductionDate");
            ApiResponse<Guid?> res = new ApiResponse<Guid?>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<Guid?> apiResponse = res;
                apiResponse.Response = await _service.GetByComplexAndProductionDate(complexId, productionDate);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error GetByComplexAndProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDaily Get - GetByComplexAndProductionDate");
                return Ok(res);
            }
        }

    }
}
