﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class OperationSummaryByFacilityService : RepositoryBase<MonthlyOperationSummaryByFieldEntity, OperationSummaryByFacility>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public OperationSummaryByFacilityService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOperationSummaryByFacility(OperationSummaryByFacility OperationSummaryByFacility)
        {
            _dbContext.OperationSummaryByFacility.Add(
                _mapper.Map<OperationSummaryByFacility, MonthlyOperationSummaryByFacilityEntity>(OperationSummaryByFacility)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
