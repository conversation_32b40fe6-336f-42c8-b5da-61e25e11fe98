﻿using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasDaily
{

    public class DailyGasDeliveryValidator : AbstractValidator<DailyGasDelivery>
    {

        public DailyGasDeliveryValidator(string complexId, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPG-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr027 = validationRuleService.GetValidationRule("OPG-VR027");
            var vr031 = validationRuleService.GetValidationRule("OPG-VR031");


            var facilities = referenceData?.Complexes?.FirstOrDefault(x => x.Id == complexId)?.Facilities;

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Must(x => facilities.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr027.Message, complexId))
                    .WithErrorCode(vr027.Number.ToString())
                    .WithName(vr027.Type.ToString());



            RuleFor(x => x.DomesticGasDeliveryPointsId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).SelectMany(c => c.DomesticGasDeliveryPoints).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Custom((x, c) =>
                {
                    var domesticGasDeliveryPoints = facilities?.FirstOrDefault(y => y.Id == c.InstanceToValidate.FacilityId)?.DomesticGasDeliveryPoints;
                    if (domesticGasDeliveryPoints == null || !domesticGasDeliveryPoints.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr031.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr031.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => facilities.Any(y => y.Id == x.FacilityId), ApplyConditionTo.CurrentValidator);



            RuleFor(x => x.DomesticGasDeliveryVolume_OFU)
              .NotNull()
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());

            RuleFor(x => x.DomesticGasDeliveryVolume_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.DomesticGasDeliveryEnergy_OFU)
              .NotNull()
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());

            RuleFor(x => x.DomesticGasDeliveryEnergy_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.DomesticGasDeliveryEnergy_Boe)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.DomesticGasDeliveryMass)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.DomesticGasDeliveryGRHV)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

        }


    }


}