﻿using Ae.Stratus.Core.Backend.NoSQL.Base;
using Ae.Stratus.Core.Common.ValidationRules;

namespace OperatorProductionData.Services.Services
{
    public class ValidationRuleNoSqlService : RepositoryBaseMongoDB<ValidationRule>
    {
        public ValidationRuleNoSqlService(string connectionString, string databaseName) : base(connectionString, databaseName, "ValidationRule")
        {
        }

        public async Task<bool> AddBulk(List<ValidationRule> validationRules)
        {
            await _container.InsertManyAsync(validationRules);

            return true;
        }
    }
}
