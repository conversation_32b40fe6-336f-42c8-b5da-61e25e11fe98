﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class OperationSummaryByFieldService : RepositoryBase<MonthlyOperationSummaryByFieldEntity, OperationSummaryByField>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public OperationSummaryByFieldService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOperationSummaryByField(OperationSummaryByField OperationSummaryByField)
        {
            _dbContext.OperationSummaryByField.Add(
                _mapper.Map<OperationSummaryByField, MonthlyOperationSummaryByFieldEntity>(OperationSummaryByField)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
