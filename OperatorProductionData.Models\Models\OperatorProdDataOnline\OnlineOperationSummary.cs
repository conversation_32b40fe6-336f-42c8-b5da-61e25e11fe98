﻿using OperatorProductionData.Models.Utilities;
using System;

namespace OperatorProductionData.Models.Models.OperatorProdDataOnline
{
    public class OnlineOperationSummary
    {
        public string? ComplexId { get; set; }
        public string? FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? FieldId { get; set; }
        public string? ReservoirId { get; set; }
        public string? WellId { get; set; }
        public string Timestamp { get; set; }
        public virtual DateTime ParsedTimestamp
        {
            set
            {
                _tempTimestamp = DateUtils.ConvertDateTimeString(Timestamp, "yyyy-MM-dd HH:mm:ss", System.Globalization.DateTimeStyles.AssumeLocal);
            }

            get
            {
                return _tempTimestamp;
            }
        }

        private DateTime _tempTimestamp;

        public decimal OilProd_OFU { get; set; }
        public decimal OilProd_SI { get; set; }
        public decimal GasProd_OFU { get; set; }
        public decimal GasProd_SI { get; set; }
        public decimal WaterProd_OFU { get; set; }
        public decimal WaterProd_SI { get; set; }
        public string Comments { get; set; }
        public string CommentedBy { get; set; }
    }
}
