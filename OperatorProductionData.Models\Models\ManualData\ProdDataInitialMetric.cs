﻿using System;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.ManualData
{
    public class ProdDataInitialMetric : ManualData
    {
        [Key]
        public Guid Id { get; set; }
        public string BlockId { get; set; }
        public decimal ProducedWater { get; set; }
        public decimal WaterInjection { get; set; }
        public decimal ProducedGas { get; set; }
        public decimal GasInjection { get; set; }
        public decimal GasLift { get; set; }
        public decimal FuelGas { get; set; }
        public decimal FlareGas { get; set; }
        public decimal Metanol { get; set; }
        public decimal ALNGGasExport { get; set; }
    }
}