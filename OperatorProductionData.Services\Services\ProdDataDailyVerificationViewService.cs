﻿using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.Overview;
using OperatorProductionData.Services.Enums;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataDailyVerificationViewService : BaseRepository<ProdDataDailyVerificationView>
    {
        public ProdDataDailyVerificationViewService(IMongoCollection<ProdDataDailyVerificationView> container) : base(container)
        {
        }

        //public async Task<IEnumerable<DailyDashboardData>> GetDailyDashboardData(ICollection<string> blockIds)
        //{
        //    int amountDaysAgo = 2; //TODO :: the number of days should be parametrized
        //    DateTime today = DateTime.UtcNow;
        //    DateTime baseDate = today.AddDays(-amountDaysAgo);

        //    var returnList =
        //            (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
        //                                            && x.ParsedProductionDate >= baseDate.Date
        //                                            && x.ParsedProductionDate < today.Date
        //                                        //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
        //                                        )
        //            )
        //            .ToList()
        //        .GroupBy(x => new { x.BlockId, x.ParsedProductionDate })
        //        .Select(r => new DailyDashboardData
        //        {
        //            BlockId = r.Key.BlockId,
        //            Date = r.Key.ParsedProductionDate.Date,
        //            GasProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
        //            OilProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
        //            WaterProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
        //        }).ToList();

        //    this.FillMissingValues(blockIds, baseDate, returnList);

        //    return returnList.OrderBy(x => x.BlockId);
        //}

        //private void FillMissingValues(ICollection<string> blockIds, DateTime baseDate, List<DailyDashboardData> returnList)
        //{
        //    while (baseDate.Date < DateTime.UtcNow.Date)
        //    {
        //        var valuesList = blockIds.Select(blockId => new
        //        {
        //            BlockId = blockId,
        //            baseDate.Date,
        //        }).Except(
        //        returnList.Select(foundValue => new
        //        {
        //            foundValue.BlockId,
        //            foundValue.Date,
        //        })).ToList().Select(value => new DailyDashboardData
        //        {
        //            BlockId = value.BlockId,
        //            Date = value.Date,
        //        });

        //        returnList.AddRange(valuesList);
        //        baseDate = baseDate.AddDays(1);
        //    }
        //}

        /// <summary>
        /// Method to get Info for the Overview Page - Volumes Produced by Block OR DevAreas OR Fields
        /// </summary>
        /// <param name="blockIds"></param>
        /// <returns></returns>

        /// :::: TODO ::CHCK IF THIS METHOD BELOW IS NECESSARY ::::

        public async Task<IEnumerable<OverviewDailyData>> GetDailyOverviewData(
                DashboardOverviewDataTypes dashboardOverviewDataType,
                IEnumerable<string> blockIds,
                IEnumerable<string> oilGasFieldIds = null
        )
        {
            //Idea is for this method be 'generic' choosing the query to run, weather were talking about blocks, DevAreas or Fields

            int amountDaysAgo = 1; //TODO :: the number of days should be parametrized
            DateTime today = DateTime.UtcNow;
            DateTime baseDate = today.AddDays(-amountDaysAgo);
            var returnList = Enumerable.Empty<OverviewDailyData>();

            var query = _container.AsQueryable()
                            .Where(x => blockIds.Contains(x.BlockId)
                                    && x.ParsedProductionDate.Date.Equals(baseDate.Date)
                            //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                            )
                        .ToList();
            switch (dashboardOverviewDataType)
            {
                case DashboardOverviewDataTypes.Blocks:
                    returnList = query
                        .GroupBy(x => new { x.BlockId })
                        .Select(r => new OverviewDailyData
                        {
                            BlockId = r.Key.BlockId,
                            GasProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                            OilProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                            WaterProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.WaterProd_OFU))
                        })
                        .ToList();
                    break;
                case DashboardOverviewDataTypes.DevelopmentAreas:
                case DashboardOverviewDataTypes.Fields:

                    if (oilGasFieldIds == null)
                    {
                        throw new ArgumentNullException("OilGasFields cannot be null at this point");
                    }

                    returnList = query
                        .SelectMany(b => b.Json.OperationSummaryByField
                                            .Where(x => oilGasFieldIds.Contains(x.FieldId)))
                        .GroupBy(f => new { f.FieldId })
                        .Select(r => new OverviewDailyData
                        {
                            //BlockId = r.Key.BlockId,
                            OilGasFieldId = r.Key.FieldId,
                            GasProd = r.Sum(x => x.GasProd_OFU),
                            OilProd = r.Sum(x => x.ActualOilProd_OFU),
                            WaterProd = r.Sum(x => x.WaterProd_OFU),
                        }).ToList();
                    break;
            }

            return returnList;
        }

        /// <summary>
        /// Method to get All ProdDataDaily's for the given ProductionDate (Used in Daily Reports Methods)
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ICollection<ProdDataDailyVerificationView>> GetAllByProductionDateRange(
            string productionDateStart,
            string productionDateEnd,
            string[] blockIds = null
        )
        {
            var filterBuilder = Builders<ProdDataDailyVerificationView>.Filter;
            var filter = filterBuilder.Gte(x => x.ProductionDate, productionDateStart)
                & filterBuilder.Lte(x => x.ProductionDate, productionDateEnd);
            ;

            if (blockIds is not null && blockIds.Length > 0)
            {
                filter = filter & filterBuilder.In(x => x.BlockId, blockIds);
            }

            var prodDaily = await GetAll(filter);

            return [.. prodDaily
                    .OrderByDescending(x => x.ProductionDate)
                        .ThenBy(x => x.BlockId)];

        }

        /// <summary>
        /// Method to get check if there are no entries on OPD for the givem Blocks on a certain Date
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ICollection<string>> GetBlocksForGivenDateRange(
            string productionDateStart,
            string productionDateEnd
        )
        {
            var filterBuilder = Builders<ProdDataDailyVerificationView>.Filter;
            var filter = filterBuilder.Gte(x => x.ProductionDate, productionDateStart)
                & filterBuilder.Lte(x => x.ProductionDate, productionDateEnd);
            ;

            //var prodDaily = await GetAll(filter);
           return await _container.Find(filter)
                            .Project(x => x.BlockId)
                            .ToListAsync();
        }
    }
}