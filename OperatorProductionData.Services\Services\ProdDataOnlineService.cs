﻿using Ae.Stratus.Core.Backend.NoSQL.Base;
using OperatorProductionData.Models.Models.OperatorProdDataOnline;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Utilities;
using System.Globalization;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataOnlineService : RepositoryBaseMongoDB<ProdDataOnline>
    {
        private const string ClientTimeZoneId = "W. Central Africa Standard Time";
        private readonly TimeZoneInfo clientTimeZone;

        public ProdDataOnlineService(string connectionString, string databaseName) : base(connectionString, databaseName, "ProdDataOnline")
        {
            clientTimeZone = TimeZoneInfo.FindSystemTimeZoneById(ClientTimeZoneId);
        }

        public async Task<DateTime> GetBlockLastDate(string blockId)
        {
            var options = new FindOptions<ProdDataOnline, ProdDataOnline>
            {
                Limit = 1,
                Sort = Builders<ProdDataOnline>.Sort.Descending(o => o.ParsedDateTo),
            };

            ProdDataOnline result = (await _container.FindAsync(x => x.BlockId.Equals(blockId), options)).FirstOrDefault();
            return result == null ? DateTime.MinValue : result.ParsedDateTo;
        }

        public async Task<IEnumerable<OnlineDashboardDataAccumulated>> GetProdDataOnlineDashboardDataAccumulated(string productionDate, ICollection<string>? blockIds = null)
        {
            List<OnlineDashboardDataAccumulated> results = new();
            string clientTimeZoneId = "W. Central Africa Standard Time";

            DateTime dateParam = DateUtils.ConvertDateTimeString(productionDate, "yyyy-MM-dd", DateTimeStyles.AssumeLocal);
            DateTime dateFrom = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(new DateTime(new DateTime(dateParam.Year, dateParam.Month, dateParam.Day, 0, 0, 0).Ticks, DateTimeKind.Unspecified), clientTimeZoneId, TimeZoneInfo.Local.Id);
            DateTime dateTo = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(new DateTime(new DateTime(dateParam.Year, dateParam.Month, dateParam.Day, 23, 59, 59).Ticks, DateTimeKind.Unspecified), clientTimeZoneId, TimeZoneInfo.Local.Id);

            var prodOnlineData = (await _container.FindAsync(x => (blockIds == null || blockIds.Contains(x.BlockId)) && x.ParsedDateFrom >= dateFrom && x.ParsedDateTo <= dateTo)).ToList();

            foreach (ProdDataOnline currentRecord in prodOnlineData.OrderBy(x => x.DateFrom))
            {
                if (currentRecord.OperationSummary == null)
                    continue;

                foreach (OnlineOperationSummary currentRecordSummary in currentRecord.OperationSummary.OrderBy(x => x.Timestamp))
                {
                    OnlineDashboardDataAccumulated? resultElem = results.FirstOrDefault(x => x.BlockId == currentRecord.BlockId &&
                                                                                            (x.ComplexId ?? "") == (currentRecordSummary.ComplexId ?? "") &&
                                                                                            (x.FacilityId ?? "") == (currentRecordSummary.FacilityId ?? "") &&
                                                                                            (x.WellId ?? "") == (currentRecordSummary.WellId ?? ""));

                    if (resultElem != null)
                    {
                        if (!currentRecord.CumulatedValues!.Value)
                        {
                            resultElem.OilProd_OFU += currentRecordSummary.OilProd_OFU;
                            resultElem.OilProd_SI += currentRecordSummary.OilProd_SI;
                            resultElem.GasProd_OFU += currentRecordSummary.GasProd_OFU;
                            resultElem.GasProd_SI += currentRecordSummary.GasProd_SI;
                            resultElem.WaterProd_OFU += currentRecordSummary.WaterProd_OFU;
                            resultElem.WaterProd_SI += currentRecordSummary.WaterProd_SI;
                        }
                        else
                        {
                            resultElem.OilProd_OFU = currentRecordSummary.OilProd_OFU;
                            resultElem.OilProd_SI = currentRecordSummary.OilProd_SI;
                            resultElem.GasProd_OFU = currentRecordSummary.GasProd_OFU;
                            resultElem.GasProd_SI = currentRecordSummary.GasProd_SI;
                            resultElem.WaterProd_OFU = currentRecordSummary.WaterProd_OFU;
                            resultElem.WaterProd_SI = currentRecordSummary.WaterProd_SI;
                        }
                        resultElem.LastModifiedDate = currentRecordSummary.ParsedTimestamp;
                    }
                    else
                    {
                        OnlineDashboardDataAccumulated newResultElem = new()
                        {
                            BlockId = currentRecord.BlockId,
                            ComplexId = currentRecordSummary.ComplexId,
                            FacilityId = currentRecordSummary.FacilityId,
                            WellId = currentRecordSummary.WellId,
                            LastModifiedDate = currentRecordSummary.ParsedTimestamp,
                            OilProd_OFU = currentRecordSummary.OilProd_OFU,
                            OilProd_SI = currentRecordSummary.OilProd_SI,
                            GasProd_OFU = currentRecordSummary.GasProd_OFU,
                            GasProd_SI = currentRecordSummary.GasProd_SI,
                            WaterProd_OFU = currentRecordSummary.WaterProd_OFU,
                            WaterProd_SI = currentRecordSummary.WaterProd_SI
                        };
                        results.Add(newResultElem);
                    }
                }
            }

            return results;
        }

        public async Task<IEnumerable<OnlineDashboardData>> GetProdDataOnlineDashboardData(ICollection<string>? blockIds = null)
        {
            int amountSecondsAgo = -5; //TODO :: the number of seconds should be parametrized
            DateTime now = TimeZoneInfo.ConvertTime(DateTime.Now, clientTimeZone);
            DateTime nSecondsAgo = now.AddSeconds(amountSecondsAgo);

            var datDiff = Math.Round((now - DateTime.UtcNow).TotalHours);

            //var lastDayValues = new Dictionary<string, decimal>();

            var lastDayResultCumulated =
                (await _container.FindAsync(x => (blockIds == null || blockIds.Contains(x.BlockId))
                                                && x.ParsedDateFrom == (new DateTime(now.AddDays(-1).Year, now.AddDays(-1).Month, now.AddDays(-1).Day, 00, 00, 00)).AddHours(-datDiff)
                                                && x.ParsedDateTo == (new DateTime(now.AddDays(-1).Year, now.AddDays(-1).Month, now.AddDays(-1).Day, 23, 59, 59)).AddHours(-datDiff)
                                                //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                )
                ).ToList().GroupBy(x => new { x.BlockId }).Select(r => new OnlineDashboardData
                {
                    BlockId = r.Key.BlockId,
                    ReferenceDate = nSecondsAgo.Date.AddDays(-1),
                    Date = nSecondsAgo.Date.AddDays(-1).ToString("yyyy-MM-dd"),
                    TimeStart = "00:00:00",
                    TimeEnd = "23:59:59",
                    GasProd = r.Sum(x => x.OperationSummary.Sum(f => f.GasProd_OFU)),
                    OilProd = r.Sum(x => x.OperationSummary.Sum(f => f.OilProd_OFU)),
                    WaterProd = r.Sum(x => x.OperationSummary.Sum(f => f.WaterProd_OFU)),
                }).ToList();

            var lastDayResultCumulatedBlockIds = lastDayResultCumulated.Select(x => x.BlockId).ToList();

            var lastDayResultSum =
                    (await _container.FindAsync(x => (blockIds == null || blockIds.Contains(x.BlockId))
                                                    && !lastDayResultCumulatedBlockIds.Contains(x.BlockId)
                                                    && x.ParsedDateFrom.Date == nSecondsAgo.Date.AddDays(-1)
                                                    && x.ParsedDateTo.Date == nSecondsAgo.Date.AddDays(-1)
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList().GroupBy(x => new { x.BlockId }).Select(r => new OnlineDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        ReferenceDate = nSecondsAgo.Date.AddDays(-1),
                        Date = nSecondsAgo.Date.AddDays(-1).ToString("yyyy-MM-dd"),
                        TimeStart = "00:00:00",
                        TimeEnd = "23:59:59",
                        GasProd = r.Sum(x => x.OperationSummary.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummary.Sum(f => f.OilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummary.Sum(f => f.WaterProd_OFU)),
                    }).ToList();

            lastDayResultSum.AddRange(lastDayResultCumulated);

            var lastDayResult =
                    (await _container.FindAsync(x => (blockIds == null || blockIds.Contains(x.BlockId))
                                                    && x.ParsedDateFrom.Date == nSecondsAgo.Date.AddDays(-1)
                                                    && x.ParsedDateTo.Date == nSecondsAgo.Date.AddDays(-1)
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList().Select(x => new OnlineDashboardData
                    {
                        BlockId = x.BlockId,
                        Date = x.ParsedDateTo.ToString("yyyy-MM-dd"),
                        ReferenceDate = x.ParsedDateFrom,
                        TimeStart = x.ParsedDateFrom.ToString("HH:mm:ss"),
                        TimeEnd = x.ParsedDateTo.ToString("HH:mm:ss"),
                        ParsedTimeStart = x.ParsedDateFrom,
                        ParsedTimeEnd = x.ParsedDateTo,
                        OilProd = x.OperationSummary.Sum(f => f?.OilProd_OFU ?? 0),
                        GasProd = x.OperationSummary.Sum(f => f?.GasProd_OFU ?? 0),
                        WaterProd = x.OperationSummary.Sum(f => f?.WaterProd_OFU ?? 0),
                        CumulatedValues = x.CumulatedValues ?? false,
                    }).ToList();

            foreach (var item in lastDayResult.Where(x => x.CumulatedValues == false))
            {
                var listCount = lastDayResult.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Count();
                item.OilProd += listCount > 0 ? lastDayResult.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Max(x => x.OilProd) : 0;
                item.GasProd += listCount > 0 ? lastDayResult.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Max(x => x.GasProd) : 0;
                item.WaterProd += listCount > 0 ? lastDayResult.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Max(x => x.WaterProd) : 0;
                item.CumulatedValues = true;
            }

            //lastDayResult.ForEach(x => lastDayValues[x.BlockId] = x.OilProd);

            //Get Database Records
            var result =
                    (await _container.FindAsync(x => (blockIds.Contains(x.BlockId) || blockIds == null)
                                                    && x.ParsedDateFrom.Date >= nSecondsAgo.Date
                                                    && x.ParsedDateTo <= DateTime.UtcNow
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList().Select(x => new OnlineDashboardData
                    {
                        BlockId = x.BlockId,
                        Date = x.ParsedDateTo.ToString("yyyy-MM-dd"),
                        ReferenceDate = x.ParsedDateFrom,
                        TimeStart = x.ParsedDateFrom.ToString("HH:mm:ss"),
                        TimeEnd = x.ParsedDateTo.ToString("HH:mm:ss"),
                        ParsedTimeStart = x.ParsedDateFrom,
                        ParsedTimeEnd = x.ParsedDateTo,
                        OilProd = x.OperationSummary.Sum(f => f?.OilProd_OFU ?? 0),
                        LastDayOilProd = lastDayResultSum.FirstOrDefault(l => l.BlockId == x.BlockId)?.OilProd ?? 0,
                        GasProd = x.OperationSummary.Sum(f => f?.GasProd_OFU ?? 0),
                        LastDayGasProd = lastDayResultSum.FirstOrDefault(l => l.BlockId == x.BlockId)?.GasProd ?? 0,
                        WaterProd = x.OperationSummary.Sum(f => f?.WaterProd_OFU ?? 0),
                        LastDayWaterProd = lastDayResultSum.FirstOrDefault(l => l.BlockId == x.BlockId)?.WaterProd ?? 0,
                        CumulatedValues = x.CumulatedValues ?? false,
                    }).ToList();

            foreach (var item in result.Where(x => x.CumulatedValues == false))
            {
                var listCount = result.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Count();
                item.OilProd += listCount > 0 ? result.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Max(x => x.OilProd) : 0;
                item.GasProd += listCount > 0 ? result.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Max(x => x.GasProd) : 0;
                item.WaterProd += listCount > 0 ? result.Where(x => x.CumulatedValues == true && x.BlockId == item.BlockId && x.ParsedTimeStart <= item.ParsedTimeStart).Max(x => x.WaterProd) : 0;
                item.CumulatedValues = true;
            }

            if (lastDayResultSum.Count > 0)
                this.FillOnlineMissingValuesWithLastDay(lastDayResultSum, result);

            result.AddRange(lastDayResult);

            if (result.Count > 0 && blockIds != null)
                this.FillOnlineMissingValues(blockIds, result);

            //Work on the Summary Aggregation

            //TODO :: DELETE AFTER DEMO
            //var rand = new Random();
            //int i = 1;

            //var returnList = new List<OnlineDashboardData>();

            //returnList = blockIds.Select(blockId => new OnlineDashboardData
            //{
            //    BlockId = blockId,
            //    Date = now.ToString("yyyy-MM-dd"),
            //    TimeStart = nSecondsAgo.ToString("HH:mm:ss"),
            //    TimeEnd = now.ToString("HH:mm:ss"),
            //    OilProd = new decimal(i++) * 10 /*result.Where(x => x.BlockId.Equals(blockId))
            //                        .SelectMany(x => x.OperationSummary)
            //                            .Sum(f => f.OilProd_OFU)*/,
            //    GasProd = result.Where(x => x.BlockId.Equals(blockId))
            //                        .SelectMany(x => x.OperationSummary)
            //                            .Sum(f => f.GasProd_OFU),
            //    WaterProd = result.Where(x => x.BlockId.Equals(blockId))
            //                        .SelectMany(x => x.OperationSummary)
            //                            .Sum(f => f.WaterProd_OFU),
            //});

            //returnList.AddRange(r);
            //returnList.AddRange(this.MockTodayLastValues(blockIds));
            return result;
        }

        public List<OnlineDashboardData> MockTodayLastValues(ICollection<string> blockIds)
        {
            var rand = new Random();

            var production = new Dictionary<string, decimal>();
            var lastProduction = new Dictionary<string, decimal>();

            int i = 1;

            var list = new List<OnlineDashboardData>();

            foreach (var blockId in blockIds)
            {
                production[blockId] = 10 * i++;
                lastProduction[blockId] = 325 * ((i++ % 3) + 1);
            }


            var date = DateTime.Now.Date;
            while (date <= DateTime.Now.AddMinutes(-30))
            {
                foreach (var blockId in blockIds)
                {
                    list.Add(
                        new OnlineDashboardData
                        {
                            BlockId = blockId,
                            Date = date.ToString("yyyy-MM-dd"),
                            TimeStart = date.ToString("HH:mm:ss"),
                            TimeEnd = date.ToString("HH:mm:ss"),
                            OilProd = production[blockId],
                            LastDayOilProd = lastProduction[blockId]
                        });

                    if (date.Minute == 0)
                        production[blockId] = production[blockId] + (30 * (date.Hour % 3));
                    else
                        production[blockId] = production[blockId] + (15 * (date.Hour % 3));
                }

                date = date.AddMinutes(30);
            }

            foreach (var blockId in blockIds)
            {
                list.Add(
                    new OnlineDashboardData
                    {
                        BlockId = blockId,
                        Date = date.ToString("yyyy-MM-dd"),
                        TimeStart = DateTime.Now.ToString("HH:mm:ss"),
                        TimeEnd = DateTime.Now.ToString("HH:mm:ss"),
                        OilProd = production[blockId],
                        LastDayOilProd = lastProduction[blockId]
                    });

                if (date.Minute == 0)
                    production[blockId] = production[blockId] + (30 * (date.Hour % 3));
                else
                    production[blockId] = production[blockId] + (15 * (date.Hour % 3));
            }

            return list;
        }

        private void FillOnlineMissingValues(ICollection<string> blockIds, List<OnlineDashboardData> returnList)
        {
            var firstData = returnList.FirstOrDefault(x => x.TimeEnd == returnList.Min(x => x.TimeEnd));

            foreach (var blockId in blockIds)
            {
                if (!returnList.Any(b => b.BlockId == blockId))
                {
                    returnList.Add(new OnlineDashboardData
                    {
                        BlockId = blockId,
                        Date = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                        TimeStart = firstData?.TimeStart,
                        TimeEnd = firstData?.TimeEnd,
                        OilProd = 0,
                        LastDayOilProd = 0,
                        GasProd = 0,
                        LastDayGasProd = 0,
                        WaterProd = 0,
                        LastDayWaterProd = 0
                    });
                }
            };
        }

        private void FillOnlineMissingValuesWithLastDay(List<OnlineDashboardData> lastDayList, List<OnlineDashboardData> returnList)
        {
            var firstData = returnList.FirstOrDefault(x => x.TimeEnd == returnList.Min(x => x.TimeEnd));

            foreach (var value in lastDayList)
            {
                if (!returnList.Any(b => b.BlockId == value.BlockId))
                {
                    returnList.Add(new OnlineDashboardData
                    {
                        BlockId = value.BlockId,
                        Date = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                        ReferenceDate = value.ReferenceDate,
                        TimeStart = firstData?.TimeStart,
                        TimeEnd = firstData?.TimeEnd,
                        OilProd = 0,
                        LastDayOilProd = value.OilProd,
                        GasProd = 0,
                        LastDayGasProd = value.GasProd,
                        WaterProd = 0,
                        LastDayWaterProd = value.WaterProd
                    });
                }
            };
        }
    }
}
