﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class DetailedWaterInjectionService : RepositoryBase<DetailedWaterInjectionEntity, DetailedWaterInjection>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public DetailedWaterInjectionService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitDetailedWaterInjection(DetailedWaterInjection DetailedWaterInjection)
        {
            _dbContext.DetailedWaterInjection.Add(
                _mapper.Map<DetailedWaterInjection, DetailedWaterInjectionEntity>(DetailedWaterInjection)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
