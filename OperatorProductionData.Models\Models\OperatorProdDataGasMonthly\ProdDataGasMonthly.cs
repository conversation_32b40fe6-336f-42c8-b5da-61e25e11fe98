﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasMonthly
{

    public class ProdDataGasMonthly : IVersionable
    {
        [Key]
        public Guid Id { get; set; }
        public string ReasonId { get; set; }
        public string ReasonDescription { get; set; }
        public int ProductionMonth { get; set; }
        public int ProductionYear { get; set; }
        public string? OperatorReference { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
        public string ComplexId { get; set; }
        public ICollection<MonthlyGasProduction> MonthlyGasProduction { get; set; }        
        public ICollection<MonthlyGasShippingTotalByMonth> MonthlyGasShippingTotalByMonth { get; set; }
        public ICollection<MonthlyGasShippingTotal> MonthlyGasShippingTotal { get; set; }
        public ICollection<MonthlyGasSubmittedDocuments>? MonthlyGasSubmittedDocuments { get; set; }
        public ICollection<MonthlyGasProductionClosingInventory> MonthlyGasProductionClosingInventory { get; set; }
        public ICollection<MonthlyGasProductionAverageComposition> MonthlyGasProductionAverageComposition { get; set; }
        public ICollection<OperationGasForecastByDay> OperationGasForecastByDay { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public Guid VersionsCollectionId { get; set; }
        public int Version { get; set; }
    }
}







