﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly
{
    public class MonthlyOilStorageSummaryValidator : AbstractValidator<MonthlyOilStorageSummary>
    {

        public MonthlyOilStorageSummaryValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr016 = validationRuleService.GetValidationRule("OPD-VR016");
            var vr017 = validationRuleService.GetValidationRule("OPD-VR017");
            var vr019 = validationRuleService.GetValidationRule("OPD-VR019");
            var vr020 = validationRuleService.GetValidationRule("OPD-VR020");
            var vr021 = validationRuleService.GetValidationRule("OPD-VR021");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr088 = validationRuleService.GetValidationRule("OPD-VR088");
            var vr089 = validationRuleService.GetValidationRule("OPD-VR089");
            var vr090 = validationRuleService.GetValidationRule("OPD-VR090");
            var vr091 = validationRuleService.GetValidationRule("OPD-VR091");
            var vr092 = validationRuleService.GetValidationRule("OPD-VR092");

            var complexes = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes;
            var blends = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Blends;

            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => complexes.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr016.Message, blockId))
                    .WithErrorCode(vr016.Number.ToString())
                    .WithName(vr016.Type.ToString());

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    var facilities = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities;
                    if (facilities == null || !facilities.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr017.Message, c.InstanceToValidate.ComplexId),
                            ErrorCode = vr017.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.SystemId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var systems = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems.ToList();
                    if (!string.IsNullOrEmpty(x) && (systems != null && !systems.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr019.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr019.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                    if (string.IsNullOrEmpty(x) && !string.IsNullOrEmpty(c.InstanceToValidate.EquipmentId))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr020.Message,
                            ErrorCode = vr020.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    };
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId)), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.EquipmentId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var equipments = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems?.
                                                     FirstOrDefault(s => s.Id == c.InstanceToValidate.SystemId)?.Equipment.ToList();
                    if (!string.IsNullOrEmpty(x) && (equipments != null && !equipments.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr021.Message, c.InstanceToValidate.SystemId),
                            ErrorCode = vr021.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId && z.Systems.Any(s => s.Id == x.SystemId))), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.BlendId)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => blends != null && blends.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.InitialStock_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_SI == 0 || x.InitialStock_MT == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr089.Message)
                       .WithErrorCode(vr089.Number.ToString())
                       .WithName(vr089.Type.ToString());

            RuleFor(x => x.InitialStock_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                   .When(x => x.InitialStock_OFU == 0 || x.InitialStock_MT == 0, ApplyConditionTo.CurrentValidator)
                      .WithMessage(vr089.Message)
                      .WithErrorCode(vr089.Number.ToString())
                      .WithName(vr089.Type.ToString());

            RuleFor(x => x.InitialStock_MT)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_OFU == 0 || x.InitialStock_SI == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr089.Message)
                       .WithErrorCode(vr089.Number.ToString())
                       .WithName(vr089.Type.ToString());

            RuleFor(x => x.Production_OFU)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Equal(0)
                     .When(x => x.Production_SI == 0 || x.Production_MT == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr090.Message)
                        .WithErrorCode(vr090.Number.ToString())
                        .WithName(vr090.Type.ToString());

            RuleFor(x => x.Production_SI)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Equal(0)
                     .When(x => x.Production_OFU == 0 || x.Production_MT == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr090.Message)
                        .WithErrorCode(vr090.Number.ToString())
                        .WithName(vr090.Type.ToString());

            RuleFor(x => x.Production_MT)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Equal(0)
                     .When(x => x.Production_OFU == 0 || x.Production_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr090.Message)
                        .WithErrorCode(vr090.Number.ToString())
                        .WithName(vr090.Type.ToString());

            RuleFor(x => x.Liftings_OFU)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                 .Equal(0)
                     .When(x => x.Liftings_SI == 0 || x.Liftings_MT == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr091.Message)
                        .WithErrorCode(vr091.Number.ToString())
                        .WithName(vr091.Type.ToString());

            RuleFor(x => x.Liftings_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.Liftings_OFU == 0 || x.Liftings_MT == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr091.Message)
                        .WithErrorCode(vr091.Number.ToString())
                        .WithName(vr091.Type.ToString());

            RuleFor(x => x.Liftings_MT)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.Liftings_OFU == 0 || x.Liftings_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr091.Message)
                        .WithErrorCode(vr091.Number.ToString())
                        .WithName(vr091.Type.ToString());

            RuleFor(x => x.FinalStock_OFU)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Equal(0)
                    .When(x => x.FinalStock_SI == 0 || x.FinalStock_MT == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr092.Message)
                       .WithErrorCode(vr092.Number.ToString())
                       .WithName(vr092.Type.ToString());

            RuleFor(x => x.FinalStock_SI)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Equal(0)
                    .When(x => x.FinalStock_OFU == 0 || x.FinalStock_MT == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr092.Message)
                       .WithErrorCode(vr092.Number.ToString())
                       .WithName(vr092.Type.ToString());

            RuleFor(x => x.FinalStock_MT)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.FinalStock_OFU == 0 || x.FinalStock_SI == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr092.Message)
                       .WithErrorCode(vr092.Number.ToString())
                       .WithName(vr092.Type.ToString());

            RuleFor(x => x.Comments)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                        .WithMessage(vr023.Message)
                        .WithErrorCode(vr023.Number.ToString())
                        .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.Comments))
                        .WithMessage(vr024.Message)
                        .WithErrorCode(vr024.Number.ToString())
                        .WithName(vr024.Type.ToString());
        }
    }
}
