﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyOilStorageByPartner
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string BlendId { get; set; }
        public string ProductId { get; set; }
        public string ParticipantNif { get; set; }
        public decimal EquityShare { get; set; }
        public decimal LiftingPercentage { get; set; }
        public decimal InitialStock_CO_OFU { get; set; }
        public decimal InitialStock_CO_SI { get; set; }
        public decimal InitialStock_PO_OFU { get; set; }
        public decimal InitialStock_PO_SI { get; set; }
        public decimal Production_CO_OFU { get; set; }
        public decimal Production_CO_SI { get; set; }
        public decimal Production_PO_OFU { get; set; }
        public decimal Production_PO_SI { get; set; }
        public decimal Liftings_CO_OFU { get; set; }
        public decimal Liftings_CO_SI { get; set; }
        public decimal Liftings_PO_OFU { get; set; }
        public decimal Liftings_PO_SI { get; set; }
        public decimal FinalStock_CO_OFU { get; set; }
        public decimal FinalStock_CO_SI { get; set; }
        public decimal FinalStock_PO_OFU { get; set; }
        public decimal FinalStock_PO_SI { get; set; }
        public decimal QualityBankAdjustments_OFU { get; set; }
        public decimal QualityBankAdjustments_SI { get; set; }
        public decimal ComplexRefineryAdjustments_OFU { get; set; }
        public decimal ComplexRefineryAdjustments_SI { get; set; }
        public decimal ExternalRefineryAdjustments_OFU { get; set; }
        public decimal ExternalRefineryAdjustments_SI { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
