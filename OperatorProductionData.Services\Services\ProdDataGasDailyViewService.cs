﻿using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataGasDailyViewService : BaseRepository<ProdDataGasDailyView>
    {
        public ProdDataGasDailyViewService(IMongoCollection<ProdDataGasDailyView> container) : base(container)
        {
        }

        public async Task<ProdDataGasDailyGeneric?> GetJson(Guid id)
        {
            List<ProdDataGasDailyView> returnList =
                    (await _container.FindAsync(x => x.Id == id
                                                )
                    )
                    .ToList();

            return returnList.FirstOrDefault()?.Json;
        }

        public async Task<ICollection<ProdDataGasDailyViewDetails>> GetAllViewDetails()
        {
            var filterBuilder = Builders<ProdDataGasDailyView>.Filter;
            var filterDefinition = filterBuilder.Empty;

            var list = _container.Find(filterDefinition)
                .Project(x => new ProdDataGasDailyViewDetails
                {
                    ComplexId = x.ComplexId,
                    ProductionDate = x.ProductionDate,
                    CreatedDate = x.CreatedDate,
                    ErrorCount = x.ErrorCount,
                    ErrorCountStr = x.ErrorCount > 0 ? x.ErrorCount.ToString() : string.Empty,
                    Status = x.ErrorCount > 0 ? "Recebido / Não Aceite" : "Recebido",
                    Version = x.Version,
                    OperatorReference = x.Json.OperatorReference,
                    ParsedProductionDate = x.Json.ParsedProductionDate,
                    Id = x.Id
                })
                .ToList();

            return list;
        }
    }
}
