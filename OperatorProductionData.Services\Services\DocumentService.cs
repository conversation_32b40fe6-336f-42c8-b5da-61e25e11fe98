﻿using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Ae.Stratus.Services.FileRepository.WebApiServicesV6;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.Documents;
using OperatorProductionData.Services.Interfaces;

namespace OperatorProductionData.Services.Services
{
    public class DocumentService : IDocumentService
    {
        private const string OriginSystem = "OPD";
        private readonly FileWebApiService _fileWebApiService;
        private readonly IRepositoryNoSql<DocumentReferences> _documentReferencesRepository;
        private readonly IMapper _mapper;


        public DocumentService(FileWebApiService fileWebApiService, IRepositoryNoSql<DocumentReferences> documentReferencesRepository, IMapper mapper)
        {
            _fileWebApiService = fileWebApiService;
            _documentReferencesRepository = documentReferencesRepository;
            _mapper = mapper;
        }

        public async Task<Guid> AddDocumentByFileAsync(Document document)
        {
            var documentReferences = _mapper.Map<DocumentReferences>(document);

            var apiResponse = await _fileWebApiService.SubmitFile(OriginSystem, document.Filename, document.MimeType, new MemoryStream(Convert.FromBase64String(document.DocumentData)));

            if (documentReferences != null && apiResponse?.Response != null)
            {
                documentReferences.FileRepositoryDocumentId = apiResponse.Response.Id;
                documentReferences.Created = DateTime.UtcNow;
                await _documentReferencesRepository.Add(documentReferences);
            }

            return document.Id;
        }

        public async Task<FileContentResult?> GetDocumentAsync(Guid id, string companyCode = "")
        {
            var documentReferences = await GetDocumentReferencesAsync(id, companyCode);

            if (documentReferences != null)
            {
                var apiResponse = await _fileWebApiService.GetFileDetails(documentReferences.FileRepositoryDocumentId);

                if (apiResponse?.Response != null && apiResponse.Status == ApiResponseStatus.Success)
                {
                    return await _fileWebApiService.GetFile(apiResponse.Response.Id);
                }
                else
                {
                    return null;
                }
            }

            return null;
        }

        public async Task<DocumentDetails?> GetDocumentDetailsAsync(Guid id, string companyCode = "")
        {
            var documentReferences = await GetDocumentReferencesAsync(id, companyCode);

            if (documentReferences != null)
            {
                var apiResponse = await _fileWebApiService.GetFileDetails(documentReferences.FileRepositoryDocumentId);

                if (apiResponse?.Response != null && apiResponse.Status == ApiResponseStatus.Success)
                {
                    var documentDetails = _mapper.Map<DocumentDetails>(apiResponse.Response);
                    documentDetails.Id = id;
                    return documentDetails;
                }
                else
                {
                    return null;
                }
            }

            return null;
        }

        public async Task<List<DocumentDetails>> GetDocumentsDetailsAsync(List<Guid> ids, string companyCode = "")
        {
            var documentsReferences = new List<DocumentReferences>();
            var documentsDetails = new List<DocumentDetails>();

            if (ids == null && ids.Count > 0)
            {
                foreach (var documentReferencesId in ids)
                {
                    var documentReferences = await GetDocumentReferencesAsync(documentReferencesId, companyCode);
                    if (documentReferences != null)
                    {
                        documentsReferences.Add(documentReferences);
                    }
                }
            }
            else
            {
                documentsReferences = await GetDocumentReferencesByCompanyAsync(companyCode);
            }

            if (documentsReferences.Count > 0)
            {
                var apiResponse = await _fileWebApiService.GetFilesDetails(documentsReferences.Select(dr => dr.FileRepositoryDocumentId).ToList());

                if (apiResponse?.Response != null && apiResponse.Status == ApiResponseStatus.Success)
                {
                    foreach (var fileDetails in apiResponse.Response)
                    {
                        var documentReferences = documentsReferences.First(dr => dr.FileRepositoryDocumentId == fileDetails.Id);
                        var documentDetails = _mapper.Map<DocumentDetails>(fileDetails);
                        documentDetails.Id = documentsReferences.First(dr => dr.FileRepositoryDocumentId == fileDetails.Id).CompanyDocumentId;
                        documentsDetails.Add(documentDetails);
                    }
                }
            }

            return documentsDetails;
        }

        private async Task<DocumentReferences?> GetDocumentReferencesAsync(Guid id, string companyCode = "")
        {
            var filteringOptions = new List<FilteringOptions>
                {
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "CompanyDocumentId",
                        Value = id,
                    }
                };

            if (!string.IsNullOrEmpty(companyCode) && !string.Equals(companyCode, "anpg"))
            {
                filteringOptions.Add
                    (new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "CompanyCode",
                        Value = companyCode,
                    });
            }

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions
            };


            var documentReference = await _documentReferencesRepository.GetSortedFilteredPagedList(options);

            if (documentReference != null && documentReference.Models.Count() > 0)
            {
                return documentReference.Models.OrderByDescending(dr => dr.Created).First();
            }

            return null;
        }

        private async Task<List<DocumentReferences>?> GetDocumentReferencesByCompanyAsync(string companyCode = "")
        {
            var filteringOptions = new List<FilteringOptions>();

            if (!string.IsNullOrEmpty(companyCode))
            {
                filteringOptions.Add
                    (new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "CompanyCode",
                        Value = companyCode,
                    });
            }

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions
            };


            var documentReference = await _documentReferencesRepository.GetSortedFilteredPagedList(options);

            if (documentReference != null && documentReference.Models.Count() > 0)
            {
                return documentReference
                    .Models
                    .GroupBy(d => d.CompanyDocumentId)
                    .Select(d => d.OrderByDescending(x => x.Created).First()).ToList();
            }

            return null;
        }
    }
}
