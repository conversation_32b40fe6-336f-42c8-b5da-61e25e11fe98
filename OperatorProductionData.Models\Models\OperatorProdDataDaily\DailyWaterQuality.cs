﻿namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyWaterQuality
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? FieldId { get; set; }
        public string? WellId { get; set; }
        public decimal OilInWater { get; set; }
        public decimal OilInWaterPPM { get; set; }
        public decimal MonthlyAvgOilInWater { get; set; }
        public decimal MonthlyAvgOilInWaterPPM { get; set; }
        public decimal Chlorine { get; set; }
        public decimal Sulphate { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
