using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.ManualData;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Utilities;

namespace OperatorProductionData.Services.Services.DashboardServices.Daily
{
    //Purpose: 'Orchestrate' the requests to the various services to gather organized production data for the Dashboards

    public class DailyDashboardChartsService //: BaseRepository<ProdDataDaily>
    {
        private const decimal OFU_SI_CONVERSION = 158.987294928M;

        private ProdDataDailyVerificationViewService _prodDataDailyVerificationViewService { get; set; }
        private ProdForecastDataService _prodForecastDataService;
        private ProdAlngForecastDataService _prodAlngForecastDataService;

        public DailyDashboardChartsService(
            //IMongoCollection<ProdDataDailyVerificationView> ProdDataDailyVerificationViewContainer

            ProdDataDailyVerificationViewService prodDataDailyVerificationViewService,
            IRepositoryNoSql<ProdForecastData> prodForecastDataService,
            IRepositoryNoSql<ProdAlngForecastData> prodAlngForecastDataService
        ) // : base(container)
        {
            _prodDataDailyVerificationViewService = prodDataDailyVerificationViewService;
            _prodForecastDataService = (ProdForecastDataService)prodForecastDataService;
            _prodAlngForecastDataService = (ProdAlngForecastDataService)prodAlngForecastDataService;
        }

        public async Task<IEnumerable<ProductionDashboardDto>> GetDailyProductionDashboardCharts(
            string productionDateStart,
            string productionDateEnd,
            string[] blockIds
        )
        {
            //var filterBuilder = Builders<ProdDataDaily>.Filter;
            //var filter = filterBuilder.Gte(x => x.ProductionDate, productionDateStart)
            //    & filterBuilder.Lte(x => x.ProductionDate, productionDateEnd);
            var parsedDateStart = DateUtils.ConvertDateTimeString(productionDateStart, "yyyy-MM-dd");
            var parsedDateEnd = DateUtils.ConvertDateTimeString(productionDateEnd, "yyyy-MM-dd");

            var dateDiff = parsedDateEnd - parsedDateStart;

            var prodDataList = await this.GetAllDailyInfoByProductionDateRange(productionDateStart, productionDateEnd, blockIds);
            var prodLastValueDataList = await this.GetAllDailyInfoByProductionDateRange(parsedDateStart.AddMonths(-1), parsedDateEnd.AddMonths(-1), blockIds);
            var prodHomologousDataList = await this.GetAllDailyInfoByProductionDateRange(parsedDateStart.AddYears(-1), parsedDateEnd.AddYears(-1), blockIds);
            var prodAlngForecastDataList = await this.GetAllAlngForecastDataByProductionDateRange(parsedDateStart, parsedDateEnd, blockIds);
            var prodForecastDataList = await this.GetAllForecastDataByProductionDateRange(parsedDateStart, parsedDateEnd, blockIds);

            var result = new List<ProductionDashboardDto>();

            foreach (var blockGroup in prodDataList.GroupBy(x => new { x.BlockId, x.ProductionDate, x.ParsedProductionDate }))
            {
                var blockData = blockGroup.SelectMany(x => x.Json.OperationSummaryByFacility).ToList();
                var fieldData = blockGroup.SelectMany(x => x.Json.OperationSummaryByField).ToList();
                var lastValueGroupDataList = prodLastValueDataList.Where(x => x.ProductionDate == blockGroup.Key.ProductionDate && x.BlockId == blockGroup.Key.BlockId)?.SelectMany(x => x.Json.OperationSummaryByFacility)?.ToList() ?? new List<DailyOperationSummaryByFacility>();
                var homologousGroupDataList = prodHomologousDataList.Where(x => x.ProductionDate == blockGroup.Key.ProductionDate && x.BlockId == blockGroup.Key.BlockId)?.SelectMany(x => x.Json.OperationSummaryByFacility)?.ToList() ?? new List<DailyOperationSummaryByFacility>();
                var prodAlngForecastGroupDataList = prodAlngForecastDataList.Where(x => x.Month == blockGroup.Key.ParsedProductionDate.Month && x.Year == blockGroup.Key.ParsedProductionDate.Year && x.BlockId == blockGroup.Key.BlockId)?.ToList() ?? new List<ProdAlngForecastData>();
                var prodForecastGroupDataList = prodForecastDataList.Where(x => x.Month == blockGroup.Key.ParsedProductionDate.Month && x.Year == blockGroup.Key.ParsedProductionDate.Year && x.BlockId == blockGroup.Key.BlockId)?.ToList() ?? new List<ProdForecastData>();
                var daysOfMonth = (new DateTime(blockGroup.Key.ParsedProductionDate.Year, blockGroup.Key.ParsedProductionDate.Month, 1).AddMonths(1).AddDays(-1)).Day;
                var chartDto = new ProductionDashboardDto
                {
                    BlockId = blockGroup.Key.BlockId,
                    ProductionDate = blockGroup.Key.ProductionDate,



                    // By Complex aggregations
                    ProductionByComplex_SI = CalculateProductionByComplex(blockData, "SI"),
                    ProductionByComplex_OFU = CalculateProductionByComplex(blockData, "OFU"),

                    // By Facility aggregations
                    ProductionByFacility_SI = CalculateProductionByFacility(blockData, "SI"),
                    ProductionByFacility_OFU = CalculateProductionByFacility(blockData, "OFU"),

                    // By Field aggregations
                    ProductionByField_SI = CalculateProductionByField(fieldData, "SI"),
                    ProductionByField_OFU = CalculateProductionByField(fieldData, "OFU"),

                    // Block totals
                    BlockTotals_SI = CalculateBlockTotals(blockData, lastValueGroupDataList, homologousGroupDataList, prodAlngForecastGroupDataList, prodForecastGroupDataList, daysOfMonth, dateDiff.Days, "SI"),
                    BlockTotals_OFU = CalculateBlockTotals(blockData, lastValueGroupDataList, homologousGroupDataList, prodAlngForecastGroupDataList, prodForecastGroupDataList, daysOfMonth, dateDiff.Days, "OFU"),

                    // Operator totals
                    //OperatorTotals_SI = CalculateOperatorTotals(blockData, "SI"),
                    //OperatorTotals_OFU = CalculateOperatorTotals(blockData, "OFU"),
                    //OperatorTotals = new TotalsDto(),

                    // Additional metrics
                    ProductionLossMetrics = CalculateProductionLossMetrics(
                        blockGroup.SelectMany(x => x.Json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()),
                        blockGroup.SelectMany(x => x.Json.ProductionLoss ?? new List<DailyProductionLoss>())
                    ),

                    WaterQualityMetrics = CalculateWaterQualityMetrics(blockGroup.SelectMany(x => x.Json.WaterQuality)),
                    //EquipmentStatusMetrics = CalculateEquipmentStatusMetrics(blockGroup.SelectMany(x => x.EquipmentStatus ?? new List<DailyEquipmentStatus>())),

                    ProductionLossValues = this.GetLossValues(blockGroup.Select(x => x.Json)),

                    ProductionEficiency = GetEficiency(blockGroup.Select(x => x.Json).ToList()),

                    OilExported_OFU = blockGroup.Sum(b => (b.Json.OilStorageSummary ?? new List<DailyOilStorageSummary>()).Sum(f => f.Liftings_OFU)),
                    OilExported_SI = blockGroup.Sum(b => (b.Json.OilStorageSummary ?? new List<DailyOilStorageSummary>()).Sum(f => f.Liftings_SI)),

                    CreatedDate = DateTime.UtcNow
                };

                result.Add(chartDto);
            }

            return result;
        }

        public async Task<ProductionLossDto> GetLossValuesAsync(string productionDateStart, string productionDateEnd, string[] blockIds)
        {
            var prodDataDailies = await this.GetAllDailyInfoByProductionDateRange(productionDateStart, productionDateEnd, blockIds);

            return this.GetLossValues(prodDataDailies.Select(x => x.Json));
        }

        private async Task<ICollection<ProdDataDailyVerificationView>> GetAllDailyInfoByProductionDateRange(string productionDateStart, string productionDateEnd, string[] blockIds)
        {
            return await _prodDataDailyVerificationViewService.GetAllByProductionDateRange(
                            productionDateStart,
                            productionDateEnd,
                            blockIds
                        );
            //await GetAll(filter);
        }

        private async Task<ICollection<ProdDataDailyVerificationView>> GetAllDailyInfoByProductionDateRange(DateTime productionDateStart, DateTime productionDateEnd, string[] blockIds)
        {
            return await _prodDataDailyVerificationViewService.GetAllByProductionDateRange(
                            productionDateStart.ToString("yyyy-MM-dd"),
                            productionDateEnd.ToString("yyyy-MM-dd"),
                            blockIds
                        );
            //await GetAll(filter);
        }

        private async Task<ICollection<ProdForecastData>> GetAllForecastDataByProductionDateRange(DateTime productionDateStart, DateTime productionDateEnd, string[] blockIds)
        {
            var res = await _prodForecastDataService.GetAllByProductionDateRange(
                            productionDateStart.Month,
                            productionDateEnd.Month,
                            productionDateStart.Year,
                            productionDateEnd.Year
                        );
            return res.Where(r => blockIds.Contains(r.BlockId))?.ToList() ?? new List<ProdForecastData>();
            //await GetAll(filter);
        }

        private async Task<ICollection<ProdAlngForecastData>> GetAllAlngForecastDataByProductionDateRange(DateTime productionDateStart, DateTime productionDateEnd, string[] blockIds)
        {
            var res = await _prodAlngForecastDataService.GetAllByProductionDateRange(
                            productionDateStart.Month,
                            productionDateEnd.Month,
                            productionDateStart.Year,
                            productionDateEnd.Year
                        );

            return res.Where(r => blockIds.Contains(r.BlockId))?.ToList() ?? new List<ProdAlngForecastData>();
            //await GetAll(filter);
        }

        private ProductionLossDto GetLossValues(IEnumerable<ProdDataDaily> dailiesJson)
        {
            var productionLossDto = new ProductionLossDto();

            foreach (var json in dailiesJson)
            {
                if (json.ProductionLoss != null && json.ProductionLoss.Count > 0)
                {
                    productionLossDto.TotalLoss_OFU += json.ProductionLoss.Sum(x => x.OilLoss_OFU);
                    productionLossDto.TotalLoss_SI += json.ProductionLoss.Sum(x => x.OilLoss_SI);
                    productionLossDto.TotalUnplannedLoss_OFU += json.ProductionLoss.Where(l => l.LossMotiveId.Contains("TL_NPL_")).Sum(x => x.OilLoss_OFU);
                    productionLossDto.TotalUnplannedLoss_SI += json.ProductionLoss.Where(l => l.LossMotiveId.Contains("TL_NPL_")).Sum(x => x.OilLoss_SI);
                    productionLossDto.TotalPlannedLoss_OFU += json.ProductionLoss.Where(l => !l.LossMotiveId.Contains("TL_NPL_")).Sum(x => x.OilLoss_OFU);
                    productionLossDto.TotalPlannedLoss_SI += json.ProductionLoss.Where(l => !l.LossMotiveId.Contains("TL_NPL_")).Sum(x => x.OilLoss_SI);
                }
                else
                {
                    productionLossDto.TotalLoss_OFU += (json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()).Sum(x => x.UnplannedOilLoss_OFU + x.PlannedOilLoss_OFU);
                    productionLossDto.TotalLoss_SI += (json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()).Sum(x => x.UnplannedOilLoss_SI + x.PlannedOilLoss_SI);
                    productionLossDto.TotalUnplannedLoss_OFU += (json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()).Sum(x => x.UnplannedOilLoss_OFU);
                    productionLossDto.TotalUnplannedLoss_SI += (json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()).Sum(x => x.UnplannedOilLoss_SI);
                    productionLossDto.TotalPlannedLoss_OFU += (json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()).Sum(x => x.PlannedOilLoss_OFU);
                    productionLossDto.TotalPlannedLoss_SI += (json.ProductionLossSummary ?? new List<DailyProductionLossSummary>()).Sum(x => x.PlannedOilLoss_SI);
                }
            }

            return productionLossDto;
        }

        private List<ProductionByComplexDto> CalculateProductionByComplex(List<DailyOperationSummaryByFacility> facilities, string unitMeasure)
        {
            return [.. facilities.GroupBy(f => f.ComplexId)
                .Select(g => new ProductionByComplexDto
                {
                    ComplexId = g.Key,
                    OilProduction = unitMeasure == "SI" ? g.Sum(x => x.ActualOilProd_SI) : g.Sum(x => x.ActualOilProd_OFU),
                    ExpectedOilProduction = unitMeasure == "SI" ? g.Sum(x => x.ExpectedOilProd_SI) : g.Sum(x => x.ExpectedOilProd_OFU),
                    GasProduction = unitMeasure == "SI" ? g.Sum(x => x.GasProd_SI) : g.Sum(x => x.GasProd_OFU),
                    WaterProduction = unitMeasure == "SI" ? g.Sum(x => x.WaterProd_SI) : g.Sum(x => x.WaterProd_OFU),
                    GasInjection = unitMeasure == "SI" ? g.Sum(x => x.GasInjected_SI ?? 0) : g.Sum(x => x.GasInjected_OFU ?? 0),
                    WaterInjection = unitMeasure == "SI" ? g.Sum(x => x.WaterInjected_SI ?? 0) : g.Sum(x => x.WaterInjected_OFU ?? 0),
                    GasExported = unitMeasure == "SI" ? g.Sum(x => x.GasExported_SI) : g.Sum(x => x.GasExported_OFU),
                    GasFlared = unitMeasure == "SI" ? g.Sum(x => x.GasFlared_SI) : g.Sum(x => x.GasFlared_OFU),
                    GasFuel = unitMeasure == "SI" ? g.Sum(x => x.GasFuel_SI) : g.Sum(x => x.GasFuel_OFU),
                    GasLift = unitMeasure == "SI" ? g.Sum(x => x.GasLift_SI) : g.Sum(x => x.GasLift_OFU),
                    WaterCut = g.Sum(x => x.WaterCut),
                    BasicSedimentWater = g.Sum(x => x.BasicSedimentWater),
                    VoidageReplacementRatio = g.Sum(x => x.VoidageReplacementRatio),
                    UnitMeasure = unitMeasure
                })];
        }

        private List<ProductionByFacilityDto> CalculateProductionByFacility(List<DailyOperationSummaryByFacility> facilities, string unitMeasure)
        {
            return [.. facilities.Select(f => new ProductionByFacilityDto
            {
                FacilityId = f.FacilityId,
                ComplexId = f.ComplexId,
                OilProduction = unitMeasure == "SI" ? f.ActualOilProd_SI : f.ActualOilProd_OFU,
                ExpectedOilProduction = unitMeasure == "SI" ? f.ExpectedOilProd_SI : f.ExpectedOilProd_SI,
                GasProduction = unitMeasure == "SI" ? f.GasProd_SI : f.GasProd_OFU,
                WaterProduction = unitMeasure == "SI" ? f.WaterProd_SI : f.WaterProd_OFU,
                GasInjection = unitMeasure == "SI" ? f.GasInjected_SI ?? 0 : f.GasInjected_OFU ?? 0,
                WaterInjection = unitMeasure == "SI" ? f.WaterInjected_SI ?? 0 : f.WaterInjected_OFU ?? 0,
                GasExported = unitMeasure == "SI" ? f.GasExported_SI : f.GasExported_OFU,
                GasImported = unitMeasure == "SI" ? f.GasImport_SI ?? default : f.GasImport_OFU ?? default,
                GasFlared = unitMeasure == "SI" ? f.GasFlared_SI : f.GasFlared_OFU,
                GasFuel = unitMeasure == "SI" ? f.GasFuel_SI : f.GasFuel_OFU,
                GasLift = unitMeasure == "SI" ? f.GasLift_SI : f.GasLift_OFU,
                WaterCut = f.WaterCut,
                BasicSedimentWater = f.BasicSedimentWater,
                VoidageReplacementRatio = f.VoidageReplacementRatio,
                UnitMeasure = unitMeasure
            })];
        }

        private List<ProductionByFieldDto> CalculateProductionByField(IEnumerable<DailyOperationSummaryByField> fields, string unitMeasure)
        {
            return fields
                .Select(x => new ProductionByFieldDto
                {
                    FieldId = x.FieldId,
                    ComplexId = x.ComplexId,
                    FacilityId = x.FacilityId,
                    OilProduction = unitMeasure == "SI" ? x.ActualOilProd_SI : x.ActualOilProd_OFU,
                    ExpectedOilProduction = unitMeasure == "SI" ? x.ExpectedOilProd_SI : x.ExpectedOilProd_OFU,
                    GasProduction = unitMeasure == "SI" ? x.GasProd_SI : x.GasProd_OFU,
                    WaterProduction = unitMeasure == "SI" ? x.WaterProd_SI : x.WaterProd_OFU,
                    GasInjection = unitMeasure == "SI" ? x.GasInjected_SI ?? 0 : x.GasInjected_OFU ?? 0,
                    WaterInjection = unitMeasure == "SI" ? x.WaterInjected_SI ?? 0 : x.WaterInjected_OFU ?? 0,
                    GasExported = unitMeasure == "SI" ? x.GasExported_SI : x.GasExported_OFU,
                    GasImported = unitMeasure == "SI" ? x.GasImport_SI ?? default : x.GasImport_OFU ?? default,
                    GasFlared = unitMeasure == "SI" ? x.GasFlared_SI : x.GasFlared_OFU,
                    GasFuel = unitMeasure == "SI" ? x.GasFuel_SI : x.GasFuel_OFU,
                    GasLift = unitMeasure == "SI" ? x.GasLift_SI : x.GasLift_OFU,
                    WaterCut = x.WaterCut,
                    BasicSedimentWater = x.BasicSedimentWater,
                    VoidageReplacementRatio = x.VoidageReplacementRatio,
                    UnitMeasure = unitMeasure,
                    WaterDischarge = unitMeasure == "SI" ? x.WaterDischarge_SI ?? 0 : x.WaterDischarge_OFU ?? 0,
                })
                .ToList();
        }

        private TotalsDto CalculateBlockTotals(
            List<DailyOperationSummaryByFacility> facilities,
            List<DailyOperationSummaryByFacility> lastValuesFacilities,
            List<DailyOperationSummaryByFacility> homologosFacilities,
            List<ProdAlngForecastData> alngForecastData,
            List<ProdForecastData> forecastData,
            int days,
            int dateDiff,
            string unitMeasure)
        {
            return new TotalsDto
            {
                TotalOilProduction = unitMeasure == "SI" ? facilities.Sum(x => x.ActualOilProd_SI) : facilities.Sum(x => x.ActualOilProd_OFU),
                TotalExpectedOilProduction = unitMeasure == "SI" ? facilities.Sum(x => x.ExpectedOilProd_SI) : facilities.Sum(x => x.ExpectedOilProd_OFU),
                TotalGasProduction = unitMeasure == "SI" ? facilities.Sum(x => x.GasProd_SI) : facilities.Sum(x => x.GasProd_OFU),
                TotalWaterProduction = unitMeasure == "SI" ? facilities.Sum(x => x.WaterProd_SI) : facilities.Sum(x => x.WaterProd_OFU),
                TotalGasInjection = unitMeasure == "SI" ? facilities.Sum(x => x.GasInjected_SI ?? 0) : facilities.Sum(x => x.GasInjected_OFU ?? 0),
                TotalWaterInjection = unitMeasure == "SI" ? facilities.Sum(x => x.WaterInjected_SI ?? 0) : facilities.Sum(x => x.WaterInjected_OFU ?? 0),
                TotalGasExported = unitMeasure == "SI" ? facilities.Sum(x => x.GasExported_SI) : facilities.Sum(x => x.GasExported_OFU),
                TotalGasImported = unitMeasure == "SI" ? facilities.Sum(x => x.GasImport_SI ?? default) : facilities.Sum(x => x.GasImport_OFU ?? default),
                TotalGasFlared = unitMeasure == "SI" ? facilities.Sum(x => x.GasFlared_SI) : facilities.Sum(x => x.GasFlared_OFU),
                TotalGasFuel = unitMeasure == "SI" ? facilities.Sum(x => x.GasFuel_SI) : facilities.Sum(x => x.GasFuel_OFU),
                TotalGasLift = unitMeasure == "SI" ? facilities.Sum(x => x.GasLift_SI) : facilities.Sum(x => x.GasLift_OFU),
                TotalWaterCut = facilities.Sum(x => x.WaterCut),
                TotalWaterDischarge = unitMeasure == "SI" ? facilities.Sum(x => x.WaterDischarge_SI ?? 0) : facilities.Sum(x => x.WaterDischarge_OFU ?? 0),
                UnitMeasure = unitMeasure,
                TotalLastValueOilProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.ActualOilProd_SI) : lastValuesFacilities.Sum(x => x.ActualOilProd_OFU),
                TotalHomologousOilProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.ActualOilProd_SI) : homologosFacilities.Sum(x => x.ActualOilProd_OFU),
                TotalLastValueGasProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.GasProd_SI) : lastValuesFacilities.Sum(x => x.GasProd_OFU),
                TotalHomologousGasProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.GasProd_SI) : homologosFacilities.Sum(x => x.GasProd_OFU),

                TotalLastValueFlaredGasProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.GasFlared_SI) : lastValuesFacilities.Sum(x => x.GasFlared_OFU),
                TotalHomologousFlaredGasProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.GasFlared_SI) : homologosFacilities.Sum(x => x.GasFlared_OFU),

                TotalLastValueWaterProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.WaterProd_SI) : lastValuesFacilities.Sum(x => x.WaterProd_OFU),
                TotalHomologousWaterProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.WaterProd_SI) : homologosFacilities.Sum(x => x.WaterProd_OFU),
                TotalGasForecast = forecastData.Sum(x => x.ProducedGasForecast) != 0 ? unitMeasure == "SI" ? (forecastData.Sum(x => x.ProducedGasForecast) / days) * dateDiff * OFU_SI_CONVERSION : (forecastData.Sum(x => x.ProducedGasForecast) / days) * dateDiff : default,
                TotalWaterForecast = forecastData.Sum(x => x.ProducedWaterForecast) != 0 ? unitMeasure == "SI" ? (forecastData.Sum(x => x.ProducedWaterForecast) / days) * dateDiff * OFU_SI_CONVERSION : (forecastData.Sum(x => x.ProducedWaterForecast) / days) * dateDiff : default,
                TotalOilForecast = alngForecastData.Sum(x => x.OilProductionForecast) != 0 ? unitMeasure == "SI" ? (alngForecastData.Sum(x => x.OilProductionForecast) / days) * dateDiff * OFU_SI_CONVERSION : (alngForecastData.Sum(x => x.OilProductionForecast) / days) * dateDiff : default,
            };
        }

        private decimal GetEficiency(ICollection<ProdDataDaily> prodDataDailies)
        {
            decimal totalProduction = prodDataDailies.Sum(p => p.OperationSummaryByFacility.Sum(f => f.ActualOilProd_OFU));

            return prodDataDailies.SelectMany(x => x.OperationSummaryByFacility.Select(f => new
            {
                FacilityEfficiency = f.PlantEfficiency != 0 && f.ActualOilProd_OFU != 0 && (x.OperationSummaryByFacility.Sum(p => p.ActualOilProd_OFU)) != 0 ? (f.ActualOilProd_OFU / (x.OperationSummaryByFacility.Sum(p => p.ActualOilProd_OFU))) * f.PlantEfficiency : default,
                FacilityTotalProduction = x.OperationSummaryByFacility.Sum(p => p.ActualOilProd_OFU)
            })).ToList().Sum(x => x.FacilityEfficiency != 0 && x.FacilityTotalProduction != 0 && totalProduction != 0 ? (x.FacilityTotalProduction / totalProduction) * x.FacilityEfficiency : default);
        }

        private WaterQualityMetricsDto CalculateWaterQualityMetrics(IEnumerable<DailyWaterQuality> waterQuality)
        {
            var wqList = waterQuality.ToList();
            if (!wqList.Any()) return new WaterQualityMetricsDto();

            return new WaterQualityMetricsDto
            {
                //AverageSalinity = wqList.Average(x => x.Salinity ?? 0),
                //AverageTemperature = wqList.Average(x => x.Temperature ?? 0),
                //AveragePH = wqList.Average(x => x.PH ?? 0),
                TotalSamples = wqList.Count
            };
        }

        private ProductionLossMetricsDto CalculateProductionLossMetrics(
            IEnumerable<DailyProductionLossSummary> productionLossSummary,
            IEnumerable<DailyProductionLoss> productionLoss
        )
        {
            //TODO :: FINISH THIS METHOD

            //var lossList = productionLossSummary.ToList();
            //if (!lossList.Any()) return new ProductionLossMetricsDto();

            ////var plannedOilLoss = lossList.Sum(x => x.PlannedOilLoss_SI);
            ////var unplannedOilLoss = lossList.Sum(x => x.UnplannedOilLoss_SI);

            ////First Find info on Json?.ProductionLoss, if not found go to Json?.ProductionLossSummary object.

            //var oilLoss = prodDataDailies?.SelectMany(x => x.Json?.ProductionLoss ?? new List<DailyProductionLoss>())?.Where(x => x.LossMotiveId.Contains("TL_NPL_") && x.SystemId == systemType.Id)?.Sum(x => unitType == UnitType.OFU ? x.OilLoss_OFU : x.OilLoss_SI);
            //var oilLossSummary = prodDataDailies?.SelectMany(x => x.Json?.ProductionLossSummary ?? new List<DailyProductionLossSummary>())?.Where(x => x.SystemId == systemType.Id)?.Sum(x => unitType == UnitType.OFU ? x.UnplannedOilLoss_OFU : x.UnplannedOilLoss_SI) ?? default;
            //totalType = oilLoss != null && oilLoss > 0 ? Math.Round(oilLoss ?? default) : Math.Round(oilLossSummary);


            //return new ProductionLossMetricsDto
            //{
            //    PlannedOilLoss = plannedOilLoss,
            //    UnplannedOilLoss = unplannedOilLoss,
            //    TotalOilLoss = plannedOilLoss + unplannedOilLoss,
            //    //TotalOilLoss = lossList.Sum(x => x.OilLoss_SI),
            //    //TotalGasLoss = lossList.Sum(x => x.GasLoss ?? 0),
            //    //TotalWaterLoss = lossList.Sum(x => x.WaterLoss ?? 0),
            //    LossEventCount = lossList.Count,
            //    LossByMotive = lossList.GroupBy(x => x.LossMotiveId)
            //        .ToDictionary(g => g.Key, g => g.Sum(x => x.OilLoss_SI))
            //};
            return new ProductionLossMetricsDto();
        }

        //private EquipmentStatusMetricsDto CalculateEquipmentStatusMetrics(IEnumerable<DailyEquipmentStatus> equipmentStatuses)
        //{
        //    var statusList = equipmentStatuses.ToList();
        //    if (!statusList.Any()) return new EquipmentStatusMetricsDto();

        //    return new EquipmentStatusMetricsDto
        //    {
        //        TotalEquipment = statusList.Count,
        //        OperationalCount = statusList.Count(x => x.Status == "Operational"),
        //        MaintenanceCount = statusList.Count(x => x.Status == "Maintenance"),
        //        DownCount = statusList.Count(x => x.Status == "Down"),
        //        AvailabilityPercentage = statusList.Any() ? 
        //            (decimal)statusList.Count(x => x.Status == "Operational") / statusList.Count * 100 : 0
        //    };
        //}
    }
}