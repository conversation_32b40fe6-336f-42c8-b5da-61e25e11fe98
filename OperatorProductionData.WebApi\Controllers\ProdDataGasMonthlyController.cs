﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Notifications;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.SignalRHubs;
using OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasMonthly;
using OperatorProductionData.WebApi.Enums;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataGasMonthlyController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataGasMonthlyService _service { get; set; }
        private GasValidationRuleCoreService _validationRuleService { get; set; }
        private ProdDataGasMonthlyRejectedService _rejectedService { get; set; }
        private readonly IHubContext<ProdDataNotificationsHub> _prodDataGasMonthlyNotificationsHub;
        private ProdDataGasMonthlyViewService _viewService { get; set; }
        private ProdDataGasMonthlyVerificationViewService _verificationViewService { get; set; }
        private IMapper _mapper { get; set; }
        public ProdDataGasMonthlyController(
            ILogger<ProdDataGasMonthlyController> logger,
            ProdDataGasMonthlyService service,
            ProdDataGasMonthlyViewService viewService,
            ProdDataGasMonthlyVerificationViewService verificationViewService,
            ProdDataGasMonthlyRejectedService rejectedService,
             IHubContext<ProdDataNotificationsHub> prodDataGasMonthlyNotificationsHub,
            GasValidationRuleCoreService validationRuleService,
              IMapper mapper)
        {
            _logger = logger;
            _service = service;
            _viewService = viewService;
            _verificationViewService = verificationViewService;
            _rejectedService = rejectedService;
            _prodDataGasMonthlyNotificationsHub = prodDataGasMonthlyNotificationsHub;
            _validationRuleService = validationRuleService;
            _mapper = mapper;
        }


        [HttpPost]
        [Route("SubmitProdDataGasMonthly")]
        public async Task<IActionResult> SubmitProdDataGasMonthly([FromBody] ReferenceDataProdDataGasMonthly referenceDataProdDataGasMonthly)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new ProdDataGasMonthlyValidator(EnumAction.Add, referenceDataProdDataGasMonthly.GasReferenceData, _validationRuleService);
                var result = validator.Validate(referenceDataProdDataGasMonthly.ProdDataGasMonthly);
                if (!result.IsValid)
                {
                    apiResponse.Status = ApiResponseStatus.Error;
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );
                    //Register Rejected ProdDataGasMonthly
                    var prodDataGasMonthlyRejected = _mapper.Map<ProdDataGasMonthly, ProdDataGasMonthlyRejected>(referenceDataProdDataGasMonthly.ProdDataGasMonthly);
                    prodDataGasMonthlyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataGasMonthlyRejected);
                    await GenerateNotification(referenceDataProdDataGasMonthly,
                        new DateTime(referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, 1), NotificationStyle.Danger, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, false);
                }
                else
                {
                    var newModel = await _service.Add(referenceDataProdDataGasMonthly.ProdDataGasMonthly);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };
                    await GenerateNotification(referenceDataProdDataGasMonthly,
                       new DateTime(referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, 1), NotificationStyle.Success, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, true);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem submitting Product Data Gas Monthly.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPut]
        [Route("ChangeProdDataGasMonthly")]
        public async Task<IActionResult> ChangeProdDataGasMonthly([FromBody] ReferenceDataProdDataGasMonthly referenceDataProdDataGasMonthly)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new ProdDataGasMonthlyValidator(EnumAction.Update, referenceDataProdDataGasMonthly.GasReferenceData, _validationRuleService);
                var result = validator.Validate(referenceDataProdDataGasMonthly.ProdDataGasMonthly);
                if (!result.IsValid)
                {
                    apiResponse.Status = ApiResponseStatus.Error;
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );
                    //Register Rejected ProdDataGasMonthly
                    var prodDataGasMonthlyRejected = _mapper.Map<ProdDataGasMonthly, ProdDataGasMonthlyRejected>(referenceDataProdDataGasMonthly.ProdDataGasMonthly);
                    prodDataGasMonthlyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataGasMonthlyRejected);
                    await GenerateNotification(referenceDataProdDataGasMonthly,
                        new DateTime(referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, 1), NotificationStyle.Danger, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, false);
                }
                else
                {
                    var newModel = await _service.Update(referenceDataProdDataGasMonthly.ProdDataGasMonthly);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    await GenerateNotification(referenceDataProdDataGasMonthly,
                       new DateTime(referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, 1), NotificationStyle.Success, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionMonth, referenceDataProdDataGasMonthly.ProdDataGasMonthly.ProductionYear, true);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem updating Product Data Gas Monthly.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataGasMonthly Post - getlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>> apiResponse = res;
                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthly - Error getting sorted, filtered and paged list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthly Post - getlist");
                return Ok(res);
            }
        }

        [HttpPost]
        [Route("getviewlist")]
        public async Task<IActionResult> GetViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataGasMonthlyView Post - getviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>> apiResponse = res;
                apiResponse.Response = await _viewService.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthlyView - Error getting sorted, filtered and paged view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthlyView Post - getviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getviewdetailslist")]
        public async Task<IActionResult> GetViewListDetails()
        {
            _logger.LogInformation("Enter ProdDataGasMonthly GET - getviewdetailslist");
            ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>> res = new ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {

                res.Response = await _viewService.GetAllViewDetails();

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthly - Error getting sorted, filtered and paged view details list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthly GET - getviewdetailslist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getproddatagasmonthlyviewjson")]
        public async Task<IActionResult> GetProdDataGasMonthlyViewJson([FromQuery] Guid id)
        {
            _logger.LogInformation("Enter ProdDataGasMonthly - GetProdDataGasDailyViewJson");
            ApiResponse<ProdDataGasMonthlyGeneric> res = new ApiResponse<ProdDataGasMonthlyGeneric>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataGasMonthlyGeneric> apiResponse = res;
                var json = await _viewService.GetJson(id);

                apiResponse.Response = json;

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthly - Error GetProdDataGasDailyViewJson");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthly Get - GetProdDataGasDailyViewJson");
                return Ok(res);
            }
        }

        private async Task GenerateNotification(ReferenceDataProdDataGasMonthly infrastructureProdDataGasMonthly, DateTime productionDate, NotificationStyle style, int productionMonthFilter, int productionYearFilter, bool isValid)
        {
            try
            {
                await _service.UpdateProdDataGasMonthlyRejectedMaterializedView(productionMonthFilter, productionYearFilter);
                if (isValid)
                {
                    await _service.UpdateProdDataGasMonthlyVerificationMaterializedView(productionMonthFilter, productionYearFilter);
                }

                var methodName = "NotifyProdDataSubmission";
                await _prodDataGasMonthlyNotificationsHub.Clients.All.SendAsync(
                    methodName,
                    new ProdDataNotification
                    {
                        Type = "ProdDataGasMonthly",
                        BlockId = infrastructureProdDataGasMonthly.ProdDataGasMonthly.ComplexId,
                        ProductionDate = productionDate,
                        Date = DateTime.Now,
                        NotificationStyle = style,
                    });
            }
            catch (Exception ex)
            {
                //
            }
        }


        [HttpGet]
        [Route("getbycomplexandproductiondate")]
        public async Task<IActionResult> GetByComplexAndProductionDate(
            [FromQuery] string complexId,
            [FromQuery] int productionYear,
            [FromQuery] int productionMonth
        )
        {
            _logger.LogInformation("Enter ProdDataGasMonthly - GetByComplexAndProductionDate");
            ApiResponse<Guid?> res = new ApiResponse<Guid?>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<Guid?> apiResponse = res;
                apiResponse.Response = await _service.GetByComplexAndProductionDate(complexId, productionYear, productionMonth);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthly - Error GetByComplexAndProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthly Get - GetByComplexAndProductionDate");
                return Ok(res);
            }
        }

    }
}
