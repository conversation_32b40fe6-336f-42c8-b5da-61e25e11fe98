﻿using OperatorProductionData.Models.Utilities;
using System;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasMonthly
{
    public class OperationGasForecastByDay
    {
        public string ProductionDate { get; set; }
        public virtual DateTime ParsedProductionDate
        {
            set
            {
                _tempProductionDate = DateUtils.ConvertDateTimeString(ProductionDate, "yyyy-MM-dd");
            }

            get
            {
                return _tempProductionDate;
            }
        }

        private DateTime _tempProductionDate;
        public string FacilityId { get; set; }
        public string GasTypeId { get; set; }
        public decimal ProductionVolumeForecast_OFU { get; set; }
        public decimal ProductionVolumeForecast_SI { get; set; }
        public decimal HV { get; set; }
    }
}
