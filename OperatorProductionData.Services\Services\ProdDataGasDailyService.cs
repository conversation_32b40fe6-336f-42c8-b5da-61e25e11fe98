﻿using MongoDB.Bson;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataGasDailyService : BaseRepository<ProdDataGasDaily>
    {
        public ProdDataGasDailyService(string connectionString, string databaseName) : base(connectionString, databaseName, "ProdDataGasDaily")
        {
        }

        /// <summary>
        /// Method to get Info about existing ProdDataGasDaily's for the given ComplexId and ProductionDate
        /// </summary>
        /// <param name="complexId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<Guid?> GetByComplexAndProductionDate(string complexId, string productionDate)
        {
            var filterBuilder = Builders<ProdDataGasDaily>.Filter;
            var filter = filterBuilder.Eq(x => x.ComplexId, complexId)
                            & filterBuilder.Eq(x => x.ProductionDate, productionDate)
                            ;

            var prodDataGasDaily = await GetAll(filter);

            return prodDataGasDaily
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefault()
                    ?.VersionsCollectionId;
        }

        public async Task UpdateProdDataGasDailyRejectedMaterializedView(string productionDateFilter)
        {
            var pipeline = new[]
            {
                 new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionDate", productionDateFilter }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataGasDailyRejected" },
                    { "pipeline",
                        new BsonArray
                        {
                            new BsonDocument
                            {
                                { "$match", new BsonDocument
                                    {
                                        { "ProductionDate", productionDateFilter }
                                    }
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionDate", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "ComplexId", "$ComplexId"},
                    { "ProductionDate", "$ProductionDate"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataGasDailyView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }

        public async Task UpdateProdDataGasDailyVerificationMaterializedView(string productionDateFilter)
        {

            var pipeline = new[]
            {
                 new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionDate", productionDateFilter }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "productionDate", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"ComplexId", "$ComplexId"},
                            {"ProductionDate", "$ProductionDate"},
                            {"ParsedProductionDate", "$ParsedProductionDate"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "ComplexId", "$_id.ComplexId" },
                    { "ProductionDate", "$_id.ProductionDate" },
                    { "ParsedProductionDate", "$_id.ParsedProductionDate"},
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataGasDailyVerificationView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }
    }
}
