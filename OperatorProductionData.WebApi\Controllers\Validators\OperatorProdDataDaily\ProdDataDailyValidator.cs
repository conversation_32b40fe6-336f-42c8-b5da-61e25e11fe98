﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations;
using OperatorProductionData.Models.Utilities;
using OperatorProductionData.WebApi.Enums;
using System.Reflection;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataDaily
{
    public class ProdDataDailyValidator : AbstractValidator<ProdDataDaily>
    {
        static readonly DateTime _inceptionDate = DateTime.Parse("2022-10-01 00:00:00.0000000");

        public ProdDataDailyValidator(EnumAction action, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService, ProdDataDailyValidationReport report)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");

            switch (action)
            {
                case EnumAction.Add:

                    ValidateContent(infrastructure, validationRuleService, report);

                    break;
                case EnumAction.Update:
                    var updateReasons = infrastructure.ReferenceTables.UpdateReasons;

                    RuleFor(x => x.Id)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString());

                    RuleFor(x => x.ReasonId)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString())
                        .Must(x => updateReasons.Any(y => y.Id == x))
                            .WithMessage(vr010.Message)
                            .WithErrorCode(vr010.Number.ToString())
                            .WithName(vr010.Type.ToString());

                    ValidateContent(infrastructure, validationRuleService, report);

                    break;
            }
        }

        private void ValidateContent(ReferenceData infrastructure, ValidationRuleCoreService validationRuleService, ProdDataDailyValidationReport report)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr009 = validationRuleService.GetValidationRule("OPD-VR009");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr011 = validationRuleService.GetValidationRule("OPD-VR011");
            var vr012 = validationRuleService.GetValidationRule("OPD-VR012");
            var vr128 = validationRuleService.GetValidationRule("OPD-VR128");

            RuleFor(x => x.ProductionDate)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedProductionDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr128.Message,
                            ErrorCode = vr128.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedProductionDate < _inceptionDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr011.Message,
                            ErrorCode = vr011.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedProductionDate >= DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr012.Message,
                            ErrorCode = vr012.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.BlockId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => infrastructure.Blocks.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.OperationSummaryByFacility)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.OperationSummaryByFacility)
                .SetValidator(x => new DailyOperationSummaryByFacilityValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.OperationSummaryByField)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.OperationSummaryByField)
                .SetValidator(x => new DailyOperationSummaryByFieldValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.ProductionLoss)
                .SetValidator(x => new DailyProductionLossValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.ProductionLoss is not null &&
                    x.ProductionLoss.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId)
                , ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.ProductionLossSummary)
                .SetValidator(x => new DailyProductionLossSummaryValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.ProductionLossSummary is not null &&
                    x.ProductionLossSummary.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId)
                , ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.WaterQuality)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.WaterQuality)
                .SetValidator(x => new DailyWaterQualityValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.CommodityConsumptionSummary)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.CommodityConsumptionSummary)
                .SetValidator(x => new DailyCommodityConsumptionSummaryValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.EquipmentStatus)
                .SetValidator(x => new DailyEquipmentStatusValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.EquipmentStatus is not null &&
                    x.EquipmentStatus.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.WellStatus)
                .SetValidator(x => new DailyWellStatusValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.WellStatus is not null &&
                    x.WellStatus.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.OperationComments)
                .SetValidator(x => new DailyOperationCommentsValidator(
                        x.BlockId,
                        x.ParsedProductionDate,
                        infrastructure,
                        validationRuleService
                     )
                    )
                .When(x =>
                    x.OperationComments is not null &&
                    x.OperationComments.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId)
                , ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.DetailedProduction)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DetailedProduction)
                .SetValidator(x => new DailyDetailedProductionValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.DetailedGasInjection)
                .SetValidator(x => new DailyDetailedGasInjectionValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.DetailedGasInjection is not null &&
                    x.DetailedGasInjection.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.DetailedWaterInjection)
                .SetValidator(x => new DailyDetailedWaterInjectionValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.DetailedWaterInjection is not null &&
                    x.DetailedWaterInjection.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.OilStorageSummary)
                .SetValidator(x => new DailyOilStorageSummaryValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.OilStorageSummary is not null &&
                    x.OilStorageSummary.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.Liftings)
                .SetValidator(x => new DailyLiftingsValidator(x.BlockId,
                        x.ParsedProductionDate,
                        infrastructure,
                        validationRuleService
                        )
                    )
                .When(x =>
                    x.Liftings is not null &&
                    x.Liftings.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.SubmittedDocuments)
                .SetValidator(new DailySubmittedDocumentsValidator(validationRuleService, infrastructure))
            .When(x =>
                x.SubmittedDocuments is not null &&
                x.SubmittedDocuments.Count > 0, ApplyConditionTo.CurrentValidator);


            #region Validations For ReportValidation
            // Dictionary to map property names
            var detailedGasInjectionMap = new Dictionary<string, string>
                {
                    { "GasInjected_OFU", "ActualGasInjection_OFU" },
                    { "GasInjection_SI", "ActualGasInjection_SI" },
                };

            List<string> propertiesToValidate = new List<string>()
                {
                    "ExpectedOilProd_OFU",
                    "ExpectedOilProd_SI",
                    "ActualOilProd_OFU",
                    "ActualOilProd_SI",
                    "GasProd_OFU",
                    "GasProd_SI",
                    "GasInjected_OFU",
                    "GasInjected_SI",
                    "GasExported_OFU",
                    "GasExported_SI",
                    "GasFlared_OFU",
                    "GasFlared_SI",
                    "GasImport_OFU",
                    "GasImport_SI",
                    "GasFuel_OFU",
                    "GasFuel_SI",
                    "GasLift_OFU",
                    "GasLift_SI",
                    "WaterProd_OFU",
                    "WaterProd_SI",
                    "WaterInjected_OFU",
                    "WaterInjected_SI",
                    "WaterDischarge_OFU",
                    "WaterDischarge_SI",
                    "PlantEfficiency",
                    "InstalledProdCapacity_OFU",
                    "InstalledProdCapacity_SI"
                };


            //Validation Type 1 //Facility
            RuleFor(x => x)
            .Custom((prod, context) =>
            {
                var operationSummaryByFacility = prod.OperationSummaryByFacility
                    .GroupBy(x => x.FacilityId)
                    .Select(g => new SummaryByFacilityForFacility
                    {
                        FacilityId = g.Key,
                        ExpectedOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_OFU), IsValid = true },
                        ExpectedOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_SI), IsValid = true },
                        ActualOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_OFU), IsValid = true },
                        ActualOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_SI), IsValid = true },
                        GasProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_OFU), IsValid = true },
                        GasProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_SI), IsValid = true },
                        GasInjected_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasInjected_OFU), IsValid = true },
                        GasInjected_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasInjected_SI), IsValid = true },
                        GasExported_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasExported_OFU), IsValid = true },
                        GasExported_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasExported_SI), IsValid = true },
                        GasFlared_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFlared_OFU), IsValid = true },
                        GasFlared_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFlared_SI), IsValid = true },
                        GasImport_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasImport_OFU), IsValid = true },
                        GasImport_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasImport_SI), IsValid = true },
                        GasFuel_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFuel_OFU), IsValid = true },
                        GasFuel_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFuel_SI), IsValid = true },
                        GasLift_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_OFU), IsValid = true },
                        GasLift_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_SI), IsValid = true },
                        WaterProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_OFU), IsValid = true },
                        WaterProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_SI), IsValid = true },
                        WaterInjected_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterInjected_OFU), IsValid = true },
                        WaterInjected_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterInjected_SI), IsValid = true },
                        WaterDischarge_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterDischarge_OFU), IsValid = true },
                        WaterDischarge_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterDischarge_SI), IsValid = true },
                        InstalledProdCapacity_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.InstalledProdCapacity_SI), IsValid = true },
                        InstalledProdCapacity_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.InstalledProdCapacity_OFU), IsValid = true },
                        PlantEfficiency = new ValidationResult<decimal?> { Value = g.Sum(x => x.PlantEfficiency), IsValid = true }
                    }).ToList();




                //operationSummaryByField
                var operationSummaryByFieldWithFacility = prod.OperationSummaryByField.Where(x => x.FacilityId != null); //Because FacilityId isn't required in OperationSummaryByField
                var operationSummaryByFieldByFacility = operationSummaryByFieldWithFacility
                    .GroupBy(x => x.FacilityId ?? string.Empty) // Use string.Empty for null FacilityId (Never Happens)
                    .Select(g => new SummaryByFieldForFacility
                    {
                        FacilityId = g.Key,
                        ExpectedOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_OFU), IsValid = true },
                        ExpectedOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_SI), IsValid = true },
                        ActualOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_OFU), IsValid = true },
                        ActualOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_SI), IsValid = true },
                        GasProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_OFU), IsValid = true },
                        GasProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_SI), IsValid = true },
                        GasInjected_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasInjected_OFU), IsValid = true },
                        GasInjected_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasInjected_SI), IsValid = true },
                        GasExported_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasExported_OFU), IsValid = true },
                        GasExported_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasExported_SI), IsValid = true },
                        GasFlared_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFlared_OFU), IsValid = true },
                        GasFlared_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFlared_SI), IsValid = true },
                        GasImport_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasImport_OFU), IsValid = true },
                        GasImport_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasImport_SI), IsValid = true },
                        GasFuel_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFuel_OFU), IsValid = true },
                        GasFuel_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFuel_SI), IsValid = true },
                        GasLift_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_OFU), IsValid = true },
                        GasLift_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_SI), IsValid = true },
                        WaterProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_OFU), IsValid = true },
                        WaterProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_SI), IsValid = true },
                        WaterInjected_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterInjected_OFU), IsValid = true },
                        WaterInjected_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterInjected_SI), IsValid = true },
                        WaterDischarge_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterDischarge_OFU), IsValid = true },
                        WaterDischarge_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterDischarge_SI), IsValid = true }
                    }).ToList();

                //detailedProduction
                var detailedProductionByFacility = prod.DetailedProduction
                    .GroupBy(x => x.FacilityId)
                    .Select(g => new DetailedProductionForFacility
                    {
                        FacilityId = g.Key,
                        ExpectedOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_OFU), IsValid = true },
                        ExpectedOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_SI), IsValid = true },
                        ActualOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_OFU), IsValid = true },
                        ActualOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_SI), IsValid = true },
                        GasProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_OFU), IsValid = true },
                        GasProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_SI), IsValid = true },
                        GasLift_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_OFU), IsValid = true },
                        GasLift_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_SI), IsValid = true },
                        WaterProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_OFU), IsValid = true },
                        WaterProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_SI), IsValid = true },
                    }).ToList();


                //DetailedGasInjection

                var detailedGasInjectionByFacility = prod.DetailedGasInjection?
                    .GroupBy(x => x.FacilityId)
                    .Select(g => new DetailedGasInjectionForFacility
                    {
                        FacilityId = g.Key,
                        GasInjected_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualGasInjection_OFU), IsValid = true },
                        GasInjected_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualGasInjection_SI), IsValid = true },
                    }).ToList() ?? new List<DetailedGasInjectionForFacility>();


                //Count Facilities on ByFacility
                var numberOfFacilities = operationSummaryByFacility.Count();

                for (int i = 0; i < numberOfFacilities; i++)
                {
                    var facilityKey = operationSummaryByFacility[i].FacilityId;

                    var facility = operationSummaryByFacility.FirstOrDefault(x => x.FacilityId == facilityKey);
                    var field = operationSummaryByFieldByFacility.FirstOrDefault(x => x.FacilityId == facilityKey);
                    var detailedProduction = detailedProductionByFacility.FirstOrDefault(x => x.FacilityId == facilityKey);
                    var detailedGasInjection = detailedGasInjectionByFacility.FirstOrDefault(x => x.FacilityId == facilityKey);


                    foreach (var propertyName in propertiesToValidate)
                    {
                        ValidationResult<decimal?> forFacilityPropValue = null;
                        ValidationResult<decimal?> forFieldPropValue = null;
                        ValidationResult<decimal?> forProductionPropValue = null;
                        ValidationResult<decimal?> forGasPropValue = null;

                        //forFacilityPropValue
                        if (facility != null)
                        {
                            var prop = facility.GetType().GetProperty(propertyName);

                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(facility);

                                ValidationResult<decimal?> propValue = null;

                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forFacilityPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forFacilityPropValue = (ValidationResult<decimal?>)propValueObj;
                                }

                            }
                        }

                        //forFieldPropValue
                        if (field != null)
                        {
                            var prop = field.GetType().GetProperty(propertyName);

                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(field);

                                ValidationResult<decimal?> propValue = null;

                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forFieldPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forFieldPropValue = (ValidationResult<decimal?>)propValueObj;
                                }

                            }
                        }

                        //forProductionPropValue
                        if (detailedProduction != null)
                        {
                            var prop = detailedProduction.GetType().GetProperty(propertyName);
                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(detailedProduction);
                                ValidationResult<decimal?> propValue = null;
                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forProductionPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forProductionPropValue = (ValidationResult<decimal?>)propValueObj;
                                }
                            }
                        }

                        //forGasPropValue
                        if (detailedGasInjection != null)
                        {
                            var prop = detailedGasInjection.GetType().GetProperty(propertyName);
                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(detailedGasInjection);
                                ValidationResult<decimal?> propValue = null;
                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forGasPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forGasPropValue = (ValidationResult<decimal?>)propValueObj;
                                }
                            }
                        }

                        var valuesToValidated = new List<decimal>();

                        if (forFacilityPropValue?.Value != null)
                            valuesToValidated.Add(forFacilityPropValue.Value.Value);

                        if (forFieldPropValue?.Value != null)
                            valuesToValidated.Add(forFieldPropValue.Value.Value);

                        if (forProductionPropValue?.Value != null)
                            valuesToValidated.Add(forProductionPropValue.Value.Value);

                        if (forGasPropValue?.Value != null)
                            valuesToValidated.Add(forGasPropValue.Value.Value);

                        var isValid = AreValidValues(valuesToValidated);

                        if (!isValid)
                        {

                            if (forFacilityPropValue?.Value != null)
                            {
                                SetIsValidToFalse(facility, propertyName);
                            }

                            if (forFieldPropValue?.Value != null)
                            {
                                SetIsValidToFalse(field, propertyName); ;
                            }

                            if (forProductionPropValue?.Value != null)
                            {
                                SetIsValidToFalse(detailedProduction, propertyName);
                            }

                            if (forGasPropValue?.Value != null)
                            {
                                SetIsValidToFalse(detailedGasInjection, propertyName);
                            }


                            context.AddFailure(new ValidationFailure
                            {
                                ErrorMessage = $"The sum of this property for facility: {facilityKey} across the json objects is different!",
                                ErrorCode = vr012.Number.ToString(),
                                PropertyName = propertyName,
                                Severity = Severity.Warning
                            });
                        }
                    }
                }

                report.SummaryByFacilityForFacility = operationSummaryByFacility;
                report.SummaryByFieldForFacility = operationSummaryByFieldByFacility;
                report.ProductionDetailedForFacility = detailedProductionByFacility;
                report.DetailedGasInjectionForFacility = detailedGasInjectionByFacility;
            });


            //Validation Type 2 //facilityId and fieldId
            RuleFor(x => x)
            .Custom((prod, context) =>
            {

                //operationSummaryByField
                var operationSummaryByFieldWithFacility = prod.OperationSummaryByField.Where(x => x.FacilityId != null); //Because FacilityId isn't required in OperationSummaryByField
                var operationSummaryByFieldByFacilityAndField = operationSummaryByFieldWithFacility
                    .GroupBy(x => (x.FacilityId ?? string.Empty, x.FieldId))// Use string.Empty for null FacilityId (Never Happens)
                    .Select(g => new SummaryByFieldForFacilityAndField
                    {
                        FacilityId = g.Key.Item1,
                        FieldId = g.Key.Item2,
                        ExpectedOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_OFU), IsValid = true },
                        ExpectedOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_SI), IsValid = true },
                        ActualOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_OFU), IsValid = true },
                        ActualOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_SI), IsValid = true },
                        GasProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_OFU), IsValid = true },
                        GasProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_SI), IsValid = true },
                        GasInjected_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasInjected_OFU), IsValid = true },
                        GasInjected_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasInjected_SI), IsValid = true },
                        GasExported_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasExported_OFU), IsValid = true },
                        GasExported_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasExported_SI), IsValid = true },
                        GasFlared_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFlared_OFU), IsValid = true },
                        GasFlared_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFlared_SI), IsValid = true },
                        GasImport_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasImport_OFU), IsValid = true },
                        GasImport_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.GasImport_SI), IsValid = true },
                        GasFuel_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFuel_OFU), IsValid = true },
                        GasFuel_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasFuel_SI), IsValid = true },
                        GasLift_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_OFU), IsValid = true },
                        GasLift_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_SI), IsValid = true },
                        WaterProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_OFU), IsValid = true },
                        WaterProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_SI), IsValid = true },
                        WaterInjected_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterInjected_OFU), IsValid = true },
                        WaterInjected_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterInjected_SI), IsValid = true },
                        WaterDischarge_OFU = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterDischarge_OFU), IsValid = true },
                        WaterDischarge_SI = new ValidationResult<decimal?> { Value = g.Sum(x => x.WaterDischarge_SI), IsValid = true }
                    }).ToList();

                //detailedProduction
                var detailedProductionByFacilityAndField = prod.DetailedProduction
                    .GroupBy(x => (x.FacilityId, x.FieldId))
                    .Select(g => new DetailedProductionForFacilityAndField
                    {
                        FacilityId = g.Key.FacilityId,
                        FieldId = g.Key.FieldId,
                        ExpectedOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_OFU), IsValid = true },
                        ExpectedOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ExpectedOilProd_SI), IsValid = true },
                        ActualOilProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_OFU), IsValid = true },
                        ActualOilProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualOilProd_SI), IsValid = true },
                        GasProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_OFU), IsValid = true },
                        GasProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasProd_SI), IsValid = true },
                        GasLift_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_OFU), IsValid = true },
                        GasLift_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.GasLift_SI), IsValid = true },
                        WaterProd_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_OFU), IsValid = true },
                        WaterProd_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.WaterProd_SI), IsValid = true },
                    }).ToList();


                //DetailedGasInjection

                var detailedGasInjectionByFacilityAndField = prod.DetailedGasInjection?
                    .GroupBy(x => (x.FacilityId, x.FieldId))
                    .Select(g => new DetailedGasInjectionForFacilityAndField
                    {
                        FacilityId = g.Key.FacilityId,
                        FieldId = g.Key.FieldId,
                        GasInjected_OFU = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualGasInjection_OFU), IsValid = true },
                        GasInjected_SI = new ValidationResult<decimal> { Value = g.Sum(x => x.ActualGasInjection_SI), IsValid = true },
                    }).ToList() ?? new List<DetailedGasInjectionForFacilityAndField>();

                for (int i = 0; i < operationSummaryByFieldByFacilityAndField.Count(); i++)
                {
                    var facilityKey = operationSummaryByFieldByFacilityAndField[i].FacilityId;
                    var fieldKey = operationSummaryByFieldByFacilityAndField[i].FieldId;

                    var field = operationSummaryByFieldByFacilityAndField.FirstOrDefault(x => x.FacilityId == facilityKey && x.FieldId == fieldKey);
                    var detailedProduction = detailedProductionByFacilityAndField.FirstOrDefault(x => x.FacilityId == facilityKey && x.FieldId == fieldKey);
                    var detailedGasInjection = detailedGasInjectionByFacilityAndField.FirstOrDefault(x => x.FacilityId == facilityKey && x.FieldId == fieldKey);


                    foreach (var propertyName in propertiesToValidate)
                    {
                        ValidationResult<decimal?> forFieldPropValue = null;
                        ValidationResult<decimal?> forProductionPropValue = null;
                        ValidationResult<decimal?> forGasPropValue = null;

                        //forFieldPropValue
                        if (field != null)
                        {
                            var prop = field.GetType().GetProperty(propertyName);

                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(field);

                                ValidationResult<decimal?> propValue = null;

                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forFieldPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forFieldPropValue = (ValidationResult<decimal?>)propValueObj;
                                }

                            }
                        }

                        //forProductionPropValue
                        if (detailedProduction != null)
                        {
                            var prop = detailedProduction.GetType().GetProperty(propertyName);
                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(detailedProduction);
                                ValidationResult<decimal?> propValue = null;
                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forProductionPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forProductionPropValue = (ValidationResult<decimal?>)propValueObj;
                                }
                            }
                        }

                        //forGasPropValue
                        if (detailedGasInjection != null)
                        {
                            var prop = detailedGasInjection.GetType().GetProperty(propertyName);
                            if (prop != null)
                            {
                                var propValueObj = prop.GetValue(detailedGasInjection);
                                ValidationResult<decimal?> propValue = null;
                                if (prop.PropertyType == typeof(ValidationResult<decimal>))
                                {
                                    forGasPropValue = ((ValidationResult<decimal>)propValueObj).ToNullable();
                                }
                                else
                                {
                                    forGasPropValue = (ValidationResult<decimal?>)propValueObj;
                                }
                            }
                        }

                        var valuesToValidated = new List<decimal>();

                        if (forFieldPropValue?.Value != null)
                        {
                            valuesToValidated.Add(forFieldPropValue.Value.Value);
                        }

                        if (forProductionPropValue?.Value != null)
                            valuesToValidated.Add(forProductionPropValue.Value.Value);

                        if (forGasPropValue?.Value != null)
                            valuesToValidated.Add(forGasPropValue.Value.Value);

                        var isValid = AreValidValues(valuesToValidated);

                        if (!isValid)
                        {
                            if (forFieldPropValue?.Value != null)
                            {
                                SetIsValidToFalse(field, propertyName); ;
                            }

                            if (forProductionPropValue?.Value != null)
                            {
                                SetIsValidToFalse(detailedProduction, propertyName);
                            }

                            if (forGasPropValue?.Value != null)
                            {
                                SetIsValidToFalse(detailedGasInjection, propertyName);
                            }

                            context.AddFailure(new ValidationFailure
                            {
                                ErrorMessage = $"The sum of this property for field: {fieldKey} across the json objects is different!",
                                ErrorCode = vr012.Number.ToString(),
                                PropertyName = propertyName,
                                Severity = Severity.Warning
                            });
                        }
                    }
                }

                report.SummaryByFieldForFacilityAndField = operationSummaryByFieldByFacilityAndField;
                report.ProductionDetailedForFacilityAndField = detailedProductionByFacilityAndField;
                report.DetailedGasInjectionForFacilityAndField = detailedGasInjectionByFacilityAndField;
            });

            //Validation Totals
            RuleFor(x => x)
            .Custom((prod, context) =>
            {
                report.SummaryByFacilityTotal = new SummaryByFacility();
                report.SummaryByFieldTotal = new SummaryByField();
                report.DetailedProductionTotal = new DetailedProduction();
                report.DetailedGasInjectionTotal = new DetailedGasInjection();

                foreach (var prop in propertiesToValidate)
                {
                    decimal? totalOperationSummaryByFacility = null;
                    decimal? totalOperationSummaryByField = null;
                    decimal? totalDetailedProduction = null;
                    decimal? totalDetailedGasInjection = null;


                    var propInfoOperationSummaryByFacility = typeof(DailyOperationSummaryByFacility).GetProperty(prop);
                    if (propInfoOperationSummaryByFacility != null)
                    {
                        totalOperationSummaryByFacility = prod.OperationSummaryByFacility.Sum(x => (decimal)(propInfoOperationSummaryByFacility.GetValue(x) ?? 0.0m));
                    }

                    var propInfoOperationSummaryByField = typeof(DailyOperationSummaryByField).GetProperty(prop);
                    if (propInfoOperationSummaryByField != null)
                    {
                        totalOperationSummaryByField = prod.OperationSummaryByField.Sum(x => (decimal)(propInfoOperationSummaryByField.GetValue(x) ?? 0.0m));
                    }

                    var propInfoDailyDetailedProduction = typeof(DailyDetailedProduction).GetProperty(prop);
                    if (propInfoDailyDetailedProduction != null)
                    {
                        totalDetailedProduction = prod.DetailedProduction.Sum(x => (decimal)(propInfoDailyDetailedProduction.GetValue(x) ?? 0.0m));
                    }

                    PropertyInfo? propInfoDetailedGasInjection;
                    if (detailedGasInjectionMap.TryGetValue(prop, out var mappedProp))
                    {
                        propInfoDetailedGasInjection = typeof(DailyDetailedGasInjection).GetProperty(mappedProp);
                        if (propInfoDetailedGasInjection != null)
                        {
                            totalDetailedGasInjection = prod.DetailedGasInjection?.Sum(x => (decimal)(propInfoDetailedGasInjection.GetValue(x) ?? 0.0m));
                        }
                    }
                    else
                    {
                        propInfoDetailedGasInjection = typeof(DailyDetailedGasInjection).GetProperty(prop);
                        if (propInfoDetailedGasInjection != null)
                        {
                            totalDetailedGasInjection = prod.DetailedGasInjection?.Sum(x => (decimal)(propInfoDetailedGasInjection.GetValue(x) ?? 0.0m));
                        }
                    }

                    var valuesToValidated = new List<decimal>();

                    if (totalOperationSummaryByFacility != null)
                        valuesToValidated.Add((decimal)totalOperationSummaryByFacility);

                    if (totalOperationSummaryByField != null)
                        valuesToValidated.Add((decimal)totalOperationSummaryByField);

                    if (totalDetailedProduction != null)
                        valuesToValidated.Add((decimal)totalDetailedProduction);

                    if (totalDetailedGasInjection != null)
                        valuesToValidated.Add((decimal)totalDetailedGasInjection);



                    var isValid = AreValidValues(valuesToValidated);

                    if (!isValid)
                    {
                        context.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = $"{prop}: The total sum for this property is different.",
                            ErrorCode = "VR012",
                            PropertyName = prop,
                            Severity = Severity.Warning
                        });
                    }


                    if (propInfoOperationSummaryByFacility != null)
                    {
                        SetValidationResultForProperty(report.SummaryByFacilityTotal, prop, totalOperationSummaryByFacility, isValid);
                    }

                    if (propInfoOperationSummaryByField != null)
                    {
                        SetValidationResultForProperty(report.SummaryByFieldTotal, prop, totalOperationSummaryByField, isValid);
                    }

                    if (propInfoDailyDetailedProduction != null)
                    {
                        SetValidationResultForProperty(report.DetailedProductionTotal, prop, totalDetailedProduction, isValid);
                    }

                    if (propInfoDetailedGasInjection != null)
                    {
                        SetValidationResultForProperty(report.DetailedGasInjectionTotal, prop, totalDetailedGasInjection, isValid);
                    }


                }


            });

            //Validations For Liftings and Allocations
            RuleFor(x => x)
            .Custom((prod, context) =>
            {
                report.LiftingsAndAllocations = new List<LiftingAndAllocations>();

                if (prod.Liftings != null && prod.Liftings.Count > 0)
                {
                    foreach (var lifting in prod.Liftings)
                    {
                        var listToCheckOFU = new List<decimal>
                        {
                            lifting.QuantityLoaded_OFU,
                            lifting.Allocations.Sum(x => x.Quantity_OFU)
                        };
                        var listToCheckSI = new List<decimal>
                        {
                            lifting.QuantityLoaded_SI,
                            lifting.Allocations.Sum(x => x.Quantity_SI)
                        };

                        var IsValidOFU = AreValidValues(listToCheckOFU);
                        var IsValidSI = AreValidValues(listToCheckSI);

                        var reportLifting = new LiftingAndAllocations()
                        {
                            StartDate = lifting.ParsedStartDate,
                            EndDate = lifting.ParsedEndDate,
                            Vessel = lifting.VesselName,
                            QuantityLoaded_OFU = new ValidationResult<decimal> { Value = lifting.QuantityLoaded_OFU, IsValid = IsValidOFU },
                            QuantityLoaded_SI = new ValidationResult<decimal> { Value = lifting.QuantityLoaded_SI, IsValid = IsValidSI },

                        };

                        if (lifting.Allocations != null && lifting.Allocations.Count > 0)
                        {
                            reportLifting.Allocations = new List<Allocation>();

                            foreach (var allocation in lifting.Allocations)
                            {
                                var reportAllocation = new Allocation()
                                {
                                    Quantity_OFU = new ValidationResult<decimal> { Value = allocation.Quantity_OFU, IsValid = IsValidOFU },
                                    Quantity_SI = new ValidationResult<decimal> { Value = allocation.Quantity_SI, IsValid = IsValidSI },
                                    NIF = allocation.ExporterNif
                                };

                                reportLifting.Allocations.Add(reportAllocation);
                            }
                        }
                        report.LiftingsAndAllocations.Add(reportLifting);
                    }
                }

            });

            //Validations For ProductionLosses
            RuleFor(x => x)
            .Custom((prod, context) =>
            {


                //Join ProductionLossSummary and ProductionLoss
                List<AggregateProducionLossSummaryAndProductionLoss>? listProdLossSummary;
                List<AggregateProducionLossSummaryAndProductionLoss>? prdLossAll;

                if (prod.ProductionLoss != null && prod.ProductionLoss.Count > 0)
                {
                    listProdLossSummary = prod.ProductionLossSummary?
                        .GroupJoin(
                            prod.ProductionLoss,
                            pls => new { pls.FacilityId, pls.SystemId, pls.EquipmentId, pls.WellId },
                            pl => new { pl.FacilityId, pl.SystemId, pl.EquipmentId, pl.WellId },
                            (pls, plGroup) => new { pls, plGroup }
                        )
                        .SelectMany(
                            temp => temp.plGroup.DefaultIfEmpty(),
                            (temp, pl) => new AggregateProducionLossSummaryAndProductionLoss
                            {
                                FacilityId = temp.pls.FacilityId,
                                SystemId = temp.pls.SystemId,
                                EquipmentId = temp.pls.EquipmentId,
                                WellId = temp.pls.WellId,
                                PlannedOilLoss_OFU = temp.pls.PlannedOilLoss_OFU,
                                PlannedOilLoss_SI = temp.pls.PlannedOilLoss_SI,
                                UnplannedOilLoss_OFU = temp.pls.UnplannedOilLoss_OFU,
                                UnplannedOilLoss_SI = temp.pls.UnplannedOilLoss_SI,
                                LossMotiveSummaryId = temp.pls.LossMotiveId,
                                OilLoss_OFU = pl?.OilLoss_OFU ?? 0,
                                OilLoss_SI = pl?.OilLoss_SI ?? 0,
                                LossMotiveId = pl?.LossMotiveId,
                            }
                        ).ToList();

                    var idsPLS = prod.ProductionLossSummary?
                        .Select(a => new { a.FacilityId, a.SystemId, a.EquipmentId, a.WellId })
                        .ToHashSet();

                    var listProdLoss = prod.ProductionLoss?
                        .Where(b => idsPLS != null && !idsPLS.Contains(new { b.FacilityId, b.SystemId, b.EquipmentId, b.WellId }))
                        .Select(b => new AggregateProducionLossSummaryAndProductionLoss
                        {
                            FacilityId = b.FacilityId,
                            SystemId = b.SystemId,
                            EquipmentId = b.EquipmentId,
                            WellId = b.WellId,
                            PlannedOilLoss_OFU = 0,
                            PlannedOilLoss_SI = 0,
                            UnplannedOilLoss_OFU = 0,
                            UnplannedOilLoss_SI = 0,
                            LossMotiveSummaryId = null,
                            OilLoss_OFU = b?.OilLoss_OFU ?? 0,
                            OilLoss_SI = b?.OilLoss_SI ?? 0,
                            LossMotiveId = b?.LossMotiveId,
                        });

                    if (listProdLossSummary != null && listProdLossSummary.Count > 0 && listProdLoss != null)
                    {
                        prdLossAll = [.. listProdLossSummary, .. listProdLoss];
                    }
                    else
                    {
                        prdLossAll = listProdLoss?.ToList();
                    }
                }
                else
                {
                    prdLossAll = prod.ProductionLossSummary?.Select(a =>
                       new AggregateProducionLossSummaryAndProductionLoss
                       {
                           FacilityId = a.FacilityId,
                           SystemId = a.SystemId,
                           EquipmentId = a.EquipmentId,
                           WellId = a.WellId,
                           PlannedOilLoss_OFU = a.PlannedOilLoss_OFU,
                           PlannedOilLoss_SI = a.PlannedOilLoss_SI,
                           UnplannedOilLoss_OFU = a.UnplannedOilLoss_OFU,
                           UnplannedOilLoss_SI = a.UnplannedOilLoss_SI,
                           LossMotiveSummaryId = a.LossMotiveId,
                       }).ToList();
                }

                var prdLossByFacility = prdLossAll?
                    .Where(f => !string.IsNullOrEmpty(f.FacilityId))
                    .GroupBy(f => f.FacilityId)
                    .Select(fg => new LossesByFacility
                    {
                        FacilityId = fg.Key,
                        ProductionLossSummary = new ProductionLossSummary
                        {
                            PlannedOilLoss_OFU = new ValidationResult<decimal> { Value = fg.Sum(x => x.PlannedOilLoss_OFU), IsValid = true },
                            PlannedOilLoss_SI = new ValidationResult<decimal> { Value = fg.Sum(x => x.PlannedOilLoss_SI), IsValid = true },
                            UnplannedOilLoss_OFU = new ValidationResult<decimal> { Value = fg.Sum(x => x.UnplannedOilLoss_OFU), IsValid = true },
                            UnplannedOilLoss_SI = new ValidationResult<decimal> { Value = fg.Sum(x => x.UnplannedOilLoss_SI), IsValid = true },
                            LossMotive = string.Join(";", fg.Select(x => x.LossMotiveSummaryId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveSummaryId)) ? ";" : string.Empty)
                        },
                        ProductionLoss = new ProductionLoss
                        {
                            OilLoss_OFU = new ValidationResult<decimal> { Value = fg.Sum(x => x.OilLoss_OFU), IsValid = true },
                            OilLoss_SI = new ValidationResult<decimal> { Value = fg.Sum(x => x.OilLoss_SI), IsValid = true },
                            LossMotive = string.Join(";", fg.Select(x => x.LossMotiveId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveId)) ? ";" : string.Empty)
                        },
                        LossesBySystems = fg
                            .Where(f => !string.IsNullOrEmpty(f.SystemId))
                            .GroupBy(s => s.SystemId)
                            .Select(sg => new LossesBySystem
                            {
                                SystemId = sg.Key,
                                ProductionLossSummary = new ProductionLossSummary
                                {
                                    PlannedOilLoss_OFU = new ValidationResult<decimal> { Value = sg.Sum(x => x.PlannedOilLoss_OFU), IsValid = true },
                                    PlannedOilLoss_SI = new ValidationResult<decimal> { Value = sg.Sum(x => x.PlannedOilLoss_SI), IsValid = true },
                                    UnplannedOilLoss_OFU = new ValidationResult<decimal> { Value = sg.Sum(x => x.UnplannedOilLoss_OFU), IsValid = true },
                                    UnplannedOilLoss_SI = new ValidationResult<decimal> { Value = sg.Sum(x => x.UnplannedOilLoss_SI), IsValid = true },
                                    LossMotive = string.Join(";", fg.Select(x => x.LossMotiveSummaryId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveSummaryId)) ? ";" : string.Empty)
                                },
                                ProductionLoss = new ProductionLoss
                                {
                                    OilLoss_OFU = new ValidationResult<decimal> { Value = sg.Sum(x => x.OilLoss_OFU), IsValid = true },
                                    OilLoss_SI = new ValidationResult<decimal> { Value = sg.Sum(x => x.OilLoss_SI), IsValid = true },
                                    LossMotive = string.Join(";", fg.Select(x => x.LossMotiveId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveId)) ? ";" : string.Empty)
                                },
                                LossesByEquipments = sg
                                    .Where(f => !string.IsNullOrEmpty(f.EquipmentId))
                                    .GroupBy(e => e.EquipmentId)
                                    .Select(eg => new LossesByEquipment
                                    {
                                        EquipmentId = eg.Key,
                                        ProductionLossSummary = new ProductionLossSummary
                                        {
                                            PlannedOilLoss_OFU = new ValidationResult<decimal> { Value = eg.Sum(x => x.PlannedOilLoss_OFU), IsValid = true },
                                            PlannedOilLoss_SI = new ValidationResult<decimal> { Value = eg.Sum(x => x.PlannedOilLoss_SI), IsValid = true },
                                            UnplannedOilLoss_OFU = new ValidationResult<decimal> { Value = eg.Sum(x => x.UnplannedOilLoss_OFU), IsValid = true },
                                            UnplannedOilLoss_SI = new ValidationResult<decimal> { Value = eg.Sum(x => x.UnplannedOilLoss_SI), IsValid = true },
                                            LossMotive = string.Join(";", fg.Select(x => x.LossMotiveSummaryId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveSummaryId)) ? ";" : string.Empty)
                                        },
                                        ProductionLoss = new ProductionLoss
                                        {
                                            OilLoss_OFU = new ValidationResult<decimal> { Value = eg.Sum(x => x.OilLoss_OFU), IsValid = true },
                                            OilLoss_SI = new ValidationResult<decimal> { Value = eg.Sum(x => x.OilLoss_SI), IsValid = true },
                                            LossMotive = string.Join(";", fg.Select(x => x.LossMotiveId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveId)) ? ";" : string.Empty)
                                        },
                                        LossesByWells = eg
                                            .Where(f => !string.IsNullOrEmpty(f.WellId))
                                            .GroupBy(w => w.WellId)
                                            .Select(wg => new LossesByWell
                                            {
                                                WellId = wg.Key,
                                                ProductionLossSummary = new ProductionLossSummary
                                                {
                                                    PlannedOilLoss_OFU = new ValidationResult<decimal> { Value = wg.Sum(x => x.PlannedOilLoss_OFU), IsValid = true },
                                                    PlannedOilLoss_SI = new ValidationResult<decimal> { Value = wg.Sum(x => x.PlannedOilLoss_SI), IsValid = true },
                                                    UnplannedOilLoss_OFU = new ValidationResult<decimal> { Value = wg.Sum(x => x.UnplannedOilLoss_OFU), IsValid = true },
                                                    UnplannedOilLoss_SI = new ValidationResult<decimal> { Value = wg.Sum(x => x.UnplannedOilLoss_SI), IsValid = true },
                                                    LossMotive = string.Join(";", fg.Select(x => x.LossMotiveSummaryId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveSummaryId)) ? ";" : string.Empty)
                                                },
                                                ProductionLoss = new ProductionLoss
                                                {
                                                    OilLoss_OFU = new ValidationResult<decimal> { Value = wg.Sum(x => x.OilLoss_OFU), IsValid = true },
                                                    OilLoss_SI = new ValidationResult<decimal> { Value = wg.Sum(x => x.OilLoss_SI), IsValid = true },
                                                    LossMotive = string.Join(";", fg.Select(x => x.LossMotiveId).Where(id => !string.IsNullOrEmpty(id)).Distinct()) + (fg.Any(x => !string.IsNullOrEmpty(x.LossMotiveId)) ? ";" : string.Empty)
                                                }
                                            }).ToList(),

                                    }).ToList(),

                            }).ToList(),

                    }).ToList();

                report.LossesByFacilities = prdLossByFacility ?? new List<LossesByFacility>();


            });

            #endregion Validations For ReportValidation
        }

        private bool AreValidValues(List<decimal> values, decimal margin = 0.01m)
        {
            for (int i = 0; i < values.Count; i++)
            {
                for (int j = i + 1; j < values.Count; j++)
                {
                    decimal reference = values[i];
                    decimal toCompare = values[j];

                    decimal allowedDifference = Math.Round(reference * margin, 7);
                    decimal actualDifference = Math.Abs(Math.Round(reference - toCompare, 7));

                    if (actualDifference > allowedDifference)
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        private void SetIsValidToFalse(object obj, string propertyName)
        {
            var prop = obj.GetType().GetProperty(propertyName);

            var validationResultObj = prop?.GetValue(obj);

            if (validationResultObj != null)
            {
                var isValidProp = validationResultObj.GetType().GetProperty("IsValid");

                isValidProp?.SetValue(validationResultObj, false);
            }
        }

        private void SetValidationResultForProperty<T>(Object obj, string propName, T newValue, bool isValid = true)
        {
            var propInfo = obj.GetType().GetProperty(propName);
            if (propInfo != null)
            {
                var validationResult = propInfo.GetValue(obj);
                if (validationResult == null)
                {
                    var validationResultType = typeof(ValidationResult<>).MakeGenericType(propInfo.PropertyType.GetGenericArguments());
                    validationResult = Activator.CreateInstance(validationResultType);
                    propInfo.SetValue(obj, validationResult);
                }

                var valueProperty = validationResult.GetType().GetProperty("Value");
                var isValidProperty = validationResult.GetType().GetProperty("IsValid");

                if (valueProperty != null && isValidProperty != null)
                {
                    valueProperty.SetValue(validationResult, newValue);
                    isValidProperty.SetValue(validationResult, isValid);
                }
            }
        }


    }

}
