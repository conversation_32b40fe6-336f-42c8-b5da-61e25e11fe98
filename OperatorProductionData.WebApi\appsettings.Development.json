{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Debug"
    }
  },
  "SwaggerConfigs": {
    "SwaggerUi": true,
    "Scopes": [ "operator_api" ],
    "Title": "Operator Prodution Data",
    "Description": "An external API used by oil & gas Block Operators to submit the Production data into SMP.",
    "Versions": [ 1 ],
    "AuthorityUrl": "https://smp.aeeconomics.com/identity/sts",
    //    "BasePath": "/api/operator/prod-management"
    "BasePath": "",
    "EnableRequestResponseLogging": true
  },
  "ConnectionStrings": {
    //"OperatorProductionDataAuditDB": "Server=localhost,7900;Database=SMP.OperatorProductionAudit;User ID=sa;Password=Password_123;Trust Server Certificate=true",
    //"OperatorProductionDataMongoDB": "************************************************",
    "OperatorProductionDataMongoDB": "************************************************",
    //"OperatorProductionDataMongoDB": "mongodb://localhost:27017",
    //"OperatorProductionDataMongoDB": "****************************************************",
    "OperatorProductionDataDBName": "OperatorProductionDataDB"
  },
  "FileServiceBaseURL": "https://smp.aeeconomics.com/api/fileManager/",
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug", //Verbose
      "Override": {
        "Microsoft.EntityFrameworkCore": "Information",
        "Microsoft.AspNetCore": "Warning",
        "Microsoft": "Information",
        "System": "Information",
        "System.Net.Http.HttpClient": "Warning",
        "MongoDB.Command": "Debug"
      }
    },
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "File",
        "Args": {
          "buffered": true,
          "flushToDiskInterval": 15,
          "outputTemplate": "[{Timestamp:o}][{Level:u4}][{ThreadId}][{SourceContext}] {Message}{NewLine}{Exception}",
          "pathFormat": "Logs//log-{Date}.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 3,
          "textFormatter": "Serilog.Formatting.Json.JsonFormatter, Serilog"
        }
      },
      {
        "Name": "MongoDBBson",
        "Args": {
          "databaseUrl": "mongodb://localhost:27017/OperatorProductionDataDB?authSource=admin",
          "collectionName": "logs",
          "cappedMaxSizeMb": "1024",
          "cappedMaxDocuments": "50000",
          "rollingInterval": "Day"
        }
      },
      {
        "Name": "Elasticsearch",
        "Args": {
          "nodeUris": "http://ae-photon-01:9200",
          "indexFormat": "log-smp-api-upstream-{0:yyyy.MM}"
        }
      }
    ]
  }
}
