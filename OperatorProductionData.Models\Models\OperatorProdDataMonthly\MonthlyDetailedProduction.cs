﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyDetailedProduction
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? FieldId { get; set; }
        public string? ReservoirId { get; set; }
        public string? WellId { get; set; }
        public decimal ExpectedOilProd_OFU { get; set; }
        public decimal ExpectedOilProd_SI { get; set; }
        public decimal MonthOilProd_OFU { get; set; }
        public decimal MonthOilProd_SI { get; set; }
        public decimal YearOilProd_OFU { get; set; }
        public decimal YearOilProd_SI { get; set; }
        public decimal MonthWaterProd_OFU { get; set; }
        public decimal MonthWaterProd_SI { get; set; }
        public decimal YearWaterProd_OFU { get; set; }
        public decimal YearWaterProd_SI { get; set; }
        public decimal MonthGasProd_OFU { get; set; }
        public decimal MonthGasProd_SI { get; set; }
        public decimal YearGasProd_OFU { get; set; }
        public decimal YearGasProd_SI { get; set; }
        public decimal MonthGasLift_OFU { get; set; }
        public decimal MonthGasLift_SI { get; set; }
        public decimal YearGasLift_OFU { get; set; }
        public decimal YearGasLift_SI { get; set; }
        public decimal DailyOilRate_OFU { get; set; }
        public decimal DailyOilRate_SI { get; set; }
        public decimal DailyGasRate_OFU { get; set; }
        public decimal DailyGasRate_SI { get; set; }
        public decimal DailyWaterRate_OFU { get; set; }
        public decimal DailyWaterRate_SI { get; set; }
        public decimal MonthOperationDays { get; set; }
        public decimal YearOperationDays { get; set; }
        public decimal? Choke { get; set; }
        public decimal? GasLiftChoke { get; set; }
        public decimal? WaterCut { get; set; }
        public decimal GasOilRatio_OFU { get; set; }
        public decimal GasOilRatio_SI { get; set; }
        public decimal? WellHeadPressure_OFU { get; set; }
        public decimal? WellHeadPressure_SI { get; set; }
        public decimal? WellHeadTemperature_OFU { get; set; }
        public decimal? WellHeadTemperature_SI { get; set; }
        public decimal? BottomHolePressure_OFU { get; set; }
        public decimal? BottomHolePressure_SI { get; set; }
        public decimal? BottomHoleTemperature_OFU { get; set; }
        public decimal? BottomHoleTemperature_SI { get; set; }
        public decimal? DownholeGasificationPressure_OFU { get; set; }
        public decimal? DownholeGasificationPressure_SI { get; set; }
        public decimal? DownholeGasificationTemperature_OFU { get; set; }
        public decimal? DownholeGasificationTemperature_SI { get; set; }
        public decimal? CasingPressure_OFU { get; set; }
        public decimal? CasingPressure_SI { get; set; }
        public decimal? CasingTemperature_OFU { get; set; }
        public decimal? CasingTemperature_SI { get; set; }
        public decimal? FlowingTubingPressure_OFU { get; set; }
        public decimal? FlowingTubingPressure_SI { get; set; }
        public decimal? FlowingTubingTemperature_OFU { get; set; }
        public decimal? FlowingTubingTemperature_SI { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public decimal? YearGasExported_OFU { get; set; }
        public decimal? YearGasExported_SI { get; set; }
        public decimal? YearGasFlared_OFU { get; set; }
        public decimal? YearGasFlared_SI { get; set; }
        public decimal? YearGasImport_OFU { get; set; }
        public decimal? YearGasImport_SI { get; set; }
        public decimal? YearGasFuel_OFU { get; set; }
        public decimal? YearGasFuel_SI { get; set; }
        public decimal? MonthGasExported_OFU { get; set; }
        public decimal? MonthGasExported_SI { get; set; }
        public decimal? MonthGasFlared_OFU { get; set; }
        public decimal? MonthGasFlared_SI { get; set; }
        public decimal? MonthGasImport_OFU { get; set; }
        public decimal? MonthGasImport_SI { get; set; }
        public decimal? MonthGasFuel_OFU { get; set; }
        public decimal? MonthGasFuel_SI { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }

    }
}
