﻿using Ae.Stratus.Core.Backend.NoSQL.Base;
using MongoDB.Driver;

namespace OperatorProductionData.Services.Services
{
    public class BaseRepository<TModel> : RepositoryBaseMongoDB<TModel>
    {
        public BaseRepository(string connectionString, string databaseName, string collection) : base(connectionString, databaseName, collection)
        {
        }

        public BaseRepository(IMongoCollection<TModel> container) : base (container)
        {

        }

    }
}
