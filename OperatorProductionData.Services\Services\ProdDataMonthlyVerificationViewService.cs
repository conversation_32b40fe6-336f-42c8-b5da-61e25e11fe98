﻿using Ae.Stratus.Core.Common.GridDataLoad;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataMonthlyVerificationViewService : BaseRepository<ProdDataMonthlyVerificationView>
    {
        public ProdDataMonthlyVerificationViewService(IMongoCollection<ProdDataMonthlyVerificationView> container) : base(container)
        {
        }

        /// <summary>
        /// Method to get All ProdDataMonthly's for the given ProductionDate (Used in Daily Reports Methods)
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ICollection<ProdDataMonthlyVerificationView>> GetAllByProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            if (productionYearStart == productionYearEnd)
            {
                return await GetAllByYearProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
            }
            else
            {
                var prodDataMonthlyVerificationViewLilst = new List<ProdDataMonthlyVerificationView>();
                var referenceYear = productionYearStart;
                while (referenceYear != productionYearEnd)
                {
                    if (referenceYear == productionYearStart)
                    {
                        prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(productionMonthStart, 12, productionYearStart, referenceYear));
                    }
                    else
                    {
                        if (referenceYear == productionMonthEnd)
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, productionMonthEnd, referenceYear, productionYearEnd));
                        }
                        else
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, 12, referenceYear, referenceYear));
                        }
                    }

                    referenceYear++;
                }

                return prodDataMonthlyVerificationViewLilst;
            }
        }

        /// <summary>
        /// Method to get All ProdDataMonthly's for the given ProductionDate range and blocks list (Similar to Daily Reports Methods)
        /// </summary>
        /// <param name="productionMonthStart">Starting month (1-12)</param>
        /// <param name="productionMonthEnd">Ending month (1-12)</param>
        /// <param name="productionYearStart">Starting year</param>
        /// <param name="productionYearEnd">Ending year</param>
        /// <param name="blockIds">Optional array of block IDs to filter by</param>
        /// <returns>Collection of ProdDataMonthlyVerificationView</returns>
        public async Task<ICollection<ProdDataMonthlyVerificationView>> GetAllByProductionDateRange(
            int productionMonthStart,
            int productionMonthEnd,
            int productionYearStart,
            int productionYearEnd,
            string[] blockIds = null
        )
        {
            var filterBuilder = Builders<ProdDataMonthlyVerificationView>.Filter;
            FilterDefinition<ProdDataMonthlyVerificationView> filter;

            // Build date range filter based on whether it spans multiple years
            if (productionYearStart == productionYearEnd)
            {
                // Same year - simple month range filter
                filter = filterBuilder.Eq(x => x.ProductionYear, productionYearStart)
                    & filterBuilder.Gte(x => x.ProductionMonth, productionMonthStart)
                    & filterBuilder.Lte(x => x.ProductionMonth, productionMonthEnd);
            }
            else
            {
                // Multiple years - complex date range filter
                filter = filterBuilder.Or(
                    // First year: from start month to December
                    filterBuilder.And(
                        filterBuilder.Eq(x => x.ProductionYear, productionYearStart),
                        filterBuilder.Gte(x => x.ProductionMonth, productionMonthStart)
                    ),
                    // Middle years: all months
                    filterBuilder.And(
                        filterBuilder.Gt(x => x.ProductionYear, productionYearStart),
                        filterBuilder.Lt(x => x.ProductionYear, productionYearEnd)
                    ),
                    // Last year: from January to end month
                    filterBuilder.And(
                        filterBuilder.Eq(x => x.ProductionYear, productionYearEnd),
                        filterBuilder.Lte(x => x.ProductionMonth, productionMonthEnd)
                    )
                );
            }

            // Add block filter if provided
            if (blockIds is not null && blockIds.Length > 0)
            {
                filter = filter & filterBuilder.In(x => x.BlockId, blockIds);
            }

            var prodMonthly = await GetAll(filter);

            return [.. prodMonthly
                    .OrderByDescending(x => x.ProductionYear)
                        .ThenByDescending(x => x.ProductionMonth)
                        .ThenBy(x => x.BlockId)];
        }

        private async Task<ICollection<ProdDataMonthlyVerificationView>> GetAllByYearProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            var filteringOptions = new List<FilteringOptions>
                {
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "ProductionYear",
                        Value = productionYearStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "ProductionYear",
                        Value = productionYearEnd,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "ProductionMonth",
                        Value = productionMonthStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "ProductionMonth",
                        Value = productionMonthEnd,
                    }
                };

            /*var sortingOptions = new List<SortingOptions>
            {
                new SortingOptions
                {
                    PropertyName = "ProductionYear",
                    IsAscending = false
                },
                new SortingOptions
                {
                    PropertyName = "ProductionMonth",
                    IsAscending = false
                },
                new SortingOptions
                {
                    PropertyName = "ComplexId",
                    IsAscending = false
                }
            };*/

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions,
                //SortingOptions = sortingOptions,
                PageIndex = 0,
                PageSize = 0,
            };

            return (await GetSortedFilteredPagedList(options)).Models;
        }

        //public async Task<IEnumerable<MonthlyDashboardData>> GetMonthlyDashboardData(ICollection<string> blockIds, bool useActualDate = true)
        //{

        //    int nMonthsAgo = 1;   //TODO :: the number of months should be parametrized
        //    //Get Database Records
        //    var initialbaseDate = DateTime.UtcNow.AddMonths(-nMonthsAgo);
        //    var finalBaseDate = useActualDate == true ? DateTime.UtcNow : DateTime.UtcNow.AddMonths(-1);

        //    IEnumerable<ProdDataMonthly> result;

        //    List<MonthlyDashboardData> returnList = new List<MonthlyDashboardData>();

        //    if (DateTime.UtcNow.Year > initialbaseDate.Year)
        //    {
        //        returnList =
        //            (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
        //                                            && ((x.ProductionMonth >= initialbaseDate.Month && x.ProductionYear == initialbaseDate.Year) ||
        //                                                (x.ProductionYear > initialbaseDate.Year && x.ProductionYear < DateTime.UtcNow.Year) ||
        //                                                (x.ProductionYear == DateTime.UtcNow.Year && x.ProductionMonth <= finalBaseDate.Month))
        //                                            //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
        //                                            )
        //            ).ToList()
        //            .GroupBy(x => new { x.BlockId, x.ProductionMonth, x.ProductionYear })
        //            .Select(r => new MonthlyDashboardData
        //            {
        //                BlockId = r.Key.BlockId,
        //                Month = r.Key.ProductionMonth,
        //                Year = r.Key.ProductionYear,
        //                GasProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
        //                OilProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
        //                WaterProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
        //            }).ToList();
        //    }
        //    else
        //    {
        //        returnList =
        //            (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
        //                                            && x.ProductionMonth >= initialbaseDate.Month
        //                                            && (useActualDate == true ? x.ProductionMonth <= DateTime.UtcNow.Month : x.ProductionMonth < DateTime.UtcNow.Month)
        //                                            && x.ProductionYear == initialbaseDate.Year
        //                                            //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
        //                                            )
        //            )
        //            .ToList()
        //            .GroupBy(x => new { x.BlockId, x.ProductionMonth, x.ProductionYear })
        //            .Select(r => new MonthlyDashboardData
        //            {
        //                BlockId = r.Key.BlockId,
        //                Month = r.Key.ProductionMonth,
        //                Year = r.Key.ProductionYear,
        //                GasProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
        //                OilProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
        //                WaterProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
        //            }).ToList();
        //    }
        //    this.FillMonthlyMissingValues(blockIds, initialbaseDate, finalBaseDate, returnList, useActualDate);

        //    return returnList.OrderBy(x => x.BlockId);
        //}

        public async Task<IEnumerable<QuarterlyDashboardData>> GetQuarterlyDashboardData(ICollection<string> blockIds, bool useActualQuarter = true)
        {
            int nQuaterlyAgo = 1;   //TODO :: the number of months should be parametrized
            int nQuaterlyAgoMod = nQuaterlyAgo % 4;
            //Get Database Records
            var actualQuater = (int)Math.Ceiling(DateTime.UtcNow.Month / 3M);
            int finalQuater;
            int initialQuarter;
            int finalQuarterYear;
            int initialQuarterYear;

            if (useActualQuarter)
            {
                finalQuater = actualQuater;
                finalQuarterYear = DateTime.UtcNow.Year;
            }
            else
            {
                finalQuater = actualQuater != 1 ? (int)Math.Ceiling(DateTime.UtcNow.Month / 3M) - 1 : 4;
                finalQuarterYear = actualQuater != 1 ? DateTime.UtcNow.Year : DateTime.UtcNow.Year - 1;
            }

            initialQuarter = finalQuater - nQuaterlyAgoMod > 0 ? finalQuater - nQuaterlyAgoMod : 4 - (finalQuater - nQuaterlyAgoMod);
            initialQuarterYear = (finalQuater > initialQuarter ? DateTime.UtcNow.Year : DateTime.UtcNow.Year - 1) - (int)(nQuaterlyAgo / 4);

            int initialQuaterMonth = ((initialQuarter - 1) * 3) + 1;
            var finalQuaterMonth = finalQuater * 3;

            var initialBaseDate = new DateTime(initialQuarterYear, initialQuaterMonth, 1);
            var finalBaseDate = new DateTime(finalQuarterYear, finalQuaterMonth, 1);

            List<QuarterlyDashboardData> returnList = new List<QuarterlyDashboardData>();

            if (finalBaseDate.Year > initialBaseDate.Year)
            {
                returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && ((x.ProductionMonth >= initialBaseDate.Month && x.ProductionYear == initialBaseDate.Year) ||
                                                        (x.ProductionYear > initialBaseDate.Year && x.ProductionYear < finalBaseDate.Year) ||
                                                        (x.ProductionYear == finalBaseDate.Year && x.ProductionMonth <= finalBaseDate.Month))
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList()
                    .Select(x => new
                    {
                        x.BlockId,
                        ProductionQuarter = (int)Math.Ceiling(x.ProductionMonth / 3M),
                        x.ProductionYear,
                        x.Json.OperationSummaryByField,
                    })
                    .GroupBy(x => new { x.BlockId, x.ProductionQuarter, x.ProductionYear }).Select(r => new QuarterlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Quarter = r.Key.ProductionQuarter,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();
            }
            else
            {
                returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && x.ProductionMonth >= initialBaseDate.Month
                                                    && x.ProductionMonth <= finalBaseDate.Month
                                                    && x.ProductionYear == finalBaseDate.Year
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList()
                    .Select(x => new
                    {
                        x.BlockId,
                        ProductionQuarter = (int)Math.Ceiling(x.ProductionMonth / 3M),
                        x.ProductionYear,
                        x.Json.OperationSummaryByField,
                    })
                    .GroupBy(x => new { x.BlockId, x.ProductionQuarter, x.ProductionYear }).Select(r => new QuarterlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Quarter = r.Key.ProductionQuarter,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();
            }
            this.FillQuarterlyMissingValues(blockIds, initialBaseDate, finalBaseDate, returnList, useActualQuarter);

            return returnList.OrderBy(x => x.BlockId);
        }

        public async Task<IEnumerable<YearlyDashboardData>> GetYearlyDashboardData(ICollection<string> blockIds, bool useActualDate = true)
        {

            int nMonthsAgo = 1;   //TODO :: the number of months should be parametrized
            //Get Database Records
            var initialBaseDate = DateTime.UtcNow.AddYears(-nMonthsAgo);
            var finalBaseDate = useActualDate == true ? DateTime.UtcNow : DateTime.UtcNow.AddYears(-1);

            IEnumerable<ProdDataMonthly> result;

            List<YearlyDashboardData> returnList = new List<YearlyDashboardData>();

            returnList =
                    (await _container.FindAsync(x => blockIds.Contains(x.BlockId)
                                                    && x.ProductionYear >= initialBaseDate.Year
                                                    && (useActualDate == true ? x.ProductionYear <= DateTime.UtcNow.Year : x.ProductionYear < DateTime.UtcNow.Year)
                                                    //&& x.Active //TODO :: THIS HAS TO BE REVIEWED
                                                    )
                    ).ToList().GroupBy(x => new { x.BlockId, x.ProductionYear }).Select(r => new YearlyDashboardData
                    {
                        BlockId = r.Key.BlockId,
                        Year = r.Key.ProductionYear,
                        GasProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.GasProd_OFU)),
                        OilProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.ActualOilProd_OFU)),
                        WaterProd = r.Sum(x => x.Json.OperationSummaryByField.Sum(f => f.WaterProd_OFU)),
                    }).ToList();

            this.FillYearlyMissingValues(blockIds, initialBaseDate, finalBaseDate, returnList, useActualDate);

            return returnList.OrderBy(x => x.BlockId);
        }

        /// <summary>
        /// Method to get check if there are no entries on OPD for the givem Blocks on a certain Date
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ICollection<string>> GetBlocksForGivenYearMonth(
            int productionYear,
            int productionMonth
        )
        {
            var filterBuilder = Builders<ProdDataMonthlyVerificationView>.Filter;
            var filter = filterBuilder.Eq(x => x.ProductionYear, productionYear)
                & filterBuilder.Eq(x => x.ProductionMonth, productionMonth);
            ;

            //var prodDaily = await GetAll(filter);
            return await _container.Find(filter)
                             .Project(x => x.BlockId)
                             .ToListAsync();
        }

        private void FillMonthlyMissingValues(ICollection<string> blockIds, DateTime initialBaseDate, DateTime finalBaseDate, List<MonthlyDashboardData> returnList, bool useActualDate)
        {
            while ((initialBaseDate.Month <= finalBaseDate.Month && initialBaseDate.Year == finalBaseDate.Year) ||
                    (initialBaseDate.Month > finalBaseDate.Month && initialBaseDate.Year < finalBaseDate.Year))
                {
                var valuesList = blockIds.Select(blockId => new
                {
                    BlockId = blockId,
                    initialBaseDate.Month,
                    initialBaseDate.Year,
                }).Except(
                returnList.Select(foundValue => new
                {
                    foundValue.BlockId,
                    foundValue.Month,
                    foundValue.Year,
                })).ToList().Select(value => new MonthlyDashboardData
                {
                    Year = value.Year,
                    Month = value.Month,
                    BlockId = value.BlockId,
                });

                returnList.AddRange(valuesList);
                initialBaseDate = initialBaseDate.AddMonths(1);
            }
        }

        private void FillQuarterlyMissingValues(ICollection<string> blockIds, DateTime initialBaseDate, DateTime finalBaseDate, List<QuarterlyDashboardData> returnList, bool useActualDate)
        {
            while ((initialBaseDate.Month <= finalBaseDate.Month && initialBaseDate.Year == finalBaseDate.Year) ||
                (initialBaseDate.Month > finalBaseDate.Month && initialBaseDate.Year < finalBaseDate.Year))
            {
                var valuesList = blockIds.Select(blockId => new
                {
                    BlockId = blockId,
                    Quarter = (int)Math.Ceiling(initialBaseDate.Month / 3M),
                    initialBaseDate.Year,
                }).Except(
                returnList.Select(foundValue => new
                {
                    foundValue.BlockId,
                    foundValue.Quarter,
                    foundValue.Year,
                })).ToList().Select(value => new QuarterlyDashboardData
                {
                    Year = value.Year,
                    Quarter = value.Quarter,
                    BlockId = value.BlockId,
                });

                returnList.AddRange(valuesList);
                initialBaseDate = initialBaseDate.AddMonths(3);
            }
        }

        private void FillYearlyMissingValues(ICollection<string> blockIds, DateTime initialBaseDate, DateTime finalBaseDate, List<YearlyDashboardData> returnList, bool useActualDate)
        {
            while (initialBaseDate.Year <= finalBaseDate.Year)
            {
                var valuesList = blockIds.Select(blockId => new
                {
                    BlockId = blockId,
                    initialBaseDate.Year,
                }).Except(
                returnList.Select(foundValue => new
                {
                    foundValue.BlockId,
                    foundValue.Year,
                })).ToList().Select(value => new YearlyDashboardData
                {
                    Year = value.Year,
                    BlockId = value.BlockId,
                });

                returnList.AddRange(valuesList);
                initialBaseDate = initialBaseDate.AddYears(1);
            }
        }
    }
}