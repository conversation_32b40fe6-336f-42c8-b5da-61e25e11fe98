﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations;
using OperatorProductionData.Models.Models.Overview;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataDailyWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataDailyWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> SubmitProdDataDaily(ReferenceData infrastructure, ProdDataDaily prodDataDaily)
        {
            var obj = new InfrastructureProdDataDaily
            {
                Infrastructure = infrastructure,
                ProdDataDaily = prodDataDaily
            };

            HttpResponseMessage response = await _client.PostAsJsonAsync("proddatadaily/SubmitProdDataDaily", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error adding model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }
            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error adding model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> ChangeProdDataDaily(ReferenceData infrastructure, ProdDataDaily prodDataDaily)
        {
            var obj = new InfrastructureProdDataDaily
            {
                Infrastructure = infrastructure,
                ProdDataDaily = prodDataDaily
            };

            HttpResponseMessage response = await _client.PutAsJsonAsync("proddatadaily/ChangeProdDataDaily", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error changing model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }
            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error changing model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataDaily>>> GetList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatadaily/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataDaily>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataDaily>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataDaily>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataDaily>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataDailyView>>> GetViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatadaily/getviewlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataDailyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataDailyView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataDailyView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataDailyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataDailyViewDetails>>> GetViewDetailsList(string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getviewdetailslist");
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataDailyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataDailyViewDetails>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataDailyViewDetails>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataDailyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<ProductionDashboardDto>>> GetDailyDashboardData(
            string productionDateStart,
            string productionDateEnd,
            string[] blockIds
        )
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionDateStart", productionDateStart },
                { "productionDateEnd", productionDateEnd },
                { "blockIds", blockIds }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getdailydashboarddata" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<ProductionDashboardDto>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting daily dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<ProductionDashboardDto>> apiResponse = await httpResponseMessage
                                            .Content
                                            .ReadFromJsonAsync<ApiResponse<IEnumerable<ProductionDashboardDto>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<ProductionDashboardDto>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily dashboard list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataDailyGeneric>> GetProdDataDailyViewJson(Guid id)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "id", id.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getproddatadailyviewjson" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataDailyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting daily dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataDailyGeneric> apiResponse = await httpResponseMessage
                                            .Content
                                            .ReadFromJsonAsync<ApiResponse<ProdDataDailyGeneric>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataDailyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily dashboard list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<OverviewDailyData>>> GetDailyOverviewData(
                    string overviewDataType,
                    string[] blockIds,
                    string[] oilGasFieldIds
        )
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "overviewDataType", overviewDataType },
                { "blockIds", blockIds },
                { "oilGasFieldIds", oilGasFieldIds }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getdailyoverviewdata" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<OverviewDailyData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting daily Overview data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<OverviewDailyData>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<OverviewDailyData>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<OverviewDailyData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily Overview data list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataInfo?>> GetByBlockAndProductionDate(
            string blockId,
            string productionDate
        )
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "blockId", blockId },
                { "productionDate", productionDate }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getbyblockandproductiondate" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataInfo?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetByBlockAndProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataInfo?> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ProdDataInfo?>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataInfo?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily GetByBlockAndProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataDaily>>> GetAllByProductionDate(string productionDate)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionDate", productionDate }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getallbyproductiondate" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataDaily>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetAllByProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataDaily>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataDaily>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataDaily>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily GetAllByProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataDailyView>> GetById(string blockId, string productionDate)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "blockId", blockId },
                { "productionDate", productionDate }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getbyid" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataDailyView>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting ProdDataDailyWebApiService GetById",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataDailyView> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ProdDataDailyView>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataDailyView>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting ProdDataDailyWebApiService GetById",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }


        public virtual async Task<ApiResponse<ICollection<ProdDataDailyValidationReport>>> GetValidationReport(string productionDate)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionDate", productionDate }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatadaily/getvalidationreport" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataDailyValidationReport>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetValidationReport",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataDailyValidationReport>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataDailyValidationReport>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataDailyValidationReport>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily GetValidationReport",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }
    }
}
