﻿namespace OperatorProductionData.Models.Utilities
{
    public static class ValidationExtensions
    {
        public static ValidationResult<T?> ToNullable<T>(this ValidationResult<T> input) where T : struct
        {
            return new ValidationResult<T?>
            {
                Value = input.IsValid ? (T?)input.Value : null,
                IsValid = input.IsValid
            };
        }
    }
}
