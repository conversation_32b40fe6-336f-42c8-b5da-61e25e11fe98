{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Configurations": {"SwaggerUi": true, "EnableRequestResponseLogging": true}, "ConnectionStrings": {"OperatorProductionDataAuditDB": "Server=localhost,7900;Database=SMP.OperatorProductionAudit;User ID=sa;Password=Password_123;Trust Server Certificate=true", "OperatorProductionDataMongoDB": "************************************************", "OperatorProductionDataDBName": "OperatorProductionDataDB"}}