﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class ProdDataMonthly : IVersionable
    {
        [Key]
        public Guid Id { get; set; }
        public string BlockId { get; set; }
        public int ProductionMonth { get; set; }
        public int ProductionYear { get; set; }
        public string? OperatorReference { get; set; }
        public ICollection<MonthlyOperationSummaryByFacility> OperationSummaryByFacility { get; set; }
        public ICollection<MonthlyOperationSummaryByField> OperationSummaryByField { get; set; }
        public ICollection<MonthlyWaterQuality> WaterQuality { get; set; }
        public ICollection<MonthlyCommodityConsumptionSummary> CommodityConsumptionSummary { get; set; }
        public ICollection<MonthlyOperationComments>? OperationComments { get; set; }
        public ICollection<MonthlyDetailedProduction> DetailedProduction { get; set; }
        public ICollection<MonthlyDetailedGasInjection>? DetailedGasInjection { get; set; }
        public ICollection<MonthlyDetailedWaterInjection>? DetailedWaterInjection { get; set; }
        public ICollection<MonthlyOilStorageSummary>? OilStorageSummary { get; set; }
        public ICollection<MonthlyOilStorageByPartner>? OilStorageByPartner { get; set; }
        public ICollection<MonthlyLiftingDetail>? LiftingDetail { get; set; }
        public ICollection<MonthlySubmittedDocuments>? SubmittedDocuments { get; set; }
        public ICollection<MonthlyGasProduction>? MonthlyLPGProduction { get; set; }
        public ICollection<MonthlyGasShipping>? MonthlyLPGShippingTotalByMonth { get; set; }
        public ICollection<GasProductionYTD>? YtdLPGProduction { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public string ReasonId { get; set; }
        public string ReasonDescription { get; set; }
        public Guid VersionsCollectionId { get; set; }
        public bool Active { get; set; } = true; //TODO :: THIS HAS TO COME FROM STRATUS IVersionable Interface
        public int Version { get; set; }
    }
}
