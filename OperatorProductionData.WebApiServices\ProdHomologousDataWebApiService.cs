﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Middleware.Base;
using OperatorProductionData.Models.Models.ManualData;
using System.Net.Http.Json;
using System.Net;
using Microsoft.AspNetCore.Http.Extensions;

namespace OperatorProductionData.WebApiServices
{
    public class ProdHomologousDataWebApiService : NoSqlWebApiServiceBase<ProdHomologousData>
    {
        public ProdHomologousDataWebApiService(string BaseURL) : base(BaseURL, "prodhomologousdata")
        {

        }

        public virtual async Task<ApiResponse<bool>> AddBulk(List<ProdDataInitialMetric> prodDataInitialMetrics)
        {
            HttpResponseMessage response = await Client.PostAsJsonAsync("prodhomologousdata/addbulk", prodDataInitialMetrics);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<bool>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error adding model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }
            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<bool>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error adding model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public async Task<ApiResponse<bool>> DeleteObj(object id, string accessToken = "")
        {
            Client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                Client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage response = await Client.DeleteAsync($"{EndPointAddress}/delete?id={id}");
            if (response.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<bool>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = (int)response.StatusCode,
                        Title = "Error deleting model",
                        Detail = response.ReasonPhrase
                    }
                }
                };
            }

            ApiResponse<bool> apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>();
            if (apiResponse == null)
            {
                return new ApiResponse<bool>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Title = "Error deleting model",
                        Detail = "Null response"
                    }
                }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdHomologousData>>> GetAllByProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            Client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionMonthStart", productionMonthStart.ToString() },
                { "productionMonthEnd", productionMonthEnd.ToString() },
                { "productionYearStart", productionYearStart.ToString() },
                { "productionYearEnd", productionYearEnd.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await Client.GetAsync("prodhomologousdata/getallbydaterange" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdHomologousData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetAllByDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdHomologousData>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdHomologousData>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdHomologousData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting monthly GetAllByProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }
    }
}
