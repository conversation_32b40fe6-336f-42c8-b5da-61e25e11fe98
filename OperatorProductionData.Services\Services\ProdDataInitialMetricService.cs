﻿using Ae.Stratus.Core.Backend.NoSQL.Base;
using Ae.Stratus.Core.Common.GridDataLoad;
using OperatorProductionData.Models.Models.ManualData;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataInitialMetricService : RepositoryBaseMongoDB<ProdDataInitialMetric>
    {
        public ProdDataInitialMetricService(string connectionString, string databaseName) : base(connectionString, databaseName, "ProdDataInitialMetric")
        {
        }

        public async Task<bool> AddBulk(List<ProdDataInitialMetric> validationRules)
        {
            await _container.InsertManyAsync(validationRules);

            return true;
        }

        public async Task<ICollection<ProdDataInitialMetric>> GetAllByProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            if (productionYearStart == productionYearEnd)
            {
                return await GetAllByYearProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
            }
            else
            {
                var prodDataMonthlyVerificationViewLilst = new List<ProdDataInitialMetric>();
                var referenceYear = productionYearStart;
                while (referenceYear != productionYearEnd)
                {
                    if (referenceYear == productionYearStart)
                    {
                        prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(productionMonthStart, 12, productionYearStart, referenceYear));
                    }
                    else
                    {
                        if (referenceYear == productionMonthEnd)
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, productionMonthEnd, referenceYear, productionYearEnd));
                        }
                        else
                        {
                            prodDataMonthlyVerificationViewLilst.AddRange(await GetAllByYearProductionDateRange(1, 12, referenceYear, referenceYear));
                        }
                    }

                    referenceYear++;
                }

                return prodDataMonthlyVerificationViewLilst;
            }
        }

        private async Task<ICollection<ProdDataInitialMetric>> GetAllByYearProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            var filteringOptions = new List<FilteringOptions>
                {
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "Year",
                        Value = productionYearStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "Year",
                        Value = productionYearEnd,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "Month",
                        Value = productionMonthStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "Month",
                        Value = productionMonthEnd,
                    }
                };

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions,
                //SortingOptions = sortingOptions,
                PageIndex = 0,
                PageSize = 0,
            };

            return (await GetSortedFilteredPagedList(options)).Models;
        }
    }
}
