﻿using Ae.Stratus.Core.Common.GridDataLoad;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.Overview;
using OperatorProductionData.Services.Enums;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataGasDailyVerificationViewService : BaseRepository<ProdDataGasDailyVerificationView>
    {
        public ProdDataGasDailyVerificationViewService(IMongoCollection<ProdDataGasDailyVerificationView> container) : base(container)
        {
        }

        /// <summary>
        /// Method to get All ProdDataDaily's for the given ProductionDate (Used in Daily Reports Methods)
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ICollection<ProdDataGasDailyVerificationView>> GetAllByProductionDateRange(string productionDateStart, string productionDateEnd)
        {
            var filteringOptions = new List<FilteringOptions>
                {
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.GreaterOrEquals,
                        PropertyName = "ProductionDate",
                        Value = productionDateStart,
                    },
                    new FilteringOptions
                    {
                        Condition = FilteringOptions.FilteringCondition.LesserOrEquals,
                        PropertyName = "ProductionDate",
                        Value = productionDateEnd,
                    }
                };

            var sortingOptions = new List<SortingOptions>
            {
                new SortingOptions
                {
                    PropertyName = "ProductionDate",
                    IsAscending = true
                },
                new SortingOptions
                {
                    PropertyName = "ComplexId",
                    IsAscending = true
                }
            };

            var options = new GridDataLoadOptions
            {
                FilteringOptions = filteringOptions,
                SortingOptions = sortingOptions,
                PageIndex = 0,
                PageSize = 0,
            };

            return (await GetSortedFilteredPagedList(options)).Models;
        }
    }
}