{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"OperatorProductionDataMongoDB": "mongodb://#{AEEconomics.MongoDB.Username}#:#{AEEconomics.MongoDB.Password}#@#{AEEconomics.MongoDB.Server}#:#{AEEconomics.MongoDB.Port}#/?ssl=true&retrywrites=false", "OperatorProductionDataDBName": "OperatorProductionDataDB"}, "SwaggerConfigs": {"SwaggerUi": true, "Scopes": ["operator_api"], "Title": "Operator Prodution Data", "Description": "An external API used by oil & gas Block Operators to submit the Production data into SMP.", "Versions": [1], "AuthorityUrl": "https://smp.dev.aeeconomics.com", "BasePath": "/api/operator/prod-management", "EnableRequestResponseLogging": true}, "FileServiceBaseURL": "http://localhost:5096/", "ValidationRules": {"ValidationRuleIdForProp": "OPD-VR124", "ValidationRuleIdForValue": "OPD-VR002", "ValidationRuleIdForChecksum": "OPD-VR123"}, "Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"Microsoft.EntityFrameworkCore": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft": "Information", "System": "Information", "System.Net.Http.HttpClient": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "Args": {"expression": "@l = 'Error' or @l = 'Fatal' or @l = 'Warning'"}}], "WriteTo": [{"Name": "File", "Args": {"path": "Logs/Error/error_.log", "outputTemplate": "{Timestamp:o} [{Level:u3}] ({SourceContext}) {Message}{NewLine}{Exception}", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact", "retainedFileCountLimit": 10}}]}}}, {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "Args": {"expression": "@l = 'Information'"}}], "WriteTo": [{"Name": "File", "Args": {"path": "Logs/Info/info_.log", "outputTemplate": "{Timestamp:o} [{Level:u3}] ({SourceContext}) {Message}{NewLine}{Exception}", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact", "retainedFileCountLimit": 10}}]}}}, {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "ApiRoles": null, "Args": {"expression": "@l = 'Verbose' or @l = 'Debug'"}}], "WriteTo": [{"Name": "File", "Args": {"path": "Logs/Verbose/Verbose_.log", "outputTemplate": "{Timestamp:o} [{Level:u3}] ({SourceContext}) {Message}{NewLine}{Exception}", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact", "retainedFileCountLimit": 10}}]}}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "SMP.API.Upstream"}}}