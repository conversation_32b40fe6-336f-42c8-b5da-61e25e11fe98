﻿using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Middleware.Base;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.ManualData;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdAlngForecastDataController : NoSqlControllerBase<ProdAlngForecastData, ProdAlngForecastDataService, ProdAlngForecastDataController>
    {
        public ProdAlngForecastDataController(ILogger<ProdAlngForecastDataController> logger, IRepositoryNoSql<ProdAlngForecastData> service) : base(logger, (ProdAlngForecastDataService)service)
        {
        }

        [HttpPost]
        [Route("addbulk")]
        public async Task<IActionResult> AddBulk([FromBody] List<ProdAlngForecastData> validationRules)
        {
            _logger.LogInformation("Enter Get - addbulk");
            ApiResponse<bool> res = new ApiResponse<bool>
            {
                Status = ApiResponseStatus.Success,
                Response = true
            };
            try
            {
                await _service.AddBulk(validationRules);

                _logger.LogInformation("Exit Get - addbulk");
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Saving Bulk");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = 400,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };

                res.Response = false;
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit Get - addbulk");
                return Ok(res);
            }
        }

        [HttpDelete]
        [Route("delete")]
        public override async Task<IActionResult> Delete([FromQuery] string id)
        {
            _logger.LogInformation("Enter Delete - delete");
            ApiResponse<bool> res = new ApiResponse<bool>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                if (Guid.TryParse(id, out var parsedGuidId))
                {
                    ApiResponse<bool> apiResponse = res;
                    apiResponse.Response = await _service.Delete(parsedGuidId);
                }
                else
                {
                    ApiResponse<bool> apiResponse2 = res;
                    apiResponse2.Response = await _service.Delete(id);
                }

                _logger.LogInformation("Exit Delete - delete");
                return Ok(res);
            }
            catch (Exception ex2)
            {
                Exception ex = ex2;
                _logger.LogError(ex, "Error saving model");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
            {
                new ApiProblemDetails
                {
                    Status = 400,
                    Title = ex.Message,
                    Detail = ex.StackTrace
                }
            };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit Delete - delete");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getallbydaterange")]
        public async Task<IActionResult> GetAllByProductionDateRange([FromQuery] int productionMonthStart, [FromQuery] int productionMonthEnd, [FromQuery] int productionYearStart, [FromQuery] int productionYearEnd)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetAllByProductionDate");
            ApiResponse<ICollection<ProdAlngForecastData>> res = new ApiResponse<ICollection<ProdAlngForecastData>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdAlngForecastData>> apiResponse = res;
                apiResponse.Response = await _service.GetAllByProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetAllByProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetAllByProductionDate");
                return Ok(res);
            }
        }
    }
}
