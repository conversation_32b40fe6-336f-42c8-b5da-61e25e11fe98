﻿using Ae.Stratus.Core.Common.Enums;
using System.Net.Http.Json;
using System.Net;
using Ae.Stratus.Core.Common.Api;
using Upstream.Models.Models;
using OperatorProductionData.Models.Models.ApiModels;
using System.Net.Http.Headers;
using OperatorProductionData.Models.Models.OperatorProdDataOnline;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.Dashboard;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataOnlineWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataOnlineWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }


        public virtual async Task<ApiResponse<Guid>> SubmitProdDataOnline(ReferenceData infrastructure, ProdDataOnline prodDataOnline)
        {
            var obj = new InfrastructureProdDataOnline
            {
                Infrastructure = infrastructure,
                ProdDataOnline = prodDataOnline
            };

            HttpResponseMessage response = await _client.PostAsJsonAsync("proddataonline/SubmitProdDataOnline", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<Guid>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<Guid>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<Guid>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error invoking Operator Prod Data Online Microservice",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataOnline>>> GetList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddataonline/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataOnline>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataOnline>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataOnline>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataOnline>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<DateTime>> GetBlockLastDate(string blockId)
        {
            HttpResponseMessage response = await _client.GetAsync($"proddataonline/GetBlockLastDate?blockId={blockId}");

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<DateTime>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<DateTime>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<DateTime>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error invoking Operator Prod Data Online Microservice",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>>> GetProdDataOnlineDashboardDataAccumulated(string productionDate, string[] blockIds)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionDate", productionDate },
                { "blockIds", blockIds }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddataonline/getonlinedashboarddataaccumulated" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<OnlineDashboardDataAccumulated>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<OnlineDashboardData>>> GetProdDataOnlineDashboardData(string[] blockIds)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "blockIds", blockIds }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddataonline/getonlinedashboarddata" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<OnlineDashboardData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting prodDataOnline dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<OnlineDashboardData>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<OnlineDashboardData>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<OnlineDashboardData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting prodDataOnline dashboard list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<bool>> DeleteById(Guid id)
        {
            HttpResponseMessage response = await _client.DeleteAsync($"proddataonline/DeleteById?id={id}");

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<bool>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error invoking Operator Prod Data Online Microservice",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<bool>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<bool>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error invoking Operator Prod Data Online Microservice",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }
    }
}

