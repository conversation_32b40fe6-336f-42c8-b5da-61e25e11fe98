﻿namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyDetailedProduction
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? FieldId { get; set; }
        public string? ReservoirId { get; set; }
        public string? WellId { get; set; }
        public decimal ExpectedOilProd_OFU { get; set; }
        public decimal ExpectedOilProd_SI { get; set; }
        public decimal ActualOilProd_OFU { get; set; }
        public decimal ActualOilProd_SI { get; set; }
        public decimal WaterProd_OFU { get; set; }
        public decimal WaterProd_SI { get; set; }
        public decimal GasProd_OFU { get; set; }
        public decimal GasProd_SI { get; set; }
        public decimal GasLift_OFU { get; set; }
        public decimal GasLift_SI { get; set; }
        public decimal? OperationTime { get; set; }
        public decimal? OpenOilWell { get; set; }
        public decimal? CloseOilWell { get; set; }
        public decimal? Choke { get; set; }
        public decimal? GasLiftChoke { get; set; }
        public decimal? WaterCut { get; set; }
        public decimal GasOilRatio_OFU { get; set; }
        public decimal GasOilRatio_SI { get; set; }
        public decimal? WellHeadPressure_OFU { get; set; }
        public decimal? WellHeadPressure_SI { get; set; }
        public decimal? WellHeadTemperature_OFU { get; set; }
        public decimal? WellHeadTemperature_SI { get; set; }
        public decimal? BottomHolePressure_OFU { get; set; }
        public decimal? BottomHolePressure_SI { get; set; }
        public decimal? BottomHoleTemperature_OFU { get; set; }
        public decimal? BottomHoleTemperature_SI { get; set; }
        public decimal? DownholeGasificationPressure_OFU { get; set; }
        public decimal? DownholeGasificationPressure_SI { get; set; }
        public decimal? DownholeGasificationTemperature_OFU { get; set; }
        public decimal? DownholeGasificationTemperature_SI { get; set; }
        public decimal? CasingPressure_OFU { get; set; }
        public decimal? CasingPressure_SI { get; set; }
        public decimal? CasingTemperature_OFU { get; set; }
        public decimal? CasingTemperature_SI { get; set; }
        public decimal? FlowingTubingPressure_OFU { get; set; }
        public decimal? FlowingTubingPressure_SI { get; set; }
        public decimal? FlowingTubingTemperature_OFU { get; set; }
        public decimal? FlowingTubingTemperature_SI { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
