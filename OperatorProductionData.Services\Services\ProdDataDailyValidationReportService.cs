﻿using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataDailyValidationReportService : BaseRepository<ProdDataDailyValidationReport>
    {
        public ProdDataDailyValidationReportService(IMongoCollection<ProdDataDailyValidationReport> container) : base(container)
        {
        }

        public async Task<List<ProdDataDailyValidationReport>?> GetValidationReport(string productionDate)
        {
            var filterBuilder = Builders<ProdDataDailyValidationReport>.Filter;
            var filter = filterBuilder.Eq(x => x.ProductionDate, productionDate);
            try
            {
                var validationReport = await GetAll(filter);
                return validationReport.ToList();
            }
            catch (Exception ex)
            {
                return null;
            }



        }

    }
}
