﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using Ae.Stratus.Core.Common.Api;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Upstream.Models.Models.Blocks;
using Upstream.Models.Models;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;

namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class ProdDataMonthlyViewDetails
    {
        public Guid Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public string BlockId { get; set; }
        public int ProductionYear { get; set; }
        public int ProductionMonth { get; set; }
        public int Version { get; set; }
        public int ErrorCount { get; set; }
        public string OperatorReference { get; set; }
        public string ErrorCountStr { get; set; }
        public string Status { get; set; } = "Recebido";
    }
}
