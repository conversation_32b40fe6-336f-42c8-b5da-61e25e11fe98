﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities;

namespace OperatorProductionData.Services.Services
{
    public class LiftingDetailService : RepositoryBase<MonthlyLiftingDetailEntity, LiftingDetail>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public LiftingDetailService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitLiftingDetail(LiftingDetail LiftingDetail)
        {
            _dbContext.LiftingDetail.Add(
                _mapper.Map<LiftingDetail, MonthlyLiftingDetailEntity>(LiftingDetail)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
