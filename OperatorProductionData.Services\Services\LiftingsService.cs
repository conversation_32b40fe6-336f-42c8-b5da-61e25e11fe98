﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class LiftingsService : RepositoryBase<LiftingsEntity, Liftings>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public LiftingsService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOperationComments(Liftings Liftings)
        {
            _dbContext.Liftings.Add(
                _mapper.Map<Liftings, LiftingsEntity>(Liftings)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
