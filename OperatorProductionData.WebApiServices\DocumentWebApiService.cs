﻿using Ae.Stratus.Core.Common.Enums;
using System.Net.Http.Json;
using System.Net;
using Document = OperatorProductionData.Models.Models.Documents.Document;
using Ae.Stratus.Core.Common.Api;
using System.Net.Http.Headers;
using OperatorProductionData.Models.Models.Documents;
using Microsoft.AspNetCore.Mvc;

namespace OperatorProductionData.WebApiServices
{
    public class DocumentWebApiService
    {
        private HttpClient _client { get; set; }

        public DocumentWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> SubmitDocument(Document document)
        {
            HttpResponseMessage response = await _client.PostAsJsonAsync("document/SubmitDocument", document);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                             Status = (int?)response.StatusCode,
                            Title = "Error adding model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error adding model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<FileContentResult?> GetDocument(string companyCode, Guid id)
        {
            HttpResponseMessage response = await _client.GetAsync($"Document/{companyCode}/{id}");

            if (response.StatusCode == HttpStatusCode.OK)
            {
                var returnedFile = new FileContentResult(await response.Content.ReadAsByteArrayAsync(), response.Content.Headers.ContentType.MediaType);

                returnedFile.FileDownloadName = response.Content.Headers.ContentDisposition.FileName;

                return returnedFile;

            }

            return null;
        }

        public virtual async Task<ApiResponse<DocumentDetails>> GetDocumentDetails(string companyCode, Guid id)
        {
            HttpResponseMessage response = await _client.GetAsync($"Document/DocumentDetails/{companyCode}/{id}");

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<DocumentDetails>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                             Status = (int?)response.StatusCode,
                            Title = "Error adding model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<DocumentDetails>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<DocumentDetails>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error adding model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<List<DocumentDetails>>> GetDocumentsDetails(string companyCode, List<Guid> ids)
        {
            var queryString = $"?ids={string.Join("&ids=", ids)}";

            HttpResponseMessage response = await _client.GetAsync($"Document/DocumentsDetails/{companyCode}{queryString}");

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<List<DocumentDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                             Status = (int?)response.StatusCode,
                            Title = "Error adding model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<List<DocumentDetails>>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<List<DocumentDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error adding model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }
    }
}
