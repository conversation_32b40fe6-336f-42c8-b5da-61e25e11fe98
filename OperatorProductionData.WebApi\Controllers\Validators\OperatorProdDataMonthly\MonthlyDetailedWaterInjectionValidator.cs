﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly;

public class MonthlyDetailedWaterInjectionValidator : AbstractValidator<MonthlyDetailedWaterInjection>
{
    public MonthlyDetailedWaterInjectionValidator(string blockId, int productionMonth, int productionYear, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
    {
        var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
        var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
        var vr005 = validationRuleService.GetValidationRule("OPD-VR005");
        var vr007 = validationRuleService.GetValidationRule("OPD-VR007");
        var vr008 = validationRuleService.GetValidationRule("OPD-VR008");
        var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
        var vr016 = validationRuleService.GetValidationRule("OPD-VR016");
        var vr017 = validationRuleService.GetValidationRule("OPD-VR017");
        var vr018 = validationRuleService.GetValidationRule("OPD-VR018");
        var vr019 = validationRuleService.GetValidationRule("OPD-VR019");
        var vr020 = validationRuleService.GetValidationRule("OPD-VR020");
        var vr021 = validationRuleService.GetValidationRule("OPD-VR021");
        var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
        var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
        var vr044 = validationRuleService.GetValidationRule("OPD-VR044");
        var vr045 = validationRuleService.GetValidationRule("OPD-VR045");
        var vr046 = validationRuleService.GetValidationRule("OPD-VR046");
        var vr047 = validationRuleService.GetValidationRule("OPD-VR047");
        var vr048 = validationRuleService.GetValidationRule("OPD-VR048");
        var vr049 = validationRuleService.GetValidationRule("OPD-VR049");
        var vr056 = validationRuleService.GetValidationRule("OPD-VR056");
        var vr057 = validationRuleService.GetValidationRule("OPD-VR057");
        var vr058 = validationRuleService.GetValidationRule("OPD-VR058");
        var vr059 = validationRuleService.GetValidationRule("OPD-VR059");
        var vr060 = validationRuleService.GetValidationRule("OPD-VR060");
        var vr081 = validationRuleService.GetValidationRule("OPD-VR081");
        var vr082 = validationRuleService.GetValidationRule("OPD-VR082");
        var vr126 = validationRuleService.GetValidationRule("OPD-VR126");
        var vr132 = validationRuleService.GetValidationRule("OPD-VR132");
        var vr139 = validationRuleService.GetValidationRule("OPD-VR139");
        var vr140 = validationRuleService.GetValidationRule("OPD-VR140");
        var vr141 = validationRuleService.GetValidationRule("OPD-VR141");
        var vr142 = validationRuleService.GetValidationRule("OPD-VR142");
        var vr143 = validationRuleService.GetValidationRule("OPD-VR143");
        var vr144 = validationRuleService.GetValidationRule("OPD-VR144");
        var vr149 = validationRuleService.GetValidationRule("OPD-VR149");
        var vr150 = validationRuleService.GetValidationRule("OPD-VR150");
        var vr151 = validationRuleService.GetValidationRule("OPD-VR151");
        var vr152 = validationRuleService.GetValidationRule("OPD-VR152");

        var complexes = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes;
        var fields = infrastructure?.Blocks?
              .Where(b => b.Id.Equals(blockId))
              .SelectMany(b => b.DevelopmentAreas.SelectMany(da => da.Fields.Select(f => f.Id)))
              .ToList();
        var reservoirs = infrastructure?.Blocks?
            .Where(b => b.Id.Equals(blockId))
            .SelectMany(b => b.DevelopmentAreas.SelectMany(da => da.Fields.SelectMany(f => f.Reservoirs.Select(f => f.Id))))
            .ToList();

        RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => complexes != null && complexes.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr016.Message, blockId))
                    .WithErrorCode(vr016.Number.ToString())
                    .WithName(vr016.Type.ToString());

        RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    var facilities = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities;
                    if (facilities == null || !facilities.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr017.Message, c.InstanceToValidate.ComplexId),
                            ErrorCode = vr017.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

        RuleFor(x => x.SystemId)
            .Cascade(CascadeMode.Stop)
            .Custom((x, c) =>
                {
                    var systems = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems.ToList();
                    if (!string.IsNullOrEmpty(x) && (systems != null && !systems.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr019.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr019.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                    if (string.IsNullOrEmpty(x) && !string.IsNullOrEmpty(c.InstanceToValidate.EquipmentId))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr020.Message,
                            ErrorCode = vr020.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    };
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId)), ApplyConditionTo.CurrentValidator);

        RuleFor(x => x.EquipmentId)
            .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var equipments = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems?.
                                                     FirstOrDefault(s => s.Id == c.InstanceToValidate.SystemId)?.Equipment.ToList();
                    if (!string.IsNullOrEmpty(x) && (equipments != null && !equipments.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr021.Message, c.InstanceToValidate.SystemId),
                            ErrorCode = vr021.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId && z.Systems.Any(s => s.Id == x.SystemId))), ApplyConditionTo.CurrentValidator);

        RuleFor(x => x.FieldId)
                 .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    if (!string.IsNullOrEmpty(x) && (fields != null && !fields.Any(f => f.Equals(x))))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr018.Message, blockId),
                            ErrorCode = vr018.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

        RuleFor(x => x.ReservoirId)
        .Cascade(CascadeMode.Stop)
        .Custom((x, c) =>
        {
            if (!string.IsNullOrEmpty(x) && (reservoirs != null && !reservoirs.Any(f => f.Equals(x))))
            {
                c.AddFailure(new ValidationFailure
                {
                    ErrorMessage = string.Format(vr132.Message, c.InstanceToValidate.FieldId),
                    ErrorCode = vr132.Number.ToString(),
                    PropertyName = c.PropertyPath
                });
            }
        });

        RuleFor(x => x.WellId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var wells = infrastructure?.Blocks?
                        .Where(b => b.Id.Equals(blockId))
                        .SelectMany(b => b.DevelopmentAreas
                            .SelectMany(da => da.Fields.Where(f => f.Id.Equals(c.InstanceToValidate.FieldId))
                                .SelectMany(f => f.Reservoirs.Where(r => r.Id.Equals(c.InstanceToValidate.ReservoirId))
                                    .SelectMany(r => r.Wells.Where(f => f.Id.Equals(x))))))
                        .ToList();

                    if (!string.IsNullOrEmpty(x) && (wells == null || !wells.Any(w => w.Id.Equals(x))))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr126.Message, c.InstanceToValidate.ReservoirId),
                            ErrorCode = vr126.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

        RuleFor(x => x.ExpectedWaterInjection_OFU)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .Equal(0)
                    .When(x => x.ExpectedWaterInjection_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr060.Message)
                        .WithErrorCode(vr060.Number.ToString())
                        .WithName(vr060.Type.ToString());

        RuleFor(x => x.ExpectedWaterInjection_SI)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .Equal(0)
                    .When(x => x.ExpectedWaterInjection_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr060.Message)
                        .WithErrorCode(vr060.Number.ToString())
                        .WithName(vr060.Type.ToString());

        RuleFor(x => x.MonthWaterInjection_OFU)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .Equal(0)
                    .When(x => x.MonthWaterInjection_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr081.Message)
                        .WithErrorCode(vr081.Number.ToString())
                        .WithName(vr081.Type.ToString());

        RuleFor(x => x.MonthWaterInjection_SI)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .Equal(0)
                    .When(x => x.MonthWaterInjection_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr081.Message)
                        .WithErrorCode(vr081.Number.ToString())
                        .WithName(vr081.Type.ToString());

        RuleFor(x => x.YearWaterInjection_OFU)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .Equal(0)
                    .When(x => x.YearWaterInjection_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr082.Message)
                        .WithErrorCode(vr082.Number.ToString())
                        .WithName(vr082.Type.ToString());

        RuleFor(x => x.YearWaterInjection_SI)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .Equal(0)
                    .When(x => x.YearWaterInjection_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr082.Message)
                        .WithErrorCode(vr082.Number.ToString())
                        .WithName(vr082.Type.ToString());

        RuleFor(x => x.MonthOperationDays)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Custom((x, c) =>
                {
                    if (productionMonth > 0 && productionMonth < 13 && productionYear > 0)
                    {
                        if (x > DateTime.DaysInMonth(productionYear, productionMonth))
                        {

                            c.AddFailure(new ValidationFailure
                            {
                                ErrorMessage = vr007.Message,
                                ErrorCode = vr007.Number.ToString(),
                                PropertyName = c.PropertyPath

                            });
                        }
                    }


                });

        RuleFor(x => x.YearOperationDays)
           .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Custom((x, c) =>
            {
                if (productionMonth > 0 && productionMonth < 13 && productionYear > 0)
                {

                    int daysInYear = 0;
                    if (DateTime.IsLeapYear(productionYear))
                    {

                        daysInYear = 366;

                    }
                    else { daysInYear = 365; }

                    if (x > daysInYear)
                    {

                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr008.Message,
                            ErrorCode = vr008.Number.ToString(),
                            PropertyName = c.PropertyPath

                        });

                    };

                }

            });



        RuleFor(x => x.Choke)
              .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
              .LessThan(100)
                    .WithMessage(vr005.Message)
                    .WithErrorCode(vr005.Number.ToString())
                    .WithName(vr005.Type.ToString());



        RuleFor(x => x.InjectionPressure_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.InjectionPressure_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr056.Message)
                        .WithErrorCode(vr056.Number.ToString())
                        .WithName(vr056.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.InjectionPressure_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr149.Message),
                            ErrorCode = vr149.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.InjectionPressure_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.InjectionPressure_SI)
            .Cascade(CascadeMode.Stop)
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Equal(0)
                .When(x => x.InjectionPressure_OFU == 0, ApplyConditionTo.CurrentValidator)
                    .WithMessage(vr056.Message)
                    .WithErrorCode(vr056.Number.ToString())
                    .WithName(vr056.Type.ToString())
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.InjectionPressure_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr149.Message),
                        ErrorCode = vr149.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.InjectionPressure_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.WellHeadPressure_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WellHeadPressure_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr044.Message)
                        .WithErrorCode(vr044.Number.ToString())
                        .WithName(vr044.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WellHeadPressure_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr139.Message),
                            ErrorCode = vr139.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WellHeadPressure_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.WellHeadPressure_SI)
            .Cascade(CascadeMode.Stop)
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Equal(0)
                .When(x => x.WellHeadPressure_OFU == 0, ApplyConditionTo.CurrentValidator)
                   .WithMessage(vr044.Message)
                   .WithErrorCode(vr044.Number.ToString())
                   .WithName(vr044.Type.ToString())
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.WellHeadPressure_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr139.Message),
                        ErrorCode = vr139.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.WellHeadPressure_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.WellHeadTemperature_OFU)
               .Cascade(CascadeMode.Stop)
               .Custom((x, e) =>
               {
                   if (e.InstanceToValidate.WellHeadTemperature_SI is null)
                   {
                       e.AddFailure(new ValidationFailure
                       {
                           ErrorMessage = string.Format(vr140.Message),
                           ErrorCode = vr140.Number.ToString(),
                           PropertyName = e.PropertyPath
                       });
                   }
               })
           .When(x => x.WellHeadTemperature_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.WellHeadTemperature_SI)
            .Cascade(CascadeMode.Stop)
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.WellHeadTemperature_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr140.Message),
                        ErrorCode = vr140.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.WellHeadTemperature_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.BottomHolePressure_OFU)
                 .Cascade(CascadeMode.Stop)
                 .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                 .Equal(0)
                     .When(x => x.BottomHolePressure_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr046.Message)
                        .WithErrorCode(vr046.Number.ToString())
                        .WithName(vr046.Type.ToString())
                 .Custom((x, e) =>
                 {
                     if (e.InstanceToValidate.BottomHolePressure_SI is null)
                     {
                         e.AddFailure(new ValidationFailure
                         {
                             ErrorMessage = string.Format(vr141.Message),
                             ErrorCode = vr141.Number.ToString(),
                             PropertyName = e.PropertyPath
                         });
                     }
                 })
             .When(x => x.BottomHolePressure_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.BottomHolePressure_SI)
            .Cascade(CascadeMode.Stop)
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Equal(0)
                .When(x => x.BottomHolePressure_OFU == 0, ApplyConditionTo.CurrentValidator)
                   .WithMessage(vr046.Message)
                   .WithErrorCode(vr046.Number.ToString())
                   .WithName(vr046.Type.ToString())
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.BottomHolePressure_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr141.Message),
                        ErrorCode = vr141.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.BottomHolePressure_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.BottomHoleTemperature_OFU)
                .Cascade(CascadeMode.Stop)
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.BottomHoleTemperature_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr142.Message),
                            ErrorCode = vr142.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.BottomHoleTemperature_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.BottomHoleTemperature_SI)
            .Cascade(CascadeMode.Stop)
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.BottomHoleTemperature_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr142.Message),
                        ErrorCode = vr142.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.BottomHoleTemperature_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.DownholeGasificationPressure_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.DownholeGasificationPressure_SI == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr048.Message)
                       .WithErrorCode(vr048.Number.ToString())
                       .WithName(vr048.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.DownholeGasificationPressure_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr143.Message),
                            ErrorCode = vr143.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.DownholeGasificationPressure_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.DownholeGasificationPressure_SI)
            .Cascade(CascadeMode.Stop)
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Equal(0)
                .When(x => x.DownholeGasificationPressure_OFU == 0, ApplyConditionTo.CurrentValidator)
                   .WithMessage(vr048.Message)
                   .WithErrorCode(vr048.Number.ToString())
                   .WithName(vr048.Type.ToString())
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.DownholeGasificationPressure_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr143.Message),
                        ErrorCode = vr143.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.DownholeGasificationPressure_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.DownholeGasificationTemperature_OFU)
                .Cascade(CascadeMode.Stop)
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.DownholeGasificationTemperature_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr144.Message),
                            ErrorCode = vr144.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.DownholeGasificationTemperature_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.DownholeGasificationTemperature_SI)
            .Cascade(CascadeMode.Stop)
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.DownholeGasificationTemperature_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr144.Message),
                        ErrorCode = vr144.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.DownholeGasificationTemperature_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.UpstreamChokePressure_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.UpstreamChokePressure_SI == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr057.Message)
                       .WithErrorCode(vr057.Number.ToString())
                       .WithName(vr057.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.UpstreamChokePressure_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr150.Message),
                            ErrorCode = vr150.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.UpstreamChokePressure_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.UpstreamChokePressure_SI)
            .Cascade(CascadeMode.Stop)
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Equal(0)
                .When(x => x.UpstreamChokePressure_OFU == 0, ApplyConditionTo.CurrentValidator)
                   .WithMessage(vr057.Message)
                   .WithErrorCode(vr057.Number.ToString())
                   .WithName(vr057.Type.ToString())
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.UpstreamChokePressure_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr150.Message),
                        ErrorCode = vr150.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.UpstreamChokePressure_SI is not null, ApplyConditionTo.AllValidators);


        RuleFor(x => x.UpstreamChokeTemperature_OFU)
                .Cascade(CascadeMode.Stop)
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.UpstreamChokeTemperature_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr151.Message),
                            ErrorCode = vr151.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.UpstreamChokeTemperature_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.UpstreamChokeTemperature_SI)
            .Cascade(CascadeMode.Stop)
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.UpstreamChokeTemperature_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr151.Message),
                        ErrorCode = vr151.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.UpstreamChokeTemperature_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.DownstreamChokePressure_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                   .When(x => x.DownstreamChokePressure_SI == 0, ApplyConditionTo.CurrentValidator)
                      .WithMessage(vr059.Message)
                      .WithErrorCode(vr059.Number.ToString())
                      .WithName(vr059.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.DownstreamChokePressure_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr152.Message),
                            ErrorCode = vr152.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.DownstreamChokePressure_OFU is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.DownstreamChokePressure_SI)
            .Cascade(CascadeMode.Stop)
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString())
            .Equal(0)
               .When(x => x.DownstreamChokePressure_OFU == 0, ApplyConditionTo.CurrentValidator)
                  .WithMessage(vr059.Message)
                  .WithErrorCode(vr059.Number.ToString())
                  .WithName(vr059.Type.ToString())
            .Custom((x, e) =>
            {
                if (e.InstanceToValidate.DownstreamChokePressure_OFU is null)
                {
                    e.AddFailure(new ValidationFailure
                    {
                        ErrorMessage = string.Format(vr152.Message),
                        ErrorCode = vr152.Number.ToString(),
                        PropertyName = e.PropertyPath
                    });
                }
            })
        .When(x => x.DownstreamChokePressure_SI is not null, ApplyConditionTo.AllValidators);

        RuleFor(x => x.Comments)
               .NotEmpty()
                   .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                       .WithMessage(vr023.Message)
                       .WithErrorCode(vr023.Number.ToString())
                       .WithName(vr023.Type.ToString());

        RuleFor(x => x.CommentedBy)
            .NotEmpty()
              .When(x => !string.IsNullOrEmpty(x.Comments))
                   .WithMessage(vr024.Message)
                   .WithErrorCode(vr024.Number.ToString())
                   .WithName(vr024.Type.ToString());

    }
}
