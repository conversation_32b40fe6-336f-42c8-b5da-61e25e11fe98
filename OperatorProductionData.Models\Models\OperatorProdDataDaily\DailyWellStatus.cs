﻿using OperatorProductionData.Models.Utilities;
using System;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyWellStatus
    {
        public string WellId { get; set; }
        public string StatusId { get; set; }
        public string StatusDate { get; set; }
        public virtual DateTime ParsedStatusDate
        {
            set
            {
                _tempStatusDate = DateUtils.ConvertDateTimeString(StatusDate, "yyyy-MM-dd");
            }

            get
            {
                return _tempStatusDate;
            }
        }

        private DateTime _tempStatusDate;
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
