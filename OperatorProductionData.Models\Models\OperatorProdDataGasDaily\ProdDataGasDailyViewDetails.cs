﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using Ae.Stratus.Core.Common.Api;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Upstream.Models.Models.Blocks;
using Upstream.Models.Models;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class ProdDataGasDailyViewDetails
    {
        public Guid Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public string ComplexId { get; set; }
        public string ProductionDate { get; set; }
        public int Version { get; set; }
        public int ErrorCount { get; set; }
        public string OperatorReference { get; set; }
        public string ErrorCountStr { get; set; }
        public DateTime ParsedProductionDate { get; set; }
        public string Status { get; set; } = "Recebido";
    }
}
