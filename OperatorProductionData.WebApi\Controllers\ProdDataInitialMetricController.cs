﻿using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Middleware.Base;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.ManualData;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataInitialMetricController : NoSqlControllerBase<ProdDataInitialMetric, ProdDataInitialMetricService, ProdDataInitialMetricController>
    {
        public ProdDataInitialMetricController(ILogger<ProdDataInitialMetricController> logger, IRepositoryNoSql<ProdDataInitialMetric> service) : base(logger, (ProdDataInitialMetricService)service)
        {
        }

        [HttpPost]
        [Route("addbulk")]
        public async Task<IActionResult> AddBulk([FromBody] List<ProdDataInitialMetric> validationRules)
        {
            _logger.LogInformation("Enter Get - addbulk");
            ApiResponse<bool> res = new ApiResponse<bool>
            {
                Status = ApiResponseStatus.Success,
                Response = true
            };
            try
            {
                await _service.AddBulk(validationRules);

                _logger.LogInformation("Exit Get - addbulk");
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Saving Bulk");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = 400,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };

                res.Response = false;
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit Get - addbulk");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getallbydaterange")]
        public async Task<IActionResult> GetAllByProductionDateRange([FromQuery] int productionMonthStart, [FromQuery] int productionMonthEnd, [FromQuery] int productionYearStart, [FromQuery] int productionYearEnd)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetAllByProductionDate");
            ApiResponse<ICollection<ProdDataInitialMetric>> res = new ApiResponse<ICollection<ProdDataInitialMetric>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdDataInitialMetric>> apiResponse = res;
                apiResponse.Response = await _service.GetAllByProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetAllByProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetAllByProductionDate");
                return Ok(res);
            }
        }
    }
}
