﻿using Ae.Stratus.Core.Backend.NoSQL.Base;
using OperatorProductionData.Models.Models;

namespace OperatorProductionData.Services.Services
{
    public class GasValidationRuleNoSqlService : RepositoryBaseMongoDB<GasValidationRule>
    {
        public GasValidationRuleNoSqlService(string connectionString, string databaseName) : base(connectionString, databaseName, "GasValidationRule")
        {
        }

        public async Task<bool> AddBulk(List<GasValidationRule> validationRules)
        {
            await _container.InsertManyAsync(validationRules);

            return true;
        }
    }
}
