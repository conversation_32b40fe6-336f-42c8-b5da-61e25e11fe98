using System;
using System.Collections.Generic;

namespace OperatorProductionData.Models.Models.Dashboard
{
    public class ProductionDashboardDto
    {
        public string OperatorId { get; set; }
        public string OperatorName { get; set; }
        public string BlockId { get; set; }
        public string BlockName { get; set; }
        public string ProductionDate { get; set; } //Daily
        public int ProductionMonth { get; set; } //Monthly
        public int ProductionYear { get; set; } //Monthly
        public decimal ProductionEficiency { get; set; } //Monthly

        public decimal OilExported_OFU { get; set; } //Monthly
        public decimal OilExported_SI { get; set; } //Monthly

        // Production aggregated by Complex
        public List<ProductionByComplexDto> ProductionByComplex_SI { get; set; } = new List<ProductionByComplexDto>();
        public List<ProductionByComplexDto> ProductionByComplex_OFU { get; set; } = new List<ProductionByComplexDto>();

        // Production aggregated by Facility
        public List<ProductionByFacilityDto> ProductionByFacility_SI { get; set; } = new List<ProductionByFacilityDto>();
        public List<ProductionByFacilityDto> ProductionByFacility_OFU { get; set; } = new List<ProductionByFacilityDto>();

        // Production aggregated by Field
        public List<ProductionByFieldDto> ProductionByField_SI { get; set; } = new List<ProductionByFieldDto>();
        public List<ProductionByFieldDto> ProductionByField_OFU { get; set; } = new List<ProductionByFieldDto>();


        // Block totals
        public TotalsDto BlockTotals_SI { get; set; }
        public TotalsDto BlockTotals_OFU { get; set; }

        // Operator Totals
        public TotalsDto OperatorTotals { get; set; } //Will be calculated (in BFF) already with the UnitOfMeasurent Accounted for 

        //Losses By Category
        public ProductionLossMetricsDto ProductionLossMetrics { get; set; }

        public ProductionLossDto ProductionLossValues { get; set; }

        // Additional metrics for dashboard charts
        public WaterQualityMetricsDto WaterQualityMetrics { get; set; }
        //public EquipmentStatusMetricsDto EquipmentStatusMetrics { get; set; }

        public DateTime CreatedDate { get; set; }
    }

    public class ProductionByComplexDto
    {
        public string ComplexId { get; set; }
        public decimal OilProduction { get; set; }
        public decimal ExpectedOilProduction { get; set; }
        public decimal GasProduction { get; set; }
        public decimal GasInjection { get; set; }
        public decimal GasExported { get; set; }
        public decimal GasFlared { get; set; }
        public decimal GasFuel { get; set; }
        public decimal GasLift { get; set; }
        public decimal WaterInjection { get; set; }
        public decimal WaterProduction { get; set; }
        public decimal WaterCut { get; set; }
        public decimal WaterDischarge { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public decimal? VoidageReplacementRatio { get; set; }
        public string UnitMeasure { get; set; }
    }

    public class ProductionByFacilityDto
    {
        public string FacilityId { get; set; }
        public string ComplexId { get; set; }
        public decimal OilProduction { get; set; }
        public decimal ExpectedOilProduction { get; set; }
        public decimal GasProduction { get; set; }
        public decimal GasInjection { get; set; }
        public decimal GasExported { get; set; }
        public decimal GasFlared { get; set; }
        public decimal GasFuel { get; set; }
        public decimal GasLift { get; set; }
        public decimal GasImported { get; set; }
        public decimal WaterInjection { get; set; }
        public decimal WaterProduction { get; set; }
        public decimal WaterCut { get; set; }
        public decimal WaterDischarge { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public decimal? VoidageReplacementRatio { get; set; }
        public string UnitMeasure { get; set; }
    }

    public class ProductionByFieldDto
    {
        public string FieldId { get; set; }
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public decimal OilProduction { get; set; }
        public decimal ExpectedOilProduction { get; set; }
        public decimal GasProduction { get; set; }
        public decimal GasInjection { get; set; }
        public decimal GasExported { get; set; }
        public decimal GasFlared { get; set; }
        public decimal GasFuel { get; set; }
        public decimal GasLift { get; set; }
        public decimal GasImported { get; set; }
        public decimal WaterInjection { get; set; }
        public decimal WaterProduction { get; set; }
        public decimal WaterCut { get; set; }
        public decimal WaterDischarge { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public decimal? VoidageReplacementRatio { get; set; }
        public string UnitMeasure { get; set; }
    }
    public class TotalsDto
    {
        public decimal TotalOilProduction { get; set; }
        public decimal TotalLastValueOilProduction { get; set; }
        public decimal TotalHomologousOilProduction { get; set; }
        public decimal TotalExpectedOilProduction { get; set; }
        public decimal TotalOilForecast { get; set; }
        public decimal TotalGasForecast { get; set; }
        public decimal TotalWaterForecast { get; set; }
        public decimal TotalGasProduction { get; set; }
        public decimal TotalLastValueGasProduction { get; set; }
        public decimal TotalHomologousGasProduction { get; set; }
        public decimal TotalLastValueFlaredGasProduction { get; set; }
        public decimal TotalHomologousFlaredGasProduction { get; set; }
        public decimal TotalGasInjection { get; set; }
        public decimal TotalGasExported { get; set; }
        public decimal TotalGasFlared { get; set; }
        public decimal TotalGasFuel { get; set; }
        public decimal TotalGasLift { get; set; }
        public decimal TotalGasImported { get; set; }
        public decimal TotalWaterInjection { get; set; }
        public decimal TotalWaterProduction { get; set; }
        public decimal TotalLastValueWaterProduction { get; set; }
        public decimal TotalHomologousWaterProduction { get; set; }
        public decimal TotalWaterCut { get; set; }
        public decimal TotalWaterDischarge { get; set; }
        public decimal TotalVoidageReplacementRatio { get; set; }
        public string UnitMeasure { get; set; }
    }

    public class WaterQualityMetricsDto
    {
        public decimal AverageSalinity { get; set; }
        public decimal AverageTemperature { get; set; }
        public decimal AveragePH { get; set; }
        public int TotalSamples { get; set; }
    }

    public class ProductionLossMetricsDto
    {
        public decimal TotalOilLoss { get; set; }
        public decimal TotalGasLoss { get; set; }
        public decimal TotalWaterLoss { get; set; }
        public decimal PlannedOilLoss { get; set; }
        public decimal UnplannedOilLoss { get; set; }
        public Dictionary<string, decimal> LossByMotive { get; set; } = new Dictionary<string, decimal>();
    }

    public class ProductionLossDto
    {
        public decimal TotalLoss_OFU { get; set; }
        public decimal TotalLoss_SI { get; set; }
        public decimal TotalPlannedLoss_OFU { get; set; }
        public decimal TotalPlannedLoss_SI { get; set; }
        public decimal TotalUnplannedLoss_OFU { get; set; }
        public decimal TotalUnplannedLoss_SI { get; set; }
    }

    //public class EquipmentStatusMetricsDto
    //{
    //    public int TotalEquipment { get; set; }
    //    public int OperationalCount { get; set; }
    //    public int MaintenanceCount { get; set; }
    //    public int DownCount { get; set; }
    //    public decimal AvailabilityPercentage { get; set; }
    //}
}