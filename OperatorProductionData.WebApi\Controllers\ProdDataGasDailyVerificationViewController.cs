using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataGasDailyVerificationViewController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataGasDailyVerificationViewService _service { get; set; }
        private IMapper _mapper { get; set; }

        public ProdDataGasDailyVerificationViewController(
                ILogger<ProdDataDailyController> logger,
                ProdDataGasDailyVerificationViewService verificationViewService,
                IMapper mapper
        )
        {
            _logger = logger;
            _service = verificationViewService;
            _mapper = mapper;
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetVerificationViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataGasDaily Post - getverificationviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>> apiResponse = res;

                options.SortingOptions = new List<SortingOptions> {
                    new SortingOptions
                    {
                        PropertyName = "ProductionDate",
                        IsAscending = false
                    }
                };

                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error getting sorted, filtered and paged verification view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDaily Post - getverificationviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getallbyproductiondaterange")]
        public async Task<IActionResult> GetAllByProductionDateRange([FromQuery] string productionDateStart, [FromQuery] string productionDateEnd)
        {
            _logger.LogInformation("Enter ProdDataGasDaily - GetAllByProductionDate");
            ApiResponse<ICollection<ProdDataGasDailyVerificationView>> res = new ApiResponse<ICollection<ProdDataGasDailyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdDataGasDailyVerificationView>> apiResponse = res;
                apiResponse.Response = await _service.GetAllByProductionDateRange(productionDateStart, productionDateEnd);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasDaily - Error GetAllByProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasDaily Get - GetAllByProductionDate");
                return Ok(res);
            }
        }
    }
}
