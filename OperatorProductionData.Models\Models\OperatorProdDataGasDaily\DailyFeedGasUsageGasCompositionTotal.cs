﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyFeedGasUsageGasCompositionTotal
    {
        public string FacilityId { get; set; }
        public string GasCompositionId { get; set; }
        public decimal TotalFeedGasUsageGasComposition { get; set; }
        public decimal TotalFeedGasUsageEnergy_OFU { get; set; }
        public decimal TotalFeedGasUsageEnergy_SI { get; set; }
        public decimal TotalFeedGasUsageVolume_OFU { get; set; }
        public decimal TotalFeedGasUsageVolume_SI { get; set; }
        public decimal TotalFeedGasUsageMonthToDateEnergy_OFU { get; set; }
        public decimal TotalFeedGasUsageMonthToDateEnergy_SI { get; set; }
        public decimal TotalFeedGasUsageMonthToDateVolume_OFU { get; set; }
        public decimal TotalFeedGasUsageMonthToDateVolume_SI { get; set; }
        public decimal TotalFeedGasUsageGRHV { get; set; }
    }
}
