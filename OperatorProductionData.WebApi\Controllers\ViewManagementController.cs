﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.ViewManagement;
using OperatorProductionData.Services.Database;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ViewManagementController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private DBSeedHelper _seedHelper { get; set; }

        public ViewManagementController(ILogger<ViewManagementController> logger, DBSeedHelper seedHelper)
        {
            _logger = logger;
            _seedHelper = seedHelper;
        }

        [HttpGet]
        [Route("{viewType}")]
        public async Task<IActionResult> RefreshView([FromRoute] ViewType viewType)
        {
            var apiResponse = new ApiResponse<bool>();
            
            try
            {
                _seedHelper.GetType().GetMethod($"Create{viewType.ToString()}").Invoke(_seedHelper, null);

                apiResponse.Status = ApiResponseStatus.Success;
                apiResponse.Response = true;
                
                return Ok(apiResponse);
            }
            catch
            {
                apiResponse.Status = ApiResponseStatus.Error;
                return BadRequest(apiResponse);
            }
        }
    }
}
