﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyGasShipping
    {
        public string FacilityId { get; set; }
        public string LpgTypeId { get; set; }
        public int TotalMonthlyNumberOfShipping { get; set; }
        public decimal TotalMonthlyLiftingMass { get; set; }
        public decimal TotalMonthlyLiftingEnergy_OFU { get; set; }
        public decimal TotalMonthlyLiftingEnergy_SI { get; set; }
        public decimal TotalMonthlyLiftingEnergy_BOE { get; set; }
        public decimal TotalMonthlyLiftingVolume_OFU { get; set; }
        public decimal TotalMonthlyLiftingVolume_SI { get; set; }
        public decimal TotalMonthlyLiftingVolume_Gallons { get; set; }
        public decimal TotalMonthlyLiftingVolume_Barrels { get; set; }
    }
}
