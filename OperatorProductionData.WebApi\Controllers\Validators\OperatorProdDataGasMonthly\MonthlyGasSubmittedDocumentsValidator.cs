﻿using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Services.Services;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasMonthly
{
    public class MonthlyGasSubmittedDocumentsValidator : AbstractValidator<MonthlyGasSubmittedDocuments>
    {
        public MonthlyGasSubmittedDocumentsValidator(GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr034 = validationRuleService.GetValidationRule("OPG-VR034");

            RuleFor(x => x.DocumentId)
              .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.DocumentTypeId)
              .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Must(x => (referenceData.ReferenceTables.DocumentTypes.Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString());

            RuleFor(x => x.Date)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr034.Message,
                            ErrorCode = vr034.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.Ref)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

        }
    }
}