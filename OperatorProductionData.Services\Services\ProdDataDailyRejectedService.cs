﻿using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.Overview;
using OperatorProductionData.Services.Enums;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataDailyRejectedService : BaseRepository<ProdDataDailyRejected>
    {
        public ProdDataDailyRejectedService(IMongoCollection<ProdDataDailyRejected> container) : base(container)
        {
        }
    }
}
