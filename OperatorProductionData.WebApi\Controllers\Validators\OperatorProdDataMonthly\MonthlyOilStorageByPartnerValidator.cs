﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators
{
    public class MonthlyOilStorageByPartnerValidator : AbstractValidator<MonthlyOilStorageByPartner>
    {
        public MonthlyOilStorageByPartnerValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr004 = validationRuleService.GetValidationRule("OPD-VR004");
            var vr005 = validationRuleService.GetValidationRule("OPD-VR005");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr016 = validationRuleService.GetValidationRule("OPD-VR016");
            var vr017 = validationRuleService.GetValidationRule("OPD-VR017");
            var vr019 = validationRuleService.GetValidationRule("OPD-VR019");
            var vr020 = validationRuleService.GetValidationRule("OPD-VR020");
            var vr021 = validationRuleService.GetValidationRule("OPD-VR021");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr093 = validationRuleService.GetValidationRule("OPD-VR093");
            var vr094 = validationRuleService.GetValidationRule("OPD-VR094");
            var vr095 = validationRuleService.GetValidationRule("OPD-VR095");
            var vr096 = validationRuleService.GetValidationRule("OPD-VR096");
            var vr097 = validationRuleService.GetValidationRule("OPD-VR097");
            var vr098 = validationRuleService.GetValidationRule("OPD-VR098");
            var vr099 = validationRuleService.GetValidationRule("OPD-VR099");
            var vr100 = validationRuleService.GetValidationRule("OPD-VR100");
            var vr101 = validationRuleService.GetValidationRule("OPD-VR101");
            var vr102 = validationRuleService.GetValidationRule("OPD-VR102");
            var vr103 = validationRuleService.GetValidationRule("OPD-VR103");
            var vr104 = validationRuleService.GetValidationRule("OPD-VR104");
            var vr105 = validationRuleService.GetValidationRule("OPD-VR105");
            var vr106 = validationRuleService.GetValidationRule("OPD-VR106");
            var vr107 = validationRuleService.GetValidationRule("OPD-VR107");
            var vr108 = validationRuleService.GetValidationRule("OPD-VR108");
            var vr109 = validationRuleService.GetValidationRule("OPD-VR109");
            var vr110 = validationRuleService.GetValidationRule("OPD-VR110");
            var vr111 = validationRuleService.GetValidationRule("OPD-VR111");
            var vr112 = validationRuleService.GetValidationRule("OPD-VR112");
            var vr113 = validationRuleService.GetValidationRule("OPD-VR113");
            var vr114 = validationRuleService.GetValidationRule("OPD-VR114");
            var vr115 = validationRuleService.GetValidationRule("OPD-VR115");
            var vr116 = validationRuleService.GetValidationRule("OPD-VR116");
            var vr117 = validationRuleService.GetValidationRule("OPD-VR117");

            var complexes = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes;
            var blends = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Blends;

            var products = infrastructure?.Blocks?
                .Where(b => b.Id.Equals(blockId))
                .SelectMany(b => b.Blends.SelectMany(bl => bl.Products))
                .ToList();

            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => complexes != null && complexes.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr016.Message, blockId))
                    .WithErrorCode(vr016.Number.ToString())
                    .WithName(vr016.Type.ToString());

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    var facilities = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities;
                    if (facilities == null || !facilities.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr017.Message, c.InstanceToValidate.ComplexId),
                            ErrorCode = vr017.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.SystemId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var systems = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems.ToList();
                    if (!string.IsNullOrEmpty(x) && (systems != null && !systems.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr019.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr019.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                    if (string.IsNullOrEmpty(x) && !string.IsNullOrEmpty(c.InstanceToValidate.EquipmentId))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr020.Message,
                            ErrorCode = vr020.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    };
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId)), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.EquipmentId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var equipments = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems?.
                                                     FirstOrDefault(s => s.Id == c.InstanceToValidate.SystemId)?.Equipment.ToList();
                    if (!string.IsNullOrEmpty(x) && (equipments != null && !equipments.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr021.Message, c.InstanceToValidate.SystemId),
                            ErrorCode = vr021.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId && z.Systems.Any(s => s.Id == x.SystemId))), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.BlendId)
              .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
              .Must(x => blends != null && blends.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.ProductId)
               .Cascade(CascadeMode.Stop)
               .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                 {
                     var products_in_blend = blends?.FirstOrDefault(y => y.Id == c.InstanceToValidate.BlendId)?.Products;

                     if (!string.IsNullOrEmpty(x) && (products_in_blend != null && !products_in_blend.Any(y => y.Id == x)))
                     {
                         c.AddFailure(new ValidationFailure
                         {
                             ErrorMessage = string.Format(vr010.Message, c.InstanceToValidate.SystemId),
                             ErrorCode = vr010.Number.ToString(),
                             PropertyName = c.PropertyPath
                         });
                     }
                 });

            RuleFor(x => x.ParticipantNif)
               .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.EquityShare)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());

            RuleFor(x => x.LiftingPercentage)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
               .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());

            RuleFor(x => x.InitialStock_CO_OFU)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
               .Equal(0)
                    .When(x => x.InitialStock_CO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr093.Message)
                        .WithErrorCode(vr093.Number.ToString())
                        .WithName(vr093.Type.ToString())
               .GreaterThan(0)
                    .When(x => x.InitialStock_CO_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr094.Message)
                        .WithErrorCode(vr094.Number.ToString())
                        .WithName(vr094.Type.ToString())
               .LessThan(0)
                    .When(x => x.InitialStock_CO_SI < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr095.Message)
                        .WithErrorCode(vr095.Number.ToString())
                        .WithName(vr095.Type.ToString());

            RuleFor(x => x.InitialStock_CO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_CO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr093.Message)
                        .WithErrorCode(vr093.Number.ToString())
                        .WithName(vr093.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.InitialStock_CO_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr094.Message)
                        .WithErrorCode(vr094.Number.ToString())
                        .WithName(vr094.Type.ToString())
                .LessThan(0)
                    .When(x => x.InitialStock_CO_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr095.Message)
                        .WithErrorCode(vr095.Number.ToString())
                        .WithName(vr095.Type.ToString());

            RuleFor(x => x.InitialStock_PO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_PO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr096.Message)
                        .WithErrorCode(vr096.Number.ToString())
                        .WithName(vr096.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.InitialStock_PO_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr097.Message)
                        .WithErrorCode(vr097.Number.ToString())
                        .WithName(vr097.Type.ToString())
                .LessThan(0)
                    .When(x => x.InitialStock_PO_SI < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr098.Message)
                        .WithErrorCode(vr098.Number.ToString())
                        .WithName(vr098.Type.ToString());

            RuleFor(x => x.InitialStock_PO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_PO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr096.Message)
                        .WithErrorCode(vr096.Number.ToString())
                        .WithName(vr096.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.InitialStock_PO_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr097.Message)
                        .WithErrorCode(vr097.Number.ToString())
                        .WithName(vr097.Type.ToString())
                .LessThan(0)
                    .When(x => x.InitialStock_PO_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr098.Message)
                        .WithErrorCode(vr098.Number.ToString())
                        .WithName(vr098.Type.ToString());


            RuleFor(x => x.Production_CO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Production_CO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr099.Message)
                        .WithErrorCode(vr099.Number.ToString())
                        .WithName(vr099.Type.ToString());


            RuleFor(x => x.Production_CO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Production_CO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr099.Message)
                        .WithErrorCode(vr099.Number.ToString())
                        .WithName(vr099.Type.ToString());


            RuleFor(x => x.Production_PO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Production_PO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr100.Message)
                        .WithErrorCode(vr100.Number.ToString())
                        .WithName(vr100.Type.ToString());


            RuleFor(x => x.Production_PO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Production_PO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr100.Message)
                        .WithErrorCode(vr100.Number.ToString())
                        .WithName(vr100.Type.ToString());



            RuleFor(x => x.Liftings_CO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Liftings_CO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr101.Message)
                        .WithErrorCode(vr101.Number.ToString())
                        .WithName(vr101.Type.ToString());


            RuleFor(x => x.Liftings_CO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Liftings_CO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr101.Message)
                        .WithErrorCode(vr101.Number.ToString())
                        .WithName(vr101.Type.ToString());


            RuleFor(x => x.Liftings_PO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Liftings_PO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr102.Message)
                        .WithErrorCode(vr102.Number.ToString())
                        .WithName(vr102.Type.ToString());


            RuleFor(x => x.Liftings_PO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Liftings_PO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr102.Message)
                        .WithErrorCode(vr102.Number.ToString())
                        .WithName(vr102.Type.ToString());

            RuleFor(x => x.FinalStock_CO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.FinalStock_CO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr103.Message)
                        .WithErrorCode(vr103.Number.ToString())
                        .WithName(vr103.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.FinalStock_CO_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr104.Message)
                        .WithErrorCode(vr104.Number.ToString())
                        .WithName(vr104.Type.ToString())
                .LessThan(0)
                    .When(x => x.FinalStock_CO_SI < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr105.Message)
                        .WithErrorCode(vr105.Number.ToString())
                        .WithName(vr105.Type.ToString());


            RuleFor(x => x.FinalStock_CO_SI)
                 .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                 .Equal(0)
                    .When(x => x.FinalStock_CO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr103.Message)
                        .WithErrorCode(vr103.Number.ToString())
                        .WithName(vr103.Type.ToString())
                 .GreaterThan(0)
                    .When(x => x.FinalStock_CO_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr104.Message)
                        .WithErrorCode(vr104.Number.ToString())
                        .WithName(vr104.Type.ToString())
                 .LessThan(0)
                    .When(x => x.FinalStock_CO_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr105.Message)
                        .WithErrorCode(vr105.Number.ToString())
                        .WithName(vr105.Type.ToString());

            RuleFor(x => x.FinalStock_PO_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.FinalStock_PO_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr106.Message)
                        .WithErrorCode(vr106.Number.ToString())
                        .WithName(vr106.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.FinalStock_PO_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr107.Message)
                        .WithErrorCode(vr107.Number.ToString())
                        .WithName(vr107.Type.ToString())
                .LessThan(0)
                        .When(x => x.FinalStock_PO_SI < 0, ApplyConditionTo.CurrentValidator)
                            .WithMessage(vr108.Message)
                            .WithErrorCode(vr108.Number.ToString())
                            .WithName(vr108.Type.ToString());

            RuleFor(x => x.FinalStock_PO_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.FinalStock_PO_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr106.Message)
                        .WithErrorCode(vr106.Number.ToString())
                        .WithName(vr106.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.FinalStock_PO_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr107.Message)
                        .WithErrorCode(vr107.Number.ToString())
                        .WithName(vr107.Type.ToString())
                .LessThan(0)
                    .When(x => x.FinalStock_PO_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr108.Message)
                        .WithErrorCode(vr108.Number.ToString())
                        .WithName(vr108.Type.ToString());

            RuleFor(x => x.QualityBankAdjustments_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.QualityBankAdjustments_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr109.Message)
                        .WithErrorCode(vr109.Number.ToString())
                        .WithName(vr109.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.QualityBankAdjustments_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr110.Message)
                        .WithErrorCode(vr110.Number.ToString())
                        .WithName(vr110.Type.ToString())
                .LessThan(0)
                    .When(x => x.QualityBankAdjustments_SI < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr111.Message)
                        .WithErrorCode(vr111.Number.ToString())
                        .WithName(vr111.Type.ToString());

            RuleFor(x => x.QualityBankAdjustments_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.QualityBankAdjustments_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr109.Message)
                        .WithErrorCode(vr109.Number.ToString())
                        .WithName(vr109.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.QualityBankAdjustments_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr110.Message)
                        .WithErrorCode(vr110.Number.ToString())
                        .WithName(vr110.Type.ToString())
                .LessThan(0)
                    .When(x => x.QualityBankAdjustments_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr111.Message)
                        .WithErrorCode(vr111.Number.ToString())
                        .WithName(vr111.Type.ToString());

            RuleFor(x => x.ComplexRefineryAdjustments_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.ComplexRefineryAdjustments_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr112.Message)
                        .WithErrorCode(vr112.Number.ToString())
                        .WithName(vr112.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.ComplexRefineryAdjustments_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr113.Message)
                        .WithErrorCode(vr113.Number.ToString())
                        .WithName(vr113.Type.ToString())
                .LessThan(0)
                    .When(x => x.ComplexRefineryAdjustments_SI < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr114.Message)
                        .WithErrorCode(vr114.Number.ToString())
                        .WithName(vr114.Type.ToString());

            RuleFor(x => x.ComplexRefineryAdjustments_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.ComplexRefineryAdjustments_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr112.Message)
                        .WithErrorCode(vr112.Number.ToString())
                        .WithName(vr112.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.ComplexRefineryAdjustments_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr113.Message)
                        .WithErrorCode(vr113.Number.ToString())
                        .WithName(vr113.Type.ToString())
                .LessThan(0)
                    .When(x => x.ComplexRefineryAdjustments_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr114.Message)
                        .WithErrorCode(vr114.Number.ToString())
                        .WithName(vr114.Type.ToString());

            RuleFor(x => x.ExternalRefineryAdjustments_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.ExternalRefineryAdjustments_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr115.Message)
                        .WithErrorCode(vr115.Number.ToString())
                        .WithName(vr115.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.ExternalRefineryAdjustments_SI > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr116.Message)
                        .WithErrorCode(vr116.Number.ToString())
                        .WithName(vr116.Type.ToString())
                .LessThan(0)
                    .When(x => x.ExternalRefineryAdjustments_SI < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr117.Message)
                        .WithErrorCode(vr117.Number.ToString())
                        .WithName(vr117.Type.ToString());

            RuleFor(x => x.ExternalRefineryAdjustments_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.ExternalRefineryAdjustments_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr115.Message)
                        .WithErrorCode(vr115.Number.ToString())
                        .WithName(vr115.Type.ToString())
                .GreaterThan(0)
                    .When(x => x.ExternalRefineryAdjustments_OFU > 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr116.Message)
                        .WithErrorCode(vr116.Number.ToString())
                        .WithName(vr116.Type.ToString())
                .LessThan(0)
                    .When(x => x.ExternalRefineryAdjustments_OFU < 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr117.Message)
                        .WithErrorCode(vr117.Number.ToString())
                        .WithName(vr117.Type.ToString());

            RuleFor(x => x.Comments)
                .NotEmpty()
                   .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                       .WithMessage(vr023.Message)
                       .WithErrorCode(vr023.Number.ToString())
                       .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .NotEmpty()
                  .When(x => !string.IsNullOrEmpty(x.Comments))
                       .WithMessage(vr024.Message)
                       .WithErrorCode(vr024.Number.ToString())
                       .WithName(vr024.Type.ToString());

        }
    }
}
