﻿using Ae.Stratus.Core.Backend.EntityFramework.Base;
using Ae.Stratus.Core.Common.Api;
using OperatorProductionData.Models.Models.Documents;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using FileDetails = Ae.Stratus.Services.FileRepository.Models.FileDetails;

namespace OperatorProductionData.WebApi.Mappers
{
    public class OPDMapper : MapperBase
    {
        public OPDMapper()
        {
            CreateMap<FileDetails, DocumentDetails>();
            CreateMap<ProdDataDaily, ProdDataDailyRejected>();
            
            CreateMap<ProdDataMonthly, ProdDataMonthlyRejected>();
            CreateMap<ProdDataGasDaily, ProdDataGasDailyRejected>();
            CreateMap<ProdDataGasMonthly, ProdDataGasMonthlyRejected>();
            CreateMap<Document, DocumentReferences>()
                .ForMember(x => x.CompanyDocumentId, opt => opt.MapFrom(o => o.Id));

            //CreateMap<ProdDataDailyView, ProdDataDailyViewDetails>()
            //    .ForMember(x => x.OperatorReference, opt => opt.MapFrom(o => o.Json.OperatorReference))
            //    .ForMember(x => x.ParsedProductionDate, opt => opt.MapFrom(o => o.Json.ParsedProductionDate))
            //    .ForMember(x => x.Version, opt => opt.MapFrom(o => o.Json.Version))
            //    .ForMember(x => x.ErrorCountStr, opt => opt.MapFrom(o => DefineErrorCountStr(o.Json.ProblemDetails, o.ErrorCount)))
            //    .ForMember(x => x.Status, opt => opt.MapFrom(o => DefineStatus(o.Json.ProblemDetails)));

            //CreateMap<ProdDataGasDailyView, ProdDataGasDailyViewDetails>()
            //    .ForMember(x => x.OperatorReference, opt => opt.MapFrom(o => o.Json.OperatorReference))
            //    .ForMember(x => x.ParsedProductionDate, opt => opt.MapFrom(o => o.Json.ParsedProductionDate))
            //    .ForMember(x => x.Version, opt => opt.MapFrom(o => o.Json.Version))
            //    .ForMember(x => x.ErrorCountStr, opt => opt.MapFrom(o => DefineErrorCountStr(o.Json.ProblemDetails, o.ErrorCount)))
            //    .ForMember(x => x.Status, opt => opt.MapFrom(o => DefineStatus(o.Json.ProblemDetails)));

            //CreateMap<ProdDataGasMonthlyView, ProdDataGasMonthlyViewDetails>()
            //    .ForMember(x => x.OperatorReference, opt => opt.MapFrom(o => o.Json.OperatorReference))
            //    .ForMember(x => x.Version, opt => opt.MapFrom(o => o.Json.Version))
            //    .ForMember(x => x.ErrorCountStr, opt => opt.MapFrom(o => DefineErrorCountStr(o.Json.ProblemDetails, o.ErrorCount)))
            //    .ForMember(x => x.Status, opt => opt.MapFrom(o => DefineStatus(o.Json.ProblemDetails)));

            //CreateMap<ProdDataMonthlyView, ProdDataMonthlyViewDetails>()
            //    .ForMember(x => x.OperatorReference, opt => opt.MapFrom(o => o.Json.OperatorReference))
            //    .ForMember(x => x.Version, opt => opt.MapFrom(o => o.Json.Version))
            //    .ForMember(x => x.ErrorCountStr, opt => opt.MapFrom(o => DefineErrorCountStr(o.Json.ProblemDetails, o.ErrorCount)))
            //    .ForMember(x => x.Status, opt => opt.MapFrom(o => DefineStatus(o.Json.ProblemDetails)));
        }

        //private static string DefineStatus(IEnumerable<ApiProblemDetails>? problemDetails)
        //{
        //    if (problemDetails != null && problemDetails.Count() != 0)
        //        return "Recebido / Não Aceite";
        //    else
        //        return "Recebido";
        //}

        //private static string DefineErrorCountStr(IEnumerable<ApiProblemDetails>? problemDetails, int errorCount)
        //{
        //    if (problemDetails != null && problemDetails.Count() != 0)
        //        return errorCount.ToString();
        //    else
        //        return string.Empty;
        //}
    }
}
