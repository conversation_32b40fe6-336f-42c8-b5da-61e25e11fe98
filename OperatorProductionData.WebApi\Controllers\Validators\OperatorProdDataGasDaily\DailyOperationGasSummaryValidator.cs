﻿using FluentValidation;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Services.Services;
using OperatorProductionData.WebApi.Enums;
using Ae.Stratus.Core.Middleware.Services;
using FluentValidation.Results;
using Upstream.Models.Models;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasDaily
{

    public class DailyOperationGasSummaryValidator : AbstractValidator<DailyOperationGasSummary>
    {

        public DailyOperationGasSummaryValidator(string complexId, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPG-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr027 = validationRuleService.GetValidationRule("OPG-VR027");


            var facilities = referenceData?.Complexes?.FirstOrDefault(x => x.Id == complexId)?.Facilities;

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Must(x => facilities.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr027.Message, complexId))
                    .WithErrorCode(vr027.Number.ToString())
                    .WithName(vr027.Type.ToString());


            RuleFor(x => x.FeedGasMass)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.FeedGasEnergyOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.FeedGasVolumeOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.FeedGasVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.DomesticGasMass)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.HpFuelGasPowerGenerationMass)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.HpFuelGasPowerGenerationEstimatedEnergyBOE)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());

            RuleFor(x => x.HpFuelGasRefrigerationMass)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());

            RuleFor(x => x.HpFuelGasRefrigerationEstimatedEnergyBOE)
              .Cascade(CascadeMode.Stop)
              .NotNull()
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .GreaterThanOrEqualTo(0)
                  .WithMessage(vr003.Message)
                  .WithErrorCode(vr003.Number.ToString())
                  .WithName(vr003.Type.ToString());

            RuleFor(x => x.HpFuelGasTotalMass)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());

            RuleFor(x => x.HpFuelGasTotalEstimatedEnergyBOE)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());

            RuleFor(x => x.LpFuelGasRegenerationHeatersMass)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasRegenerationHeatersEstimatedEnergyBOE)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());

            RuleFor(x => x.LpFuelGasHotOilHeatersMass)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasHotOilHeatersEstimatedEnergyBOE)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasDistributionMass)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());

            RuleFor(x => x.LpFuelGasDistributionEstimateBoe)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasTotalMass)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasTotalEstimatedEnergyBOE)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());

            RuleFor(x => x.FuelGasTotalMass)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.FuelGasTotalEstimatedEnergyBOE)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());

            RuleFor(x => x.AcidGasIncinerationMass)
            .Cascade(CascadeMode.Stop)
            .NotNull()
               .WithMessage(vr001.Message)
               .WithErrorCode(vr001.Number.ToString())
               .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
               .WithMessage(vr003.Message)
               .WithErrorCode(vr003.Number.ToString())
               .WithName(vr003.Type.ToString());


            RuleFor(x => x.AcidGasFlareMass)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.PentaneFuelToHotOilHeatersMass)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneIncineratedMass)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneSupplyStabilizerMass)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());



            RuleFor(x => x.PentaneTotalMass)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringWetMass)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringWetMMSCFD)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringDryMass)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringDryMMSCFD)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringMarineMass)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringMarineMMSCFD)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());



            RuleFor(x => x.DomesticGasEnergyOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.DomesticGasEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.DomesticGasVolumeOFU)
               .Cascade(CascadeMode.Stop)
               .NotNull()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
               .GreaterThanOrEqualTo(0)
                   .WithMessage(vr003.Message)
                   .WithErrorCode(vr003.Number.ToString())
                   .WithName(vr003.Type.ToString());



            RuleFor(x => x.DomesticGasVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.HpFuelGasPowerGenerationEnergyOFU)
              .Cascade(CascadeMode.Stop)
              .NotNull()
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .GreaterThanOrEqualTo(0)
                  .WithMessage(vr003.Message)
                  .WithErrorCode(vr003.Number.ToString())
                  .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasPowerGenerationEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasPowerGenerationVolumeOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasPowerGenerationVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.HpFuelGasRefrigerationEnergyOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasRefrigerationEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.HpFuelGasRefrigerationVolumeOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasRefrigerationVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasTotalEnergyOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasTotalEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.HpFuelGasTotalVolumeOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());


            RuleFor(x => x.HpFuelGasTotalVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasRegenerationHeatersEnergyOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasRegenerationHeatersEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasRegenerationHeatersVolumeOFU)
             .Cascade(CascadeMode.Stop)
             .NotNull()
                 .WithMessage(vr001.Message)
                 .WithErrorCode(vr001.Number.ToString())
                 .WithName(vr001.Type.ToString())
             .GreaterThanOrEqualTo(0)
                 .WithMessage(vr003.Message)
                 .WithErrorCode(vr003.Number.ToString())
                 .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasRegenerationHeatersVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasHotOilHeatersEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasHotOilHeatersEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasHotOilHeatersVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasHotOilHeatersVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.LpFuelGasDistributionEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasDistributionEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasDistributionVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasDistributionVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasTotalEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.LpFuelGasTotalEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasTotalVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.LpFuelGasTotalVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.FuelGasTotalEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.FuelGasTotalEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.FuelGasTotalVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.FuelGasTotalVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.AcidGasIncinerationEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());



            RuleFor(x => x.AcidGasIncinerationEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.AcidGasIncinerationVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.AcidGasIncinerationVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.AcidGasFlareEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());

            RuleFor(x => x.AcidGasFlareEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.AcidGasFlareVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.AcidGasFlareVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneFuelToHotOilHeatersEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneFuelToHotOilHeatersEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.PentaneFuelToHotOilHeatersVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneFuelToHotOilHeatersVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneIncineratedEnergyOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneIncineratedEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.PentaneIncineratedVolumeOFU)
            .Cascade(CascadeMode.Stop)
            .NotNull()
                .WithMessage(vr001.Message)
                .WithErrorCode(vr001.Number.ToString())
                .WithName(vr001.Type.ToString())
            .GreaterThanOrEqualTo(0)
                .WithMessage(vr003.Message)
                .WithErrorCode(vr003.Number.ToString())
                .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneIncineratedVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneSupplyStabilizerEnergyOFU)
           .Cascade(CascadeMode.Stop)
           .NotNull()
               .WithMessage(vr001.Message)
               .WithErrorCode(vr001.Number.ToString())
               .WithName(vr001.Type.ToString())
           .GreaterThanOrEqualTo(0)
               .WithMessage(vr003.Message)
               .WithErrorCode(vr003.Number.ToString())
               .WithName(vr003.Type.ToString());

            RuleFor(x => x.PentaneSupplyStabilizerEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.PentaneSupplyStabilizerVolumeOFU)
          .Cascade(CascadeMode.Stop)
          .NotNull()
              .WithMessage(vr001.Message)
              .WithErrorCode(vr001.Number.ToString())
              .WithName(vr001.Type.ToString())
          .GreaterThanOrEqualTo(0)
              .WithMessage(vr003.Message)
              .WithErrorCode(vr003.Number.ToString())
              .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneSupplyStabilizerVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.PentaneTotalVolumeOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());



            RuleFor(x => x.PentaneTotalVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringWetEnergyOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.NonRoutineFlaringWetEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringWetVolumeOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringWetVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringDryEnergyOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringDryEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringDryVolumeOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringDryVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringMarineEnergyOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

            RuleFor(x => x.NonRoutineFlaringMarineEnergySI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringMarineVolumeOFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());


            RuleFor(x => x.NonRoutineFlaringMarineVolumeSI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString());

        }
        
    }


    }