﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyOperationGasSummary
    {
        public string FacilityId { get; set; }
        public decimal FeedGasMass { get; set; }
        public decimal FeedGasEnergyOFU { get; set; }
        public decimal FeedGasEnergySI { get; set; }
        public decimal FeedGasVolumeOFU { get; set; }
        public decimal FeedGasVolumeSI { get; set; }
        public decimal DomesticGasMass { get; set; }
        public decimal DomesticGasEnergyOFU { get; set; }
        public decimal DomesticGasEnergySI { get; set; }
        public decimal DomesticGasVolumeOFU { get; set; }
        public decimal DomesticGasVolumeSI { get; set; }
        public decimal HpFuelGasPowerGenerationMass { get; set; }
        public decimal HpFuelGasPowerGenerationEnergyOFU { get; set; }
        public decimal HpFuelGasPowerGenerationEnergySI { get; set; }
        public decimal HpFuelGasPowerGenerationVolumeOFU { get; set; }
        public decimal HpFuelGasPowerGenerationVolumeSI { get; set; }
        public decimal HpFuelGasPowerGenerationEstimatedEnergyBOE { get; set; }
        public decimal HpFuelGasRefrigerationMass { get; set; }
        public decimal HpFuelGasRefrigerationEnergyOFU { get; set; }
        public decimal HpFuelGasRefrigerationEnergySI { get; set; }
        public decimal HpFuelGasRefrigerationVolumeOFU { get; set; }
        public decimal HpFuelGasRefrigerationVolumeSI { get; set; }
        public decimal HpFuelGasRefrigerationEstimatedEnergyBOE { get; set; }
        public decimal HpFuelGasTotalMass { get; set; }
        public decimal HpFuelGasTotalEnergyOFU { get; set; }
        public decimal HpFuelGasTotalEnergySI { get; set; }
        public decimal HpFuelGasTotalVolumeOFU { get; set; }
        public decimal HpFuelGasTotalVolumeSI { get; set; }
        public decimal HpFuelGasTotalEstimatedEnergyBOE { get; set; }
        public decimal LpFuelGasRegenerationHeatersMass { get; set; }
        public decimal LpFuelGasRegenerationHeatersEnergyOFU { get; set; }
        public decimal LpFuelGasRegenerationHeatersEnergySI { get; set; }
        public decimal LpFuelGasRegenerationHeatersVolumeOFU { get; set; }
        public decimal LpFuelGasRegenerationHeatersVolumeSI { get; set; }
        public decimal LpFuelGasRegenerationHeatersEstimatedEnergyBOE { get; set; }
        public decimal LpFuelGasHotOilHeatersMass { get; set; }
        public decimal LpFuelGasHotOilHeatersEnergyOFU { get; set; }
        public decimal LpFuelGasHotOilHeatersEnergySI { get; set; }
        public decimal LpFuelGasHotOilHeatersVolumeOFU { get; set; }
        public decimal LpFuelGasHotOilHeatersVolumeSI { get; set; }
        public decimal LpFuelGasHotOilHeatersEstimatedEnergyBOE { get; set; }
        public decimal LpFuelGasDistributionMass { get; set; }
        public decimal LpFuelGasDistributionEnergyOFU { get; set; }
        public decimal LpFuelGasDistributionEnergySI { get; set; }
        public decimal LpFuelGasDistributionVolumeOFU { get; set; }
        public decimal LpFuelGasDistributionVolumeSI { get; set; }
        public decimal LpFuelGasDistributionEstimateBoe { get; set; }
        public decimal LpFuelGasTotalMass { get; set; }
        public decimal LpFuelGasTotalEnergyOFU { get; set; }
        public decimal LpFuelGasTotalEnergySI { get; set; }
        public decimal LpFuelGasTotalVolumeOFU { get; set; }
        public decimal LpFuelGasTotalVolumeSI { get; set; }
        public decimal LpFuelGasTotalEstimatedEnergyBOE { get; set; }
        public decimal FuelGasTotalMass { get; set; }
        public decimal FuelGasTotalEnergyOFU { get; set; }
        public decimal FuelGasTotalEnergySI { get; set; }
        public decimal FuelGasTotalVolumeOFU { get; set; }
        public decimal FuelGasTotalVolumeSI { get; set; }
        public decimal FuelGasTotalEstimatedEnergyBOE { get; set; }
        public decimal AcidGasIncinerationMass { get; set; }
        public decimal AcidGasIncinerationEnergyOFU { get; set; }
        public decimal AcidGasIncinerationEnergySI { get; set; }
        public decimal AcidGasIncinerationVolumeOFU { get; set; }
        public decimal AcidGasIncinerationVolumeSI { get; set; }
        public decimal AcidGasFlareMass { get; set; }
        public decimal AcidGasFlareEnergyOFU { get; set; }
        public decimal AcidGasFlareEnergySI { get; set; }
        public decimal AcidGasFlareVolumeOFU { get; set; }
        public decimal AcidGasFlareVolumeSI { get; set; }
        public decimal PentaneFuelToHotOilHeatersMass { get; set; }
        public decimal PentaneFuelToHotOilHeatersEnergyOFU { get; set; }
        public decimal PentaneFuelToHotOilHeatersEnergySI { get; set; }
        public decimal PentaneFuelToHotOilHeatersVolumeOFU { get; set; }
        public decimal PentaneFuelToHotOilHeatersVolumeSI { get; set; }
        public decimal PentaneIncineratedMass { get; set; }
        public decimal PentaneIncineratedEnergyOFU { get; set; }
        public decimal PentaneIncineratedEnergySI { get; set; }
        public decimal PentaneIncineratedVolumeOFU { get; set; }
        public decimal PentaneIncineratedVolumeSI { get; set; }
        public decimal PentaneSupplyStabilizerMass { get; set; }
        public decimal PentaneSupplyStabilizerEnergyOFU { get; set; }
        public decimal PentaneSupplyStabilizerEnergySI { get; set; }
        public decimal PentaneSupplyStabilizerVolumeOFU { get; set; }
        public decimal PentaneSupplyStabilizerVolumeSI { get; set; }
        public decimal PentaneTotalMass { get; set; }
        public decimal PentaneTotalVolumeOFU { get; set; }
        public decimal PentaneTotalVolumeSI { get; set; }
        public decimal NonRoutineFlaringWetMass { get; set; }
        public decimal NonRoutineFlaringWetEnergyOFU { get; set; }
        public decimal NonRoutineFlaringWetEnergySI { get; set; }
        public decimal NonRoutineFlaringWetVolumeOFU { get; set; }
        public decimal NonRoutineFlaringWetVolumeSI { get; set; }
        public decimal NonRoutineFlaringWetMMSCFD { get; set; }
        public decimal NonRoutineFlaringDryMass { get; set; }
        public decimal NonRoutineFlaringDryEnergyOFU { get; set; }
        public decimal NonRoutineFlaringDryEnergySI { get; set; }
        public decimal NonRoutineFlaringDryVolumeOFU { get; set; }
        public decimal NonRoutineFlaringDryVolumeSI { get; set; }
        public decimal NonRoutineFlaringDryMMSCFD { get; set; }
        public decimal NonRoutineFlaringMarineMass { get; set; }
        public decimal NonRoutineFlaringMarineEnergyOFU { get; set; }
        public decimal NonRoutineFlaringMarineEnergySI { get; set; }
        public decimal NonRoutineFlaringMarineVolumeOFU { get; set; }
        public decimal NonRoutineFlaringMarineVolumeSI { get; set; }
        public decimal NonRoutineFlaringMarineMMSCFD { get; set; }

    }
}
