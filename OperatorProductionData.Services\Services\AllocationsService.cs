﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class AllocationsService : RepositoryBase<AllocationsEntity, Allocations>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public AllocationsService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitAllocations(Allocations Allocations)
        {
            _dbContext.Allocations.Add(
                _mapper.Map<Allocations,AllocationsEntity>(Allocations)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
