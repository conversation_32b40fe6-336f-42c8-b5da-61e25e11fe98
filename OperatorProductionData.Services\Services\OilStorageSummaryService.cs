﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class OilStorageSummaryService : RepositoryBase<OilStorageSummaryEntity, OilStorageSummary>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public OilStorageSummaryService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOilStorageSummary(OilStorageSummary OilStorageSummary)
        {
            _dbContext.OilStorageSummary.Add(
                _mapper.Map<OilStorageSummary, OilStorageSummaryEntity>(OilStorageSummary)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
