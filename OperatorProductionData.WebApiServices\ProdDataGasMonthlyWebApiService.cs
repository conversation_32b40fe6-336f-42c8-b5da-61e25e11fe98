﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataGasMonthlyWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataGasMonthlyWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> SubmitProdDataGasMonthly(GasReferenceData referenceData, ProdDataGasMonthly prodDataGasMonthly)
        {
            var obj = new ReferenceDataProdDataGasMonthly
            {
                GasReferenceData = referenceData,
                ProdDataGasMonthly = prodDataGasMonthly
            };

            HttpResponseMessage response = await _client.PostAsJsonAsync("proddatagasmonthly/SubmitProdDataGasMonthly", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error getting model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error getting model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> ChangeProdDataGasMonthly(GasReferenceData referenceData, ProdDataGasMonthly prodDataGasMonthly)
        {
            var obj = new ReferenceDataProdDataGasMonthly
            {
                GasReferenceData = referenceData,
                ProdDataGasMonthly = prodDataGasMonthly
            };

            HttpResponseMessage response = await _client.PutAsJsonAsync("proddatagasmonthly/ChangeProdDataGasMonthly", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error changing model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error changing model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>>> GetList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatagasmonthly/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasMonthly>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>>> GetViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatagasmonthly/getviewlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<Guid?>> GetByComplexAndProductionDate(string complexId, int productionYear, int productionMonth)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "complexId", complexId },
                { "productionYear", productionYear.ToString() },
                { "productionMonth", productionMonth.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasmonthly/getbycomplexandproductiondate" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<Guid?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetByComplexAndProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<Guid?> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<Guid?>>();
            if (apiResponse == null)
            {
                return new ApiResponse<Guid?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting monthly GetByComplexAndProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>>> GetViewDetailsList(string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasmonthly/getviewdetailslist");
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataGasMonthlyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataGasMonthlyGeneric>> GetProdDataGasMonthlyViewJson(Guid id)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "id", id.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasmonthly/getproddatagasmonthlyviewjson" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataGasMonthlyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting daily dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataGasMonthlyGeneric> apiResponse = await httpResponseMessage
                                            .Content
                                            .ReadFromJsonAsync<ApiResponse<ProdDataGasMonthlyGeneric>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataGasMonthlyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily dashboard list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

    }
}
