﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyStorageLoading
    {
        public string FacilityId { get; set; }
        public string GasTypeId { get; set; }
        public string GasTankId { get; set; }
        public decimal StorageTankLevel { get; set; }
        public decimal StorageTankVolume_OFU { get; set; }
        public decimal StorageTankVolume_SI { get; set; }
        public decimal StorageTankEnergy_OFU { get; set; }
        public decimal StorageTankEnergy_SI { get; set; }
        public decimal StorageOpeningInventoryVolume_OFU { get; set; }
        public decimal StorageOpeningInventoryVolume_SI { get; set; }
        public decimal StorageClosingInventoryVolume_OFU { get; set; }
        public decimal StorageClosingInventoryVolume_SI { get; set; }
        public decimal StorageAvailableVolume_OFU { get; set; }
        public decimal StorageAvailableVolume_SI { get; set; }
        public decimal StorageLoadingAvailableVolume_OFU { get; set; }
        public decimal StorageLoadingAvailableVolume_SI { get; set; }
        public decimal EstimatedEnergy_BOE { get; set; }
    }
}
