﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using OperatorProductionData.Models.Utilities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class ProdDataGasDaily : IVersionable
    {
        [Key]
        public Guid Id { get; set; }
        public string ReasonId { get; set; }
        public string ReasonDescription { get; set; }
        public string ComplexId { get; set; }
        public string ProductionDate { get; set; }
        public virtual DateTime ParsedProductionDate
        {
            set
            {
                _tempProductionDate = DateUtils.ConvertDateTimeString(ProductionDate, "yyyy-MM-dd");
            }

            get
            {
                return _tempProductionDate;
            }
        }

        private DateTime _tempProductionDate;
        public string? OperatorReference { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
        public ICollection<DailyDomesticGasDeliverySummary> DailyDomesticGasDeliverySummary { get; set; }
        public ICollection<DailyFeedGasUsage> DailyFeedGasUsage { get; set; }
        public ICollection<DailyFeedGasUsageGasComposition> DailyFeedGasUsageGasComposition { get; set; }
        public ICollection<DailyGasDelivery> DailyGasDelivery { get; set; }
        public ICollection<DailyGasSupply> DailyGasSupply { get; set; }
        public ICollection<DailyGasSupplySummary> DailyGasSupplySummary { get; set; }
        public ICollection<DailyOperationGasSummary> DailyOperationGasSummary { get; set; }
        public ICollection<DailyOperationGasSummaryByType> DailyOperationGasSummaryByType { get; set; }
        public ICollection<DailyOperationGasSummaryTotal> DailyOperationGasSummaryTotal { get; set; }
        public ICollection<DailyPerformanceMetrics> DailyPerformanceMetrics { get; set; }
        public ICollection<DailyFeedGasUsageGasCompositionTotal> DailyFeedGasUsageGasCompositionTotal { get; set; }
        public ICollection<DailyStorageLoading> DailyStorageLoading { get; set; }
        public ICollection<DailyStorageLoadingSummary> DailyStorageLoadingSummary { get; set; }
        public ICollection<DailyStreamFeed> DailyStreamFeed { get; set; }
        public ICollection<DailyShipping> DailyShipping { get; set; }
        public ICollection<DailyGasSubmittedDocuments>? DailyGasSubmittedDocuments { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public Guid VersionsCollectionId { get; set; }
        public int Version { get; set; }
    }
}
