﻿namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{
    public class AggregateProducionLossSummaryAndProductionLoss
    {
        public string FacilityId { get; set; }
        public string SystemId { get; set; }
        public string EquipmentId { get; set; }
        public string WellId { get; set; }
        public decimal PlannedOilLoss_OFU { get; set; }
        public decimal UnplannedOilLoss_OFU { get; set; }
        public decimal PlannedOilLoss_SI { get; set; }
        public decimal UnplannedOilLoss_SI { get; set; }
        public string LossMotiveSummaryId { get; set; }
        public string LossMotiveId { get; set; }
        public decimal OilLoss_OFU { get; set; }
        public decimal OilLoss_SI { get; set; }
    }
}
