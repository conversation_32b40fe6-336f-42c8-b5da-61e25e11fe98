﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Ae.Stratus.Core.Middleware.Services;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.Notifications;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.Services.DashboardServices.Monthly;
using OperatorProductionData.Services.SignalRHubs;
using OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly;
using OperatorProductionData.WebApi.Enums;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataMonthlyController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataMonthlyService _service { get; set; }
        private ProdDataMonthlyRejectedService _rejectedService { get; set; }
        private ProdDataMonthlyViewService _viewService { get; set; }
        private ProdDataMonthlyVerificationViewService _verificationViewService { get; set; }
        private MonthlyDashboardChartsService _monthlyDashboardChartsService { get; set; }
        private ValidationRuleCoreService _validationRuleService { get; set; }
        private readonly IHubContext<ProdDataNotificationsHub> _prodDataMonthlyNotificationsHub;
        private IMapper _mapper { get; set; }

        public ProdDataMonthlyController(
            ILogger<ProdDataMonthlyController> logger,
            ProdDataMonthlyService service,
            ProdDataMonthlyRejectedService rejectedService,
            ProdDataMonthlyViewService viewService,
            ProdDataMonthlyVerificationViewService verificationViewService,
            MonthlyDashboardChartsService monthlyDashboardChartsService,
            ValidationRuleCoreService validationRuleService,
            IHubContext<ProdDataNotificationsHub> prodDataMonthlyNotificationsHub,
            IMapper mapper
        )
        {
            _logger = logger;
            _service = service;
            _rejectedService = rejectedService;
            _viewService = viewService;
            _verificationViewService = verificationViewService;
            _monthlyDashboardChartsService = monthlyDashboardChartsService;
            _validationRuleService = validationRuleService;
            _prodDataMonthlyNotificationsHub = prodDataMonthlyNotificationsHub;
            _mapper = mapper;
        }

        [HttpPost]
        [Route("SubmitProdDataMonthly")]
        public async Task<IActionResult> SubmitProdDataMonthly([FromBody] InfrastructureProdDataMonthly infrastructureProdDataMonthly)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new ProdDataMonthlyValidator(EnumAction.Add, infrastructureProdDataMonthly.Infrastructure, _validationRuleService);
                var result = validator.Validate(infrastructureProdDataMonthly.ProdDataMonthly);
                if (!result.IsValid)
                {
                    apiResponse.Status = ApiResponseStatus.Error;
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );

                    //Register Rejected ProdDataDaily
                    var prodDataMonthlyRejected = _mapper.Map<ProdDataMonthly, ProdDataMonthlyRejected>(infrastructureProdDataMonthly.ProdDataMonthly);
                    prodDataMonthlyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataMonthlyRejected);
                    await GenerateNotification(infrastructureProdDataMonthly,
                        new DateTime(infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, 1), NotificationStyle.Danger, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, false);
                }
                else
                {
                    var newModel = await _service.Add(infrastructureProdDataMonthly.ProdDataMonthly);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    await GenerateNotification(infrastructureProdDataMonthly,
                        new DateTime(infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, 1), NotificationStyle.Success, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, true);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem submitting Product Data Monthly.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPut]
        [Route("ChangeProdDataMonthly")]
        public async Task<IActionResult> ChangeProdDataMonthly([FromBody] InfrastructureProdDataMonthly infrastructureProdDataMonthly)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new ProdDataMonthlyValidator(EnumAction.Update, infrastructureProdDataMonthly.Infrastructure, _validationRuleService);
                var result = validator.Validate(infrastructureProdDataMonthly.ProdDataMonthly);
                if (!result.IsValid)
                {
                    apiResponse.Status = ApiResponseStatus.Error;
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.Severity.ToString()[..1], //('E' or 'W' )
                                ProblemCode = x.ErrorCode
                            })
                    );

                    var prodDataMonthlyRejected = _mapper.Map<ProdDataMonthly, ProdDataMonthlyRejected>(infrastructureProdDataMonthly.ProdDataMonthly);
                    prodDataMonthlyRejected.ProblemDetails = apiResponse.Problems;
                    await _rejectedService.Add(prodDataMonthlyRejected);
                    await GenerateNotification(infrastructureProdDataMonthly,
                        new DateTime(infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, 1), NotificationStyle.Danger, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, false);
                }
                else
                {
                    var newModel = await _service.Update(infrastructureProdDataMonthly.ProdDataMonthly);
                    apiResponse.Response = new IdGuidResponse { Id = newModel.VersionsCollectionId };

                    await GenerateNotification(infrastructureProdDataMonthly,
                        new DateTime(infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, 1), NotificationStyle.Success, infrastructureProdDataMonthly.ProdDataMonthly.ProductionMonth, infrastructureProdDataMonthly.ProdDataMonthly.ProductionYear, true);
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem updating Product Data Monthly.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataMonthly Post - getlist");
            ApiResponse<GridDataLoadResponse<ProdDataMonthly>> res = new ApiResponse<GridDataLoadResponse<ProdDataMonthly>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataMonthly>> apiResponse = res;
                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error getting sorted, filtered and paged list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Post - getlist");
                return Ok(res);
            }
        }

        [HttpPost]
        [Route("getviewlist")]
        public async Task<IActionResult> GetViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataMonthly Post - getviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>> res = new ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>> apiResponse = res;
                apiResponse.Response = await _viewService.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error getting sorted, filtered and paged view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Post - getviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getviewdetailslist")]
        public async Task<IActionResult> GetViewListDetails()
        {
            _logger.LogInformation("Enter ProdDataMonthly GET - getviewdetailslist");
            ApiResponse<ICollection<ProdDataMonthlyViewDetails>> res = new ApiResponse<ICollection<ProdDataMonthlyViewDetails>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {

                res.Response = await _viewService.GetAllViewDetails();

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error getting sorted, filtered and paged view details list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly GET - getviewdetailslist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getproddatamonthlyviewjson")]
        public async Task<IActionResult> GetProdDataMonthlyViewJson([FromQuery] Guid id)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetProdDataGasDailyViewJson");
            ApiResponse<ProdDataMonthlyGeneric> res = new ApiResponse<ProdDataMonthlyGeneric>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataMonthlyGeneric> apiResponse = res;
                var json = await _viewService.GetJson(id);

                apiResponse.Response = json;

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetProdDataGasDailyViewJson");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetProdDataGasDailyViewJson");
                return Ok(res);
            }
        }

        //[HttpGet]
        //[Route("getmonthlydashboarddata")]
        //public async Task<IActionResult> GetMonthlyDashboardData([FromQuery] string[] blockIds)
        //{
        //    _logger.LogInformation("Enter ProdDataMonthly - GetMonthlyDashboardData");
        //    ApiResponse<IEnumerable<MonthlyDashboardData>> res = new ApiResponse<IEnumerable<MonthlyDashboardData>>
        //    {
        //        Status = ApiResponseStatus.Success
        //    };
        //    try
        //    {
        //        ApiResponse<IEnumerable<MonthlyDashboardData>> apiResponse = res;
        //        apiResponse.Response = await _verificationViewService.GetMonthlyDashboardData(blockIds);
        //        return Ok(res);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "ProdDataMonthly - Error GetMonthlyDashboardData");
        //        List<ApiProblemDetails> problems = new List<ApiProblemDetails>
        //        {
        //            new ApiProblemDetails
        //            {
        //                Status = StatusCodes.Status400BadRequest,
        //                Title = ex.Message,
        //                Detail = ex.StackTrace
        //            }
        //        };
        //        res.Status = ApiResponseStatus.Error;
        //        res.Problems = problems;
        //        _logger.LogInformation("Exit ProdDataMonthly Get - GetMonthlyDashboardData");
        //        return Ok(res);
        //    }
        //}

        [HttpGet]
        [Route("getmonthlydashboarddatabyrange")]
        public async Task<IActionResult> GetMonthlyDashboardDataByRange(
            [FromQuery] int productionMonthStart,
            [FromQuery] int productionMonthEnd,
            [FromQuery] int productionYearStart,
            [FromQuery] int productionYearEnd,
            [FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetMonthlyDashboardDataByRange");
            ApiResponse<ICollection<ProdDataMonthlyVerificationView>> res = new ApiResponse<ICollection<ProdDataMonthlyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdDataMonthlyVerificationView>> apiResponse = res;
                apiResponse.Response = await _verificationViewService.GetAllByProductionDateRange(
                    productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd, blockIds);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetMonthlyDashboardDataByRange");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetMonthlyDashboardDataByRange");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getquarterlydashboarddata")]
        public async Task<IActionResult> GetQuarterlyDashboardData([FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetQuarterlyDashboardData");
            ApiResponse<IEnumerable<QuarterlyDashboardData>> res = new ApiResponse<IEnumerable<QuarterlyDashboardData>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<IEnumerable<QuarterlyDashboardData>> apiResponse = res;
                apiResponse.Response = await _verificationViewService.GetQuarterlyDashboardData(blockIds);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetQuarterlyDashboardData");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetQuarterlyDashboardData");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getyearlydashboarddata")]
        public async Task<IActionResult> GetYearlyDashboardData([FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetYearlyDashboardData");
            ApiResponse<IEnumerable<YearlyDashboardData>> res = new ApiResponse<IEnumerable<YearlyDashboardData>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<IEnumerable<YearlyDashboardData>> apiResponse = res;
                apiResponse.Response = await _verificationViewService.GetYearlyDashboardData(blockIds);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetYearlyDashboardData");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetYearlyDashboardData");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getmonthlydashboarddata")]
        public async Task<IActionResult> GetMonthlyDashboardData(
            [FromQuery] string productionYear,
            [FromQuery] string productionMonth,
            [FromQuery] string[] blockIds)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetMonthlyDashboardData");
            ApiResponse<IEnumerable<ProductionDashboardDto>> res = new ApiResponse<IEnumerable<ProductionDashboardDto>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<IEnumerable<ProductionDashboardDto>> apiResponse = res;
                apiResponse.Response = await _monthlyDashboardChartsService.GetMonthlyProductionDashboardCharts(
                    Int16.Parse(productionYear), Int16.Parse(productionMonth), blockIds);

                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetMonthlyDashboardData");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetMonthlyDashboardData");
                return Ok(res);
            }
        }

        private async Task GenerateNotification(InfrastructureProdDataMonthly infrastructureProdDataMonthly, DateTime productionDate, NotificationStyle style, int productionMonthFilter, int productionYearFilter, bool isValid)
        {
            try
            {
                await _service.UpdateProdDataMonthlyRejectedMaterializedView(productionMonthFilter, productionYearFilter);

                if (isValid)
                {
                    await _service.UpdateProdDataMonthlyVerificationMaterializedView(productionMonthFilter, productionYearFilter);
                }

                var methodName = "NotifyProdDataSubmission";
                await _prodDataMonthlyNotificationsHub.Clients.All.SendAsync(
                    methodName,
                    new ProdDataNotification
                    {
                        Type = "ProdDataMonthly",
                        BlockId = infrastructureProdDataMonthly.ProdDataMonthly.BlockId,
                        ProductionDate = productionDate,
                        Date = DateTime.Now,
                        NotificationStyle = style,
                    });
            }
            catch (Exception ex)
            {
                //
            }
        }


        [HttpGet]
        [Route("getbyblockandproductiondate")]
        public async Task<IActionResult> GetByBlockAndProductionDate(
            [FromQuery] string blockId,
            [FromQuery] int productionYear,
            [FromQuery] int productionMonth
        )
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetByBlockAndProductionDate");
            ApiResponse<ProdDataInfo?> res = new ApiResponse<ProdDataInfo?>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ProdDataInfo?> apiResponse = res;
                apiResponse.Response = await _service.GetByBlockAndProductionDate(blockId, productionYear, productionMonth);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetByBlockAndProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetByBlockAndProductionDate");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getbyid")]
        public async Task<IActionResult> GetById([FromQuery] string blockId, [FromQuery] string productionYear, [FromQuery] string productionMonth)
        {
            _logger.LogInformation("Enter ProdDataMonthlyController - GetById");
            ApiResponse<ProdDataMonthlyView> res = new ApiResponse<ProdDataMonthlyView>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                var filteringOptions = new List<FilteringOptions>
                {
                    new() {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "BlockId",
                        Value = blockId,
                    },
                    new() {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "ProductionYear",
                        Value = productionYear,
                    },
                    new() {
                        Condition = FilteringOptions.FilteringCondition.Equals,
                        PropertyName = "ProductionMonth",
                        Value = productionMonth,
                    }
                };

                var options = new GridDataLoadOptions
                {
                    FilteringOptions = filteringOptions,
                    PageIndex = 0,
                    PageSize = 1
                };

                ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>> apiResponse = new ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>>();
                apiResponse.Response = await _viewService.GetSortedFilteredPagedList(options);

                if (apiResponse.Response.Models.Count == 1) {
                    res.Response = apiResponse.Response.Models.ElementAt(0);

                    return Ok(res);
                }
                return BadRequest(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthlyController - Error GetById");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataDailyController Get - GetById");
                return Ok(res);
            }
        }
    }
}
