﻿using System;

namespace OperatorProductionData.Models.Models.Dashboard
{
    public class OnlineDashboardData
    {
        public string BlockId { get; set; }
        public string Date { get; set; }
        public string TimeStart { get; set; }
        public string TimeEnd { get; set; }
        public DateTime ParsedTimeStart { get; set; }
        public DateTime ParsedTimeEnd { get; set; }
        public DateTime ReferenceDate { get; set; }
        public decimal OilProd { get; set; }
        public decimal GasProd { get; set; }
        public decimal WaterProd { get; set; }
        public decimal LastDayOilProd { get; set; }
        public decimal LastDayGasProd { get; set; }
        public decimal LastDayWaterProd { get; set; }
        public decimal LastDayTotalOilProd { get; set; }
        public decimal LastDayTotalGasProd { get; set; }
        public decimal LastDayTotalWaterProd { get; set; }
        public bool CumulatedValues { get; set; }
    }
}
