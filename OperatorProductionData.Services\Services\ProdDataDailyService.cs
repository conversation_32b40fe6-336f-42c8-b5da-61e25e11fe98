﻿using MongoDB.Bson;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataDailyService : BaseRepository<ProdDataDaily>
    {
        public ProdDataDailyService(IMongoCollection<ProdDataDaily> container) : base(container)
        {
        }

        /// <summary>
        /// Method to get Info about existing ProdDataDaily's for the given BlockId and ProductionDate
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="productionDate"></param>
        /// <returns></returns>
        public async Task<ProdDataInfo?> GetByBlockAndProductionDate(string blockId, string productionDate)
        {
            var filterBuilder = Builders<ProdDataDaily>.Filter;
            var filter = filterBuilder.Eq(x => x.BlockId, blockId)
                            & filterBuilder.Eq(x => x.ProductionDate, productionDate)
                            ;
            var prodDataDaily = await GetAll(filter);

            return prodDataDaily
                    .OrderByDescending(x => x.CreatedDate)
                    .Select(x => new ProdDataInfo(x.VersionsCollectionId, x.Version)).FirstOrDefault();
        }

        public async Task UpdateProdDataDailyRejectedMaterializedView(string productionDateFilter)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionDate", productionDateFilter }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataDailyRejected" },
                    { "pipeline",
                        new BsonArray
                        {
                            new BsonDocument
                            {
                                { "$match", new BsonDocument
                                    {
                                        { "ProductionDate", productionDateFilter }
                                    }
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionDate", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "BlockId", "$BlockId"},
                    { "ProductionDate", "$ProductionDate"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataDailyView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }

        public async Task UpdateProdDataDailyVerificationMaterializedView(string productionDateFilter)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionDate", productionDateFilter }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "productionDate", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"BlockId", "$BlockId"},
                            {"ProductionDate", "$ProductionDate"},
                            {"ParsedProductionDate", "$ParsedProductionDate"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "BlockId", "$_id.BlockId" },
                    { "ProductionDate", "$_id.ProductionDate" },
                    { "ParsedProductionDate", "$_id.ParsedProductionDate"},
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    }, 1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataDailyVerificationView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }
    }
}
