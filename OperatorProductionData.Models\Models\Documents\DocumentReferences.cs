﻿using MongoDB.Bson.Serialization.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.Documents
{
    public class DocumentReferences
    {
        [Key]
        public Guid Id { get; set; }

        public Guid FileRepositoryDocumentId { get; set; }

        public Guid CompanyDocumentId { get; set; }

        public string CompanyCode { get; set; }

        public DateTime Created { get; set; }
    }
}
