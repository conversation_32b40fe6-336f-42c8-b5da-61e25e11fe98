﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <packageSources>
        <add key="Stratus" value="https://pkgs.dev.azure.com/AeEconomics/04a79edc-80fe-4052-94b8-933c73d4a766/_packaging/Stratus/nuget/v3/index.json" />
		<add key="AeStratusModules" value="https://pkgs.dev.azure.com/AeEconomics/bd811ece-193e-4c15-a484-b839cbd9af69/_packaging/AeStratusModules/nuget/v3/index.json" />
		<add key="SMP" value="https://pkgs.dev.azure.com/AeEconomics/SMP/_packaging/SMP/nuget/v3/index.json" />
		<add key="NuGet" value="https://api.nuget.org/v3/index.json" />
    </packageSources>
	<packageSourceCredentials>
		<Stratus>
			<add key="Username" value="Nuget_Packages_Feeds_Internal_PAT_2025" />
			<add key="ClearTextPassword" value="EjVeT0Fstl4wk2vgDlqcOtWwJmZAxUUVSyRv0pPohIfJ8z9mfi7kJQQJ99ALACAAAAAtZbQMAAASAZDOde86" />
		</Stratus>
		<AeStratusModules>
			<add key="Username" value="Nuget_Packages_Feeds_Internal_PAT_2025" />
			<add key="ClearTextPassword" value="EjVeT0Fstl4wk2vgDlqcOtWwJmZAxUUVSyRv0pPohIfJ8z9mfi7kJQQJ99ALACAAAAAtZbQMAAASAZDOde86" />
		</AeStratusModules>
		<SMP>
			<add key="Username" value="Nuget_Packages_Feeds_Internal_PAT_2025" />
			<add key="ClearTextPassword" value="EjVeT0Fstl4wk2vgDlqcOtWwJmZAxUUVSyRv0pPohIfJ8z9mfi7kJQQJ99ALACAAAAAtZbQMAAASAZDOde86" />
		</SMP>
	</packageSourceCredentials>
</configuration>