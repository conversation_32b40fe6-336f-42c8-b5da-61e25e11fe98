﻿using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OperatorProductionData.Services.Services;
using OperatorProductionData.Services.SignalRHubs;

namespace OperatorProductionData.Services.BackgroundServices
{
    public class ProdDataOnlineHubWorker : BackgroundService
    {
        //private readonly IHubContext<ProdDataOnlineHub> _prodDataOnlineHub;
        //private readonly IServiceProvider _serviceProvider;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public ProdDataOnlineHubWorker(IServiceScopeFactory serviceScopeFactory
                                //IHubContext<ProdDataOnlineHub> prodDataOnlineHub, IServiceProvider serviceProvider
            )
        {
            //_prodDataOnlineHub = prodDataOnlineHub;
            //_serviceProvider = serviceProvider;
            _serviceScopeFactory = serviceScopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            //### TODO CHECK THIS... ###

            //while (!stoppingToken.IsCancellationRequested)
            //{
            //    //using var scope = _serviceProvider.CreateScope();
            //    using (IServiceScope scope = _serviceScopeFactory.CreateScope())
            //    {
            //        var _prodDataOnlineHub = scope.ServiceProvider.GetRequiredService<IHubContext<ProdDataOnlineHub>>();
            //        var prodDataOnlineService = scope.ServiceProvider.GetRequiredService<ProdDataOnlineService>();

            //        var dashboardData = await prodDataOnlineService.GetProdDataOnlineDashboardData();
            //        var methodName = "TransferProductionOnlineData";

            //        await _prodDataOnlineHub.Clients.All.SendAsync(
            //            methodName,
            //            dashboardData,
            //            stoppingToken);

            //        await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken); //TODO :: Read from configuration
            //    }
            //}
        }
    }
}
