﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly
{
    public class MonthlyLiftingDetailValidator : AbstractValidator<MonthlyLiftingDetail>
    {

        public MonthlyLiftingDetailValidator(string blockId, int productionMonth, int productionYear, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr005 = validationRuleService.GetValidationRule("OPD-VR005");
            var vr009 = validationRuleService.GetValidationRule("OPD-VR009");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr066 = validationRuleService.GetValidationRule("OPD-VR066");
            var vr083 = validationRuleService.GetValidationRule("OPD-VR083");
            var vr084 = validationRuleService.GetValidationRule("OPD-VR084");
            var vr085 = validationRuleService.GetValidationRule("OPD-VR085");
            var vr086 = validationRuleService.GetValidationRule("OPD-VR086");
            var vr087 = validationRuleService.GetValidationRule("OPD-VR087");
            var vr088 = validationRuleService.GetValidationRule("OPD-VR088");
            var vr131 = validationRuleService.GetValidationRule("OPD-VR131");

            var blends = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Blends;

            RuleFor(x => x.StartDate)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedStartDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr131.Message,
                            ErrorCode = vr131.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedStartDate.Month != productionMonth)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr087.Message,
                            ErrorCode = vr087.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedStartDate.Year != productionYear)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr086.Message,
                            ErrorCode = vr086.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedStartDate >= DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr083.Message,
                            ErrorCode = vr083.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedStartDate > c.InstanceToValidate.ParsedEndDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr085.Message,
                            ErrorCode = vr085.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.EndDate)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (c.InstanceToValidate.ParsedEndDate == DateTime.MinValue)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr131.Message,
                            ErrorCode = vr131.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedEndDate.Month < productionMonth)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr087.Message,
                            ErrorCode = vr087.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedEndDate.Year < productionYear)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr086.Message,
                            ErrorCode = vr086.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedEndDate >= DateTime.Now)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr083.Message,
                            ErrorCode = vr083.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                    else if (c.InstanceToValidate.ParsedEndDate < c.InstanceToValidate.ParsedStartDate)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr085.Message,
                            ErrorCode = vr085.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.OperatorReference)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.VesselImo)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.VesselName)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.IsInternal)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.BlendId)
               .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => blends != null && blends.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.QuantityLoaded_OFU)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.QuantityLoaded_SI == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr066.Message)
                       .WithErrorCode(vr066.Number.ToString())
                       .WithName(vr066.Type.ToString());

            RuleFor(x => x.QuantityLoaded_SI)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.QuantityLoaded_OFU == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr066.Message)
                       .WithErrorCode(vr066.Number.ToString())
                       .WithName(vr066.Type.ToString());

            RuleFor(x => x.Gravity)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThan(1000)
                    .WithMessage(vr088.Message)
                    .WithErrorCode(vr088.Number.ToString())
                    .WithName(vr088.Type.ToString());

            RuleFor(x => x.BasicSedimentWater)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThan(100)
                    .WithMessage(vr005.Message)
                    .WithErrorCode(vr005.Number.ToString())
                    .WithName(vr005.Type.ToString());


            RuleFor(x => x.Salinity)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThan(100)
                    .WithMessage(vr005.Message)
                    .WithErrorCode(vr005.Number.ToString())
                    .WithName(vr005.Type.ToString())
                .When(x => x.Salinity != null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.Comments)
                .NotEmpty()
                   .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                       .WithMessage(vr023.Message)
                       .WithErrorCode(vr023.Number.ToString())
                       .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
               .NotEmpty()
                  .When(x => !string.IsNullOrEmpty(x.Comments))
                       .WithMessage(vr024.Message)
                       .WithErrorCode(vr024.Number.ToString())
                       .WithName(vr024.Type.ToString());


            RuleFor(x => x.Allocations)
                .Must(x => x != null)
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                   .WithMessage(vr009.Message)
                   .WithErrorCode(vr009.Number.ToString())
                   .WithName(vr009.Type.ToString());
            RuleForEach(x => x.Allocations)
                .SetValidator(x => new MonthlyAllocationsValidator(blockId, x.BlendId, validationRuleService, infrastructure));
        }


    }
}
