﻿using OperatorProductionData.Models.Utilities;
using System;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyOperationComments
    {
        public string ComplexId { get; set; }
        public string? FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string CommentDateTime { get; set; }
        public virtual DateTime ParsedCommentDateTime
        {
            set
            {
                _tempCommentDateTime = DateUtils.ConvertDateTimeString(CommentDateTime, "yyyy-MM-dd HH:mm:ss");
            }

            get
            {
                return _tempCommentDateTime;
            }
        }

        private DateTime _tempCommentDateTime;
        public string Comments { get; set; }
        public string CommentedBy { get; set; }
    }
}
