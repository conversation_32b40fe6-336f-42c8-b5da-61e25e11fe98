﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataDaily
{
    public class DailyOperationSummaryByFacilityValidator : AbstractValidator<DailyOperationSummaryByFacility>
    {
        public DailyOperationSummaryByFacilityValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr004 = validationRuleService.GetValidationRule("OPD-VR004");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr016 = validationRuleService.GetValidationRule("OPD-VR016");
            var vr017 = validationRuleService.GetValidationRule("OPD-VR017");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr026 = validationRuleService.GetValidationRule("OPD-VR026");
            var vr027 = validationRuleService.GetValidationRule("OPD-VR027");
            var vr028 = validationRuleService.GetValidationRule("OPD-VR028");
            var vr029 = validationRuleService.GetValidationRule("OPD-VR029");
            var vr030 = validationRuleService.GetValidationRule("OPD-VR030");
            var vr031 = validationRuleService.GetValidationRule("OPD-VR031");
            var vr032 = validationRuleService.GetValidationRule("OPD-VR032");
            var vr033 = validationRuleService.GetValidationRule("OPD-VR033");
            var vr034 = validationRuleService.GetValidationRule("OPD-VR034");
            var vr035 = validationRuleService.GetValidationRule("OPD-VR035");
            var vr036 = validationRuleService.GetValidationRule("OPD-VR036");
            var vr037 = validationRuleService.GetValidationRule("OPD-VR037");
            var vr038 = validationRuleService.GetValidationRule("OPD-VR038");
            var vr039 = validationRuleService.GetValidationRule("OPD-VR039");
            var vr135 = validationRuleService.GetValidationRule("OPD-VR135");
            var vr136 = validationRuleService.GetValidationRule("OPD-VR136");
            var vr137 = validationRuleService.GetValidationRule("OPD-VR137");
            var vr138 = validationRuleService.GetValidationRule("OPD-VR138");
            var vr153 = validationRuleService.GetValidationRule("OPD-VR153");
            var vr154 = validationRuleService.GetValidationRule("OPD-VR154");

            var complexes = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes;
           
            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => complexes != null && complexes.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr016.Message, blockId))
                    .WithErrorCode(vr016.Number.ToString())
                    .WithName(vr016.Type.ToString());

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    var facilities = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities;
                    if (facilities == null || !facilities.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr017.Message, c.InstanceToValidate.ComplexId),
                            ErrorCode = vr017.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.ExpectedOilProd_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.ExpectedOilProd_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr026.Message)
                        .WithErrorCode(vr026.Number.ToString())
                        .WithName(vr026.Type.ToString());

            RuleFor(x => x.ExpectedOilProd_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.ExpectedOilProd_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr026.Message)
                        .WithErrorCode(vr026.Number.ToString())
                        .WithName(vr026.Type.ToString());

            RuleFor(x => x.ActualOilProd_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.ActualOilProd_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr027.Message)
                        .WithErrorCode(vr027.Number.ToString())
                        .WithName(vr027.Type.ToString());

            RuleFor(x => x.ActualOilProd_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.ActualOilProd_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr027.Message)
                        .WithErrorCode(vr027.Number.ToString())
                        .WithName(vr027.Type.ToString());

            RuleFor(x => x.GasProd_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasProd_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr028.Message)
                        .WithErrorCode(vr028.Number.ToString())
                        .WithName(vr028.Type.ToString());

            RuleFor(x => x.GasProd_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasProd_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr028.Message)
                        .WithErrorCode(vr028.Number.ToString())
                        .WithName(vr028.Type.ToString());

            RuleFor(x => x.GasInjected_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasInjected_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr029.Message)
                        .WithErrorCode(vr029.Number.ToString())
                        .WithName(vr029.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.GasInjected_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr135.Message),
                            ErrorCode = vr135.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.GasInjected_OFU is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.GasInjected_SI)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasInjected_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr029.Message)
                        .WithErrorCode(vr029.Number.ToString())
                        .WithName(vr029.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.GasInjected_OFU is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr135.Message),
                            ErrorCode = vr135.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.GasInjected_SI is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.GasExported_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasExported_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr030.Message)
                        .WithErrorCode(vr030.Number.ToString())
                        .WithName(vr030.Type.ToString());

            RuleFor(x => x.GasExported_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasExported_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr030.Message)
                        .WithErrorCode(vr030.Number.ToString())
                        .WithName(vr030.Type.ToString());

            RuleFor(x => x.GasFlared_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasFlared_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr031.Message)
                        .WithErrorCode(vr031.Number.ToString())
                        .WithName(vr031.Type.ToString());

            RuleFor(x => x.GasFlared_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasFlared_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr031.Message)
                        .WithErrorCode(vr031.Number.ToString())
                        .WithName(vr031.Type.ToString());

            RuleFor(x => x.GasImport_OFU)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasImport_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr153.Message)
                        .WithErrorCode(vr153.Number.ToString())
                        .WithName(vr153.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.GasImport_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr154.Message),
                            ErrorCode = vr154.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.GasImport_OFU is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.GasImport_SI)
                .Cascade(CascadeMode.Stop)
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasImport_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr153.Message)
                        .WithErrorCode(vr153.Number.ToString())
                        .WithName(vr153.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.GasImport_OFU is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr154.Message),
                            ErrorCode = vr154.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.GasImport_SI is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.GasFuel_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                    .Equal(0)
                    .When(x => x.GasFuel_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr032.Message)
                        .WithErrorCode(vr032.Number.ToString())
                        .WithName(vr032.Type.ToString());

            RuleFor(x => x.GasFuel_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasFuel_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr032.Message)
                        .WithErrorCode(vr032.Number.ToString())
                        .WithName(vr032.Type.ToString());

            RuleFor(x => x.GasLift_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasLift_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr033.Message)
                        .WithErrorCode(vr033.Number.ToString())
                        .WithName(vr033.Type.ToString());

            RuleFor(x => x.GasLift_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasLift_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr033.Message)
                        .WithErrorCode(vr033.Number.ToString())
                        .WithName(vr033.Type.ToString());

            RuleFor(x => x.WaterProd_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterProd_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr034.Message)
                        .WithErrorCode(vr034.Number.ToString())
                        .WithName(vr034.Type.ToString());

            RuleFor(x => x.WaterProd_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterProd_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr034.Message)
                        .WithErrorCode(vr034.Number.ToString())
                        .WithName(vr034.Type.ToString());

            RuleFor(x => x.WaterInjected_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterInjected_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr035.Message)
                        .WithErrorCode(vr035.Number.ToString())
                        .WithName(vr035.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WaterInjected_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr136.Message),
                            ErrorCode = vr136.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WaterInjected_OFU is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.WaterInjected_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterInjected_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr035.Message)
                        .WithErrorCode(vr035.Number.ToString())
                        .WithName(vr035.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WaterInjected_OFU is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr136.Message),
                            ErrorCode = vr136.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WaterInjected_SI is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.WaterDischarge_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterDischarge_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr036.Message)
                        .WithErrorCode(vr036.Number.ToString())
                        .WithName(vr036.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WaterDischarge_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr137.Message),
                            ErrorCode = vr137.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WaterDischarge_OFU is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.WaterDischarge_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterDischarge_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr036.Message)
                        .WithErrorCode(vr036.Number.ToString())
                        .WithName(vr036.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WaterDischarge_OFU is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr137.Message),
                            ErrorCode = vr137.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WaterDischarge_SI is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.WaterSlop_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterSlop_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr037.Message)
                        .WithErrorCode(vr037.Number.ToString())
                        .WithName(vr037.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WaterSlop_SI is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr138.Message),
                            ErrorCode = vr138.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WaterSlop_OFU is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.WaterSlop_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.WaterSlop_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr037.Message)
                        .WithErrorCode(vr037.Number.ToString())
                        .WithName(vr037.Type.ToString())
                .Custom((x, e) =>
                {
                    if (e.InstanceToValidate.WaterSlop_OFU is null)
                    {
                        e.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr138.Message),
                            ErrorCode = vr138.Number.ToString(),
                            PropertyName = e.PropertyPath
                        });
                    }
                })
            .When(x => x.WaterSlop_SI is not null, ApplyConditionTo.AllValidators);

            RuleFor(x => x.WaterCut)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());

            RuleFor(x => x.GasOilRatio_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasOilRatio_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr038.Message)
                        .WithErrorCode(vr038.Number.ToString())
                        .WithName(vr038.Type.ToString());

            RuleFor(x => x.GasOilRatio_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.GasOilRatio_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr038.Message)
                        .WithErrorCode(vr038.Number.ToString())
                        .WithName(vr038.Type.ToString());

            RuleFor(x => x.BasicSedimentWater)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());

            RuleFor(x => x.VoidageReplacementRatio)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());

            RuleFor(x => x.InstalledProdCapacity_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.InstalledProdCapacity_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr039.Message)
                        .WithErrorCode(vr039.Number.ToString())
                        .WithName(vr039.Type.ToString());

            RuleFor(x => x.InstalledProdCapacity_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.InstalledProdCapacity_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr039.Message)
                        .WithErrorCode(vr039.Number.ToString())
                        .WithName(vr039.Type.ToString());

            RuleFor(x => x.PlantEfficiency)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());

            RuleFor(x => x.Comments)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                        .WithMessage(vr023.Message)
                        .WithErrorCode(vr023.Number.ToString())
                        .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.Comments))
                        .WithMessage(vr024.Message)
                        .WithErrorCode(vr024.Number.ToString())
                        .WithName(vr024.Type.ToString());

        }
    }
}
