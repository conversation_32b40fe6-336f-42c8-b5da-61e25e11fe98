﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyStreamFeed
    {
        public string FacilityId { get; set; }
        public decimal StreamFeedHpFuelGasBasisMass { get; set; }
        public decimal StreamFeedHpFuelGasBasisEnergy { get; set; }
        public decimal StreamFeedLpFuelGasBasisMass { get; set; }
        public decimal StreamFeedLpFuelGasBasisEnergy { get; set; }
        public decimal StreamFeedLngBasisMass { get; set; }
        public decimal StreamFeedLngBasisEnergy { get; set; }
        public decimal StreamFeedPropaneBasisMass { get; set; }
        public decimal StreamFeedPropaneBasisEnergy { get; set; }
        public decimal StreamFeedButaneBasisMass { get; set; }
        public decimal StreamFeedButaneBasisEnergy { get; set; }
        public decimal StreamFeedCondensateBasisMass { get; set; }
        public decimal StreamFeedCondensateBasisEnergy { get; set; }
        public decimal StreamFeedIncineratedPentaneBasisMass { get; set; }
        public decimal StreamFeedIncineratedPentaneBasisEnergy { get; set; }
        public decimal StreamFeedPentaneHotOilSystemBasisMass { get; set; }
        public decimal StreamFeedPentaneHotOilSystemBasisEnergy { get; set; }
        public decimal StreamFeedDomesticGasBasisMass { get; set; }
        public decimal StreamFeedDomesticGasBasisEnergy { get; set; }
        public decimal StreamFeedAcidGasBasisMass { get; set; }
        public decimal StreamFeedAcidGasBasisEnergy { get; set; }
    }
}
