﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{
    public class ProdDataDailyValidationReport
    {
        [Key]
        public Guid Id { get; set; }
        public Guid ProdDataDailyId { get; set; }
        public string Block { get; set; }
        public string ProductionDate { get; set; }
        public List<SummaryByFacilityForFacility> SummaryByFacilityForFacility { get; set; }
        public List<SummaryByFieldForFacility> SummaryByFieldForFacility { get; set; }
        public List<DetailedProductionForFacility> ProductionDetailedForFacility { get; set; }
        public List<DetailedGasInjectionForFacility> DetailedGasInjectionForFacility { get; set; }
        public List<SummaryByFieldForFacilityAndField> SummaryByFieldForFacilityAndField { get; set; }
        public List<DetailedProductionForFacilityAndField> ProductionDetailedForFacilityAndField { get; set; }
        public List<DetailedGasInjectionForFacilityAndField> DetailedGasInjectionForFacilityAndField { get; set; }
        public SummaryByFacility SummaryByFacilityTotal { get; set; }
        public SummaryByField SummaryByFieldTotal { get; set; }
        public DetailedProduction DetailedProductionTotal { get; set; }
        public DetailedGasInjection DetailedGasInjectionTotal { get; set; }
        public List<LiftingAndAllocations> LiftingsAndAllocations { get; set; }

        public List<LossesByFacility> LossesByFacilities { get; set; }
        public DateTime LastUpdate { get; set; }
        public int Version { get; set; }

    }
}
