
using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.ManualData;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using OperatorProductionData.Services.Services.DashboardServices.Daily;

namespace OperatorProductionData.Services.Services.DashboardServices.Monthly
{
    //Purpose: 'Orchestrate' the requests to the various services to gather organized production data for the Monthly Dashboards

    public class MonthlyDashboardChartsService
    {
        private const decimal OFU_SI_CONVERSION = 158.987294928M;

        private ProdDataMonthlyVerificationViewService _prodDataMonthlyVerificationViewService { get; set; }
        private DailyDashboardChartsService _dailyDashboardChartsService { get; set; }
        private ProdForecastDataService _prodForecastDataService;
        private ProdAlngForecastDataService _prodAlngForecastDataService;

        public MonthlyDashboardChartsService(
            ProdDataMonthlyVerificationViewService prodDataMonthlyVerificationViewService,
            DailyDashboardChartsService dailyDashboardChartsService,
            IRepositoryNoSql<ProdForecastData> prodForecastDataService,
            IRepositoryNoSql<ProdAlngForecastData> prodAlngForecastDataService
        )
        {
            _prodDataMonthlyVerificationViewService = prodDataMonthlyVerificationViewService;
            _dailyDashboardChartsService = dailyDashboardChartsService;
            _prodForecastDataService = (ProdForecastDataService)prodForecastDataService;
            _prodAlngForecastDataService = (ProdAlngForecastDataService)prodAlngForecastDataService;
        }

        public async Task<IEnumerable<ProductionDashboardDto>> GetMonthlyProductionDashboardCharts(
            int productionYear,
            int productionMonth,
            string[] blockIds
        )
        {
            var prodDataList = await _prodDataMonthlyVerificationViewService.GetAllByProductionDateRange(
                productionMonth, //Start Month and End Month are the same  on Frontend..
                productionMonth,
                productionYear,  //Start Year and End Year are the same on Frontend..
                productionYear,
                blockIds
            );

            var lastValueProductionDate = new DateTime(productionYear, productionMonth, 1).AddMonths(-1);

            var prodLastValueDataList = await _prodDataMonthlyVerificationViewService.GetAllByProductionDateRange(lastValueProductionDate.Month, lastValueProductionDate.Month, lastValueProductionDate.Year, lastValueProductionDate.Year, blockIds);
            var prodHomologousDataList = await _prodDataMonthlyVerificationViewService.GetAllByProductionDateRange(productionMonth, productionMonth, productionYear - 1, productionYear - 1, blockIds);
            var prodAlngForecastDataList = await this.GetAllAlngForecastDataByProductionDateRange(productionMonth, productionYear, blockIds);
            var prodForecastDataList = await this.GetAllForecastDataByProductionDateRange(productionMonth, productionYear, blockIds);


            var result = new List<ProductionDashboardDto>();

            foreach (var blockGroup in prodDataList.GroupBy(x => new { x.BlockId, x.ProductionMonth, x.ProductionYear }))
            {
                var blockData = blockGroup.SelectMany(x => x.Json.OperationSummaryByField).ToList();
                var facilityData = blockGroup.SelectMany(x => x.Json.OperationSummaryByFacility).ToList();
                var lastValueGroupDataList = prodLastValueDataList.Where(x => x.ProductionMonth == blockGroup.Key.ProductionMonth && x.ProductionYear == blockGroup.Key.ProductionYear && x.BlockId == blockGroup.Key.BlockId)?.SelectMany(x => x.Json.OperationSummaryByFacility)?.ToList() ?? new List<MonthlyOperationSummaryByFacility>();
                var homologousGroupDataList = prodHomologousDataList.Where(x => x.ProductionMonth == blockGroup.Key.ProductionMonth && x.ProductionYear == blockGroup.Key.ProductionYear && x.BlockId == blockGroup.Key.BlockId)?.SelectMany(x => x.Json.OperationSummaryByFacility)?.ToList() ?? new List<MonthlyOperationSummaryByFacility>();
                var prodAlngForecastGroupDataList = prodAlngForecastDataList.Where(x => x.Month == blockGroup.Key.ProductionMonth && x.Year == blockGroup.Key.ProductionYear && x.BlockId == blockGroup.Key.BlockId)?.ToList() ?? new List<ProdAlngForecastData>();
                var prodForecastGroupDataList = prodForecastDataList.Where(x => x.Month == blockGroup.Key.ProductionMonth && x.Year == blockGroup.Key.ProductionYear && x.BlockId == blockGroup.Key.BlockId)?.ToList() ?? new List<ProdForecastData>();

                var firstDayDate = new DateTime(blockGroup.Key.ProductionYear, blockGroup.Key.ProductionMonth, 01).ToString("yyyy-MM-dd");
                var lastDayDate = new DateTime(blockGroup.Key.ProductionYear, blockGroup.Key.ProductionMonth, 01).AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd");

                var chartDto = new ProductionDashboardDto
                {
                    BlockId = blockGroup.Key.BlockId,
                    ProductionMonth = blockGroup.Key.ProductionMonth,
                    ProductionYear = blockGroup.Key.ProductionYear,

                    // By Complex aggregations
                    ProductionByComplex_SI = CalculateProductionByComplex(facilityData, "SI"),
                    ProductionByComplex_OFU = CalculateProductionByComplex(facilityData, "OFU"),

                    // By Facility aggregations
                    ProductionByFacility_SI = CalculateProductionByFacility(facilityData, "SI"),
                    ProductionByFacility_OFU = CalculateProductionByFacility(facilityData, "OFU"),

                    // By Field aggregations
                    ProductionByField_SI = CalculateProductionByField(blockData, "SI"),
                    ProductionByField_OFU = CalculateProductionByField(blockData, "OFU"),

                    // Block totals
                    BlockTotals_SI = CalculateBlockTotals(facilityData, blockData, lastValueGroupDataList, homologousGroupDataList, prodAlngForecastGroupDataList, prodForecastGroupDataList, "SI"),
                    BlockTotals_OFU = CalculateBlockTotals(facilityData, blockData, lastValueGroupDataList, homologousGroupDataList, prodAlngForecastGroupDataList, prodForecastGroupDataList, "OFU"),

                    // Additional metrics
                    WaterQualityMetrics = CalculateWaterQualityMetrics(blockGroup.SelectMany(x => x.Json.WaterQuality)),
                    ProductionLossMetrics = CalculateProductionLossMetrics(blockData),

                    ProductionEficiency = GetEficiency(blockGroup.Select(x => x.Json).ToList()),
                    ProductionLossValues = await _dailyDashboardChartsService.GetLossValuesAsync(firstDayDate, lastDayDate, [blockGroup.Key.BlockId]),

                    OilExported_OFU = blockGroup.Sum(b => (b.Json.OilStorageSummary ?? new List<MonthlyOilStorageSummary>()).Sum(f => f.Liftings_OFU)) != default ?
                        blockGroup.Sum(b => (b.Json.OilStorageSummary ?? new List<MonthlyOilStorageSummary>()).Sum(f => f.Liftings_OFU)) :
                        blockGroup.Sum(b => (b.Json.OilStorageByPartner ?? new List<MonthlyOilStorageByPartner>()).Sum(f => f.Liftings_CO_OFU + f.Liftings_PO_OFU)),
                    OilExported_SI = blockGroup.Sum(b => (b.Json.OilStorageSummary ?? new List<MonthlyOilStorageSummary>()).Sum(f => f.Liftings_SI)) != default ?
                        blockGroup.Sum(b => (b.Json.OilStorageSummary ?? new List<MonthlyOilStorageSummary>()).Sum(f => f.Liftings_SI)) :
                        blockGroup.Sum(b => (b.Json.OilStorageByPartner ?? new List<MonthlyOilStorageByPartner>()).Sum(f => f.Liftings_CO_SI + f.Liftings_PO_SI)),

                    CreatedDate = DateTime.UtcNow
                };

                result.Add(chartDto);
            }

            return result;
        }

        private List<ProductionByComplexDto> CalculateProductionByComplex(IEnumerable<MonthlyOperationSummaryByFacility> facilities, string unitMeasure)
        {
            return facilities
                .GroupBy(x => x.ComplexId)
                .Select(g => new ProductionByComplexDto
                {
                    ComplexId = g.Key,
                    OilProduction = unitMeasure == "SI" ? g.Sum(x => x.ActualOilProd_SI) : g.Sum(x => x.ActualOilProd_OFU),
                    ExpectedOilProduction = unitMeasure == "SI" ? g.Sum(x => x.ExpectedOilProd_SI) : g.Sum(x => x.ExpectedOilProd_OFU),
                    GasProduction = unitMeasure == "SI" ? g.Sum(x => x.GasProd_SI) : g.Sum(x => x.GasProd_OFU),
                    WaterProduction = unitMeasure == "SI" ? g.Sum(x => x.WaterProd_SI) : g.Sum(x => x.WaterProd_OFU),
                    GasInjection = unitMeasure == "SI" ? g.Sum(x => x.GasInjected_SI ?? 0) : g.Sum(x => x.GasInjected_OFU ?? 0),
                    WaterInjection = unitMeasure == "SI" ? g.Sum(x => x.WaterInjected_SI ?? 0) : g.Sum(x => x.WaterInjected_OFU ?? 0),
                    GasExported = unitMeasure == "SI" ? g.Sum(x => x.GasExported_SI) : g.Sum(x => x.GasExported_OFU),
                    GasFlared = unitMeasure == "SI" ? g.Sum(x => x.GasFlared_SI) : g.Sum(x => x.GasFlared_OFU),
                    GasFuel = unitMeasure == "SI" ? g.Sum(x => x.GasFuel_SI) : g.Sum(x => x.GasFuel_OFU),
                    GasLift = unitMeasure == "SI" ? g.Sum(x => x.GasLift_SI) : g.Sum(x => x.GasLift_OFU),
                    WaterCut = g.Sum(x => x.WaterCut),
                    BasicSedimentWater = g.Sum(x => x.BasicSedimentWater),
                    VoidageReplacementRatio = g.Sum(x => x.VoidageReplacementRatio),
                    UnitMeasure = unitMeasure
                })
                .ToList();
        }

        private List<ProductionByFacilityDto> CalculateProductionByFacility(IEnumerable<MonthlyOperationSummaryByFacility> facilities, string unitMeasure)
        {
            return facilities
                .Select(x => new ProductionByFacilityDto
                {
                    FacilityId = x.FacilityId,
                    ComplexId = x.ComplexId,
                    OilProduction = unitMeasure == "SI" ? x.ActualOilProd_SI : x.ActualOilProd_OFU,
                    ExpectedOilProduction = unitMeasure == "SI" ? x.ExpectedOilProd_SI : x.ExpectedOilProd_OFU,
                    GasProduction = unitMeasure == "SI" ? x.GasProd_SI : x.GasProd_OFU,
                    WaterProduction = unitMeasure == "SI" ? x.WaterProd_SI : x.WaterProd_OFU,
                    GasInjection = unitMeasure == "SI" ? x.GasInjected_SI ?? 0 : x.GasInjected_OFU ?? 0,
                    WaterInjection = unitMeasure == "SI" ? x.WaterInjected_SI ?? 0 : x.WaterInjected_OFU ?? 0,
                    GasExported = unitMeasure == "SI" ? x.GasExported_SI : x.GasExported_OFU,
                    GasImported = unitMeasure == "SI" ? x.GasImport_SI ?? default : x.GasImport_OFU ?? default,
                    GasFlared = unitMeasure == "SI" ? x.GasFlared_SI : x.GasFlared_OFU,
                    GasFuel = unitMeasure == "SI" ? x.GasFuel_SI : x.GasFuel_OFU,
                    GasLift = unitMeasure == "SI" ? x.GasLift_SI : x.GasLift_OFU,
                    WaterCut = x.WaterCut,
                    BasicSedimentWater = x.BasicSedimentWater,
                    VoidageReplacementRatio = x.VoidageReplacementRatio,
                    UnitMeasure = unitMeasure
                })
                .ToList();
        }

        private List<ProductionByFieldDto> CalculateProductionByField(IEnumerable<MonthlyOperationSummaryByField> fields, string unitMeasure)
        {
            return fields
                .Select(x => new ProductionByFieldDto
                {
                    FieldId = x.FieldId,
                    ComplexId = x.ComplexId,
                    FacilityId = x.FacilityId,
                    OilProduction = unitMeasure == "SI" ? x.ActualOilProd_SI : x.ActualOilProd_OFU,
                    ExpectedOilProduction = unitMeasure == "SI" ? x.ExpectedOilProd_SI : x.ExpectedOilProd_OFU,
                    GasProduction = unitMeasure == "SI" ? x.GasProd_SI : x.GasProd_OFU,
                    WaterProduction = unitMeasure == "SI" ? x.WaterProd_SI : x.WaterProd_OFU,
                    GasInjection = unitMeasure == "SI" ? x.GasInjected_SI ?? 0 : x.GasInjected_OFU ?? 0,
                    WaterInjection = unitMeasure == "SI" ? x.WaterInjected_SI ?? 0 : x.WaterInjected_OFU ?? 0,
                    GasExported = unitMeasure == "SI" ? x.GasExported_SI : x.GasExported_OFU,
                    GasImported = unitMeasure == "SI" ? x.GasImport_SI ?? default : x.GasImport_OFU ?? default,
                    GasFlared = unitMeasure == "SI" ? x.GasFlared_SI : x.GasFlared_OFU,
                    GasFuel = unitMeasure == "SI" ? x.GasFuel_SI : x.GasFuel_OFU,
                    GasLift = unitMeasure == "SI" ? x.GasLift_SI : x.GasLift_OFU,
                    WaterCut = x.WaterCut,
                    BasicSedimentWater = x.BasicSedimentWater,
                    VoidageReplacementRatio = x.VoidageReplacementRatio,
                    UnitMeasure = unitMeasure,
                    WaterDischarge = unitMeasure == "SI" ? x.WaterDischarge_SI ?? 0 : x.WaterDischarge_OFU ?? 0,
                })
                .ToList();
        }

        private TotalsDto CalculateBlockTotals(
            List<MonthlyOperationSummaryByFacility> facilities,
            List<MonthlyOperationSummaryByField> fields,
            List<MonthlyOperationSummaryByFacility> lastValuesFacilities,
            List<MonthlyOperationSummaryByFacility> homologosFacilities,
            List<ProdAlngForecastData> alngForecastData,
            List<ProdForecastData> forecastData,
            string unitMeasure)
        {
            return new TotalsDto
            {
                TotalOilProduction = unitMeasure == "SI" ? facilities.Sum(x => x.ActualOilProd_SI) : facilities.Sum(x => x.ActualOilProd_OFU),
                TotalExpectedOilProduction = unitMeasure == "SI" ? facilities.Sum(x => x.ExpectedOilProd_SI) : facilities.Sum(x => x.ExpectedOilProd_OFU),
                TotalGasProduction = unitMeasure == "SI" ? facilities.Sum(x => x.GasProd_SI) : facilities.Sum(x => x.GasProd_OFU),
                TotalWaterProduction = unitMeasure == "SI" ? facilities.Sum(x => x.WaterProd_SI) : facilities.Sum(x => x.WaterProd_OFU),
                TotalGasInjection = unitMeasure == "SI" ? facilities.Sum(x => x.GasInjected_SI ?? 0) : facilities.Sum(x => x.GasInjected_OFU ?? 0),
                TotalWaterInjection = unitMeasure == "SI" ? facilities.Sum(x => x.WaterInjected_SI ?? 0) : facilities.Sum(x => x.WaterInjected_OFU ?? 0),
                TotalGasExported = unitMeasure == "SI" ? facilities.Sum(x => x.GasExported_SI) : facilities.Sum(x => x.GasExported_OFU),
                TotalGasImported = unitMeasure == "SI" ? facilities.Sum(x => x.GasImport_SI ?? default) : facilities.Sum(x => x.GasImport_OFU ?? default),
                TotalGasFlared = unitMeasure == "SI" ? facilities.Sum(x => x.GasFlared_SI) : facilities.Sum(x => x.GasFlared_OFU),
                TotalGasFuel = unitMeasure == "SI" ? facilities.Sum(x => x.GasFuel_SI) : facilities.Sum(x => x.GasFuel_OFU),
                TotalGasLift = unitMeasure == "SI" ? facilities.Sum(x => x.GasLift_SI) : facilities.Sum(x => x.GasLift_OFU),
                TotalWaterCut = facilities.Sum(x => x.WaterCut),
                TotalWaterDischarge = unitMeasure == "SI" ? facilities.Sum(x => x.WaterDischarge_SI ?? 0) : facilities.Sum(x => x.WaterDischarge_OFU ?? 0),
                UnitMeasure = unitMeasure,
                TotalLastValueOilProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.ActualOilProd_SI) : lastValuesFacilities.Sum(x => x.ActualOilProd_OFU),
                TotalHomologousOilProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.ActualOilProd_SI) : homologosFacilities.Sum(x => x.ActualOilProd_OFU),
                TotalLastValueGasProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.GasProd_SI) : lastValuesFacilities.Sum(x => x.GasProd_OFU),
                TotalHomologousGasProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.GasProd_SI) : homologosFacilities.Sum(x => x.GasProd_OFU),

                TotalLastValueFlaredGasProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.GasFlared_SI) : lastValuesFacilities.Sum(x => x.GasFlared_OFU),
                TotalHomologousFlaredGasProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.GasFlared_SI) : homologosFacilities.Sum(x => x.GasFlared_OFU),

                TotalLastValueWaterProduction = unitMeasure == "SI" ? lastValuesFacilities.Sum(x => x.WaterProd_SI) : lastValuesFacilities.Sum(x => x.WaterProd_OFU),
                TotalHomologousWaterProduction = unitMeasure == "SI" ? homologosFacilities.Sum(x => x.WaterProd_SI) : homologosFacilities.Sum(x => x.WaterProd_OFU),
                TotalGasForecast = forecastData.Sum(x => x.ProducedGasForecast) != 0 ? unitMeasure == "SI" ? forecastData.Sum(x => x.ProducedGasForecast) * OFU_SI_CONVERSION : forecastData.Sum(x => x.ProducedGasForecast) : default,
                TotalWaterForecast = forecastData.Sum(x => x.ProducedWaterForecast) != 0 ? unitMeasure == "SI" ? forecastData.Sum(x => x.ProducedWaterForecast) * OFU_SI_CONVERSION : forecastData.Sum(x => x.ProducedWaterForecast) : default,
                TotalOilForecast = alngForecastData.Sum(x => x.OilProductionForecast) != 0 ? unitMeasure == "SI" ? alngForecastData.Sum(x => x.OilProductionForecast) * OFU_SI_CONVERSION : alngForecastData.Sum(x => x.OilProductionForecast) : default,
            };
        }

        private decimal GetEficiency(ICollection<ProdDataMonthly> prodDataMonthlies)
        {
            decimal totalProduction = prodDataMonthlies.Sum(p => p.OperationSummaryByFacility.Sum(f => f.ActualOilProd_OFU));

            return prodDataMonthlies.SelectMany(x => x.OperationSummaryByFacility.Select(f => new
            {
                FacilityEfficiency = f.PlantEfficiency != 0 && f.ActualOilProd_OFU != 0 && (x.OperationSummaryByFacility.Sum(p => p.ActualOilProd_OFU)) != 0 ? (f.ActualOilProd_OFU / (x.OperationSummaryByFacility.Sum(p => p.ActualOilProd_OFU))) * f.PlantEfficiency : default,
                FacilityTotalProduction = x.OperationSummaryByFacility.Sum(p => p.ActualOilProd_OFU)
            })).ToList().Sum(x => x.FacilityEfficiency != 0 && x.FacilityTotalProduction != 0 && totalProduction != 0 ? (x.FacilityTotalProduction / totalProduction) * x.FacilityEfficiency : default);
        }

        private async Task<ICollection<ProdForecastData>> GetAllForecastDataByProductionDateRange(int month, int year, string[] blockIds)
        {
            var res = await _prodForecastDataService.GetAllByProductionDateRange(month, month, year, year);
            return res.Where(r => blockIds.Contains(r.BlockId))?.ToList() ?? new List<ProdForecastData>();
            //await GetAll(filter);
        }

        private async Task<ICollection<ProdAlngForecastData>> GetAllAlngForecastDataByProductionDateRange(int month, int year, string[] blockIds)
        {
            var res = await _prodAlngForecastDataService.GetAllByProductionDateRange(month, month, year, year);

            return res.Where(r => blockIds.Contains(r.BlockId))?.ToList() ?? new List<ProdAlngForecastData>();
            //await GetAll(filter);
        }

        private WaterQualityMetricsDto CalculateWaterQualityMetrics(IEnumerable<MonthlyWaterQuality> waterQuality)
        {
            var wqList = waterQuality.ToList();
            if (!wqList.Any()) return new WaterQualityMetricsDto();

            return new WaterQualityMetricsDto
            {
                TotalSamples = wqList.Count
                // Note: Monthly water quality might have different properties than daily
                // Add specific calculations based on MonthlyWaterQuality properties
            };
        }

        private ProductionLossMetricsDto CalculateProductionLossMetrics(IEnumerable<MonthlyOperationSummaryByField> fields)
        {
            // For monthly data, production loss might be calculated differently
            // This is a placeholder implementation - adjust based on actual monthly loss data structure
            return new ProductionLossMetricsDto
            {
                TotalOilLoss = 0, // Calculate based on actual monthly loss data if available
                TotalGasLoss = 0,
                TotalWaterLoss = 0
                //...
            };
        }
    }
}