﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using OperatorProductionData.WebApi.Enums;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly
{
    public class ProdDataMonthlyValidator : AbstractValidator<ProdDataMonthly>
    {
        readonly DateTime _inceptionDate = DateTime.Parse("2022-10-01 00:00:00.0000000");

        public ProdDataMonthlyValidator(EnumAction action, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");

            switch (action)
            {
                case EnumAction.Add:

                    ValidateContent(infrastructure, validationRuleService);

                    break;
                case EnumAction.Update:
                    var updateReasons = infrastructure.ReferenceTables.UpdateReasons;

                    RuleFor(x => x.Id)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString());

                    RuleFor(x => x.ReasonId)
                        .NotEmpty()
                            .WithMessage(vr001.Message)
                            .WithErrorCode(vr001.Number.ToString())
                            .WithName(vr001.Type.ToString())
                        .Must(x => updateReasons.Any(y => y.Id == x))
                            .WithMessage(vr010.Message)
                            .WithErrorCode(vr010.Number.ToString())
                            .WithName(vr010.Type.ToString());

                    ValidateContent(infrastructure, validationRuleService);

                    break;
            }
        }

        private void ValidateContent(ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr009 = validationRuleService.GetValidationRule("OPD-VR009");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr013 = validationRuleService.GetValidationRule("OPD-VR013");
            var vr014 = validationRuleService.GetValidationRule("OPD-VR014");
            var vr015 = validationRuleService.GetValidationRule("OPD-VR015");

            RuleFor(x => x.BlockId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => infrastructure.Blocks.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.ProductionMonth)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(1)
                    .WithMessage(vr013.Message)
                    .WithErrorCode(vr013.Number.ToString())
                    .WithName(vr013.Number.ToString())
                .LessThanOrEqualTo(12)
                    .WithMessage(vr013.Message)
                    .WithErrorCode(vr013.Number.ToString())
                    .WithName(vr013.Number.ToString())
                .GreaterThanOrEqualTo(_inceptionDate.Month)
                    .When(x => x.ProductionYear <= _inceptionDate.Year, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr015.Message)
                        .WithErrorCode(vr015.Number.ToString())
                        .WithName(vr015.Type.ToString())
                .LessThanOrEqualTo(DateTime.Now.Month)
                     .When(x => x.ProductionYear >= DateTime.Now.Year, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr014.Message)
                        .WithErrorCode(vr014.Number.ToString())
                        .WithName(vr014.Type.ToString());

            RuleFor(x => x.ProductionYear)
              .Cascade(CascadeMode.Stop)
              .NotEmpty()
                  .WithMessage(vr001.Message)
                  .WithErrorCode(vr001.Number.ToString())
                  .WithName(vr001.Type.ToString())
              .LessThanOrEqualTo(DateTime.Now.Year)
                  .WithMessage(vr014.Message)
                  .WithErrorCode(vr014.Number.ToString())
                  .WithName(vr014.Type.ToString())
               .GreaterThanOrEqualTo(_inceptionDate.Year)
                  .WithMessage(vr015.Message)
                  .WithErrorCode(vr015.Number.ToString())
                  .WithName(vr015.Type.ToString());

            RuleFor(x => x.OperationSummaryByFacility)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.OperationSummaryByFacility)
                .SetValidator(x => new MonthlyOperationSummaryByFacilityValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.OperationSummaryByField)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.OperationSummaryByField)
                .SetValidator(x => new MonthlyOperationSummaryByFieldValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.WaterQuality)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.WaterQuality)
                .SetValidator(x => new MonthlyWaterQualityValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.CommodityConsumptionSummary)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.CommodityConsumptionSummary)
                .SetValidator(x => new MonthlyCommodityConsumptionSummaryValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.OperationComments)
                .SetValidator(x => new MonthlyOperationCommentsValidator(x.ProductionMonth, x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.OperationComments is not null &&
                    x.OperationComments.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.DetailedProduction)
                .Cascade(CascadeMode.Stop)
                .Must(x => x != null)
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => x != null && x.Count > 0)
                    .WithMessage(vr009.Message)
                    .WithErrorCode(vr009.Number.ToString())
                    .WithName(vr009.Type.ToString());
            RuleForEach(x => x.DetailedProduction)
                .SetValidator(x => new MonthlyDetailedProductionValidator(x.BlockId, x.ProductionMonth, x.ProductionYear, infrastructure, validationRuleService))
                .When(x => infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.DetailedGasInjection)
                .SetValidator(x => new MonthlyDetailedGasInjectionValidator(x.BlockId, x.ProductionMonth, x.ProductionYear, infrastructure, validationRuleService))
                .When(x =>
                    x.DetailedGasInjection is not null &&
                    x.DetailedGasInjection.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.DetailedWaterInjection)
                .SetValidator(x => new MonthlyDetailedWaterInjectionValidator(x.BlockId, x.ProductionMonth, x.ProductionYear, infrastructure, validationRuleService))
                .When(x =>
                    x.DetailedWaterInjection is not null &&
                    x.DetailedWaterInjection.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.OilStorageSummary)
                .SetValidator(x => new MonthlyOilStorageSummaryValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.OilStorageSummary is not null &&
                    x.OilStorageSummary.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.OilStorageByPartner)
                .SetValidator(x => new MonthlyOilStorageByPartnerValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.OilStorageByPartner is not null &&
                    x.OilStorageByPartner.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.LiftingDetail)
                .SetValidator(x => new MonthlyLiftingDetailValidator(x.BlockId, x.ProductionMonth, x.ProductionYear, infrastructure, validationRuleService))
                .When(x =>
                    x.LiftingDetail is not null &&
                    x.LiftingDetail.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.SubmittedDocuments)
                .SetValidator(new MonthlySubmittedDocumentsValidator(validationRuleService, infrastructure))
                .When(x =>
                    x.SubmittedDocuments is not null &&
                    x.SubmittedDocuments.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.YtdLPGProduction)
                .SetValidator(x => new GasProductionYTDValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.YtdLPGProduction is not null &&
                    x.YtdLPGProduction.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.MonthlyLPGProduction)
                .SetValidator(x => new MonthlyGasProductionValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.MonthlyLPGProduction is not null &&
                    x.MonthlyLPGProduction.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);

            RuleForEach(x => x.MonthlyLPGShippingTotalByMonth)
                .SetValidator(x => new MonthlyGasShippingValidator(x.BlockId, infrastructure, validationRuleService))
                .When(x =>
                    x.MonthlyLPGShippingTotalByMonth is not null &&
                    x.MonthlyLPGShippingTotalByMonth.Count > 0 &&
                    infrastructure.Blocks.Any(y => y.Id == x.BlockId), ApplyConditionTo.CurrentValidator);
        }
    }
}



