name: $(Build.DefinitionName)_$(Build.SourceBranchName)_$(date:yyyy).$(date:MM).$(date:dd).$(Rev:r)

trigger:
    branches:
        include:
            - main
            - staging
            - development
            - development-predev
    paths:
        include:
            - 'OperatorProductionData.WebApi/*'

pool:
  vmImage: ubuntu-latest

variables:
  tag: '$(Build.BuildNumber)'
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}: 
    PublishProfile: Production
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/staging') }}: 
    PublishProfile: Staging
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/development') }}: 
    PublishNugetPackage: true
    PublishProfile: Test
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/development-predev') }}: 
    PublishNugetPackage: true
    PublishProfile: Test

stages:
- stage: Build
  jobs:
  - job: BuildAndPublish
    displayName: 'Build and publish artifacts'
    steps:
    - task: VersionDotNetCoreAssemblies@2
      inputs:
        Path: '$(Build.SourcesDirectory)'
        VersionNumber: '$(Build.BuildNumber)'
        Injectversion: False
        VersionRegex: '\d+\.\d+\.\d+\.\d+'
        FilenamePattern: '.csproj'
        AddDefault: true
        OutputVersion: 'OutputedVersion'

    - task: UseDotNet@2
      inputs:
        packageType: 'sdk'
        version: '8.x'

    - task: DotNetCoreCLI@2
      displayName: 'DotNetCoreCLI build'
      inputs:
        command: 'build'
        projects: |
          **/*.csproj
          !**/node_modules/**/*.sln
        arguments: '-c Debug /p:SkipInvalidConfigurations=true /p:PublishProfile=$(PublishProfile) /p:WebPublishMethod=Package /p:EnvironmentName=$(PublishProfile)'

    - task: DotNetCoreCLI@2
      displayName: 'DotNetCoreCLI publish'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: |
          **/*.csproj
          !**/node_modules/**/*.sln
        arguments: '-c Debug --output $(Build.ArtifactStagingDirectory) -p:SkipInvalidConfigurations=true --no-restore --no-build'
        zipAfterPublish: false

    - task: PublishPipelineArtifact@1
      inputs:
        targetPath: '$(Build.ArtifactStagingDirectory)' 
        artifactName: 'smp-opd-api'
        
    - task: DotNetCoreCLI@2
      displayName: 'DotNetCoreCLI nuget pack'
      condition: and(succeeded(), eq(variables.PublishNugetPackage, 'true'))
      inputs:
        command: 'pack'
        packagesToPack: '**/OperatorProductionData.Models/*.csproj;**/OperatorProductionData.WebApiServices/*.csproj'
        packDirectory: '$(Build.ArtifactStagingDirectory)/packages'
    
    - task: DotNetCoreCLI@2
      displayName: 'DotNetCoreCLI nuget push'
      condition: and(succeeded(), eq(variables.PublishNugetPackage, 'true'))
      inputs:
        command: 'push'
        packagesToPush: '$(Build.ArtifactStagingDirectory)/packages/*.nupkg'
        nuGetFeedType: 'internal'
        publishVstsFeed: 'SMP/SMP'