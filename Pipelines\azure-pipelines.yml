trigger:
  branches:
    include:
      - development

variables:
- template: vars/global.yml

stages:

- stage: BuildDevelopment
  displayName: 'Build Development'
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/development')
  variables:
  - template: vars/development.yml
  - group: 'Cross-Sectional.Development'
  jobs:
  - job: OperatorProductionData
    displayName: 'Build image for OperatorProductionData'
    pool:
      name: ${{variables.poolName}}
    steps:
    - template: steps/create-docker-image.yml
      parameters:
        dockerfilePath: 'Dockerfile'
        repositoryName: 'library/opd'

- stage: ReleaseDevelopment
  displayName: 'Release Development'
  dependsOn: BuildDevelopment
  condition: succeeded()
  variables:
  - template: vars/development.yml
  - group: 'Cross-Sectional.Development'
  jobs:
  - job: OperatorProductionData
    displayName: 'Deploy OperatorProductionData to ACI'
    steps:
    - template: steps/deploy-aci.yml
      parameters:
        aciManifestLocation: '$(Build.SourcesDirectory)/Pipelines/deploy/opd.yaml'
        resourceGroupName: '$(AEEconomics.Environment.ResourceGroup)'
        recordSetName: 'opd'
        containerGroup: 'opd-aci'
        dnsZone: '$(AEEconomics.DNS.Zone)'

- stage: Approval
  displayName: 'Approval'
  dependsOn: ReleaseDevelopment
  pool: server
  jobs:
  - job: ManualApproval
    displayName: 'Approval'
    steps:
    - task: ManualValidation@0
      timeoutInMinutes: 10080 # task times out in 7 days
      inputs:
        instructions: 'Manual approval is required. Please review and approve the deployment.'
        failOnStandardError: true
        onTimeout: 'reject'

- stage: Staging
  displayName: 'Build Staging'
  dependsOn: Approval
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/staging')
  variables:
  - template: vars/staging.yml
  - group: 'Cross-Sectional.Staging'
  jobs:
  - job: Docker
    displayName: 'Build and publish Docker image'
    pool:
      name: DockerOnAzure
    steps:
    - template: steps/create-docker-image.yml