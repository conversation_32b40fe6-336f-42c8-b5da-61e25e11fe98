﻿using Ae.Stratus.Core.Backend.Helpers;
using Ae.Stratus.Core.Backend.Interfaces.Interfaces;
using Ae.Stratus.Core.Backend.Interfaces.NoSQL.Attributes;
using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper.Internal;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace OperatorProductionData.Services.Services
{
    public class DebugRepositoryBaseMongoDB<TModel> : IRepositoryNoSql<TModel>
    {
        public readonly IMongoCollection<TModel> _container;

        public DebugRepositoryBaseMongoDB(string connectionString, string databaseName, string collectionName)
        {
            var client = new MongoClient(connectionString);
            _container = client.GetDatabase(databaseName).GetCollection<TModel>(collectionName);
        }

        public DebugRepositoryBaseMongoDB(IMongoCollection<TModel> container)
        {
            _container = container;
        }

        public virtual async Task<TModel> Add(TModel model)
        {
            Type[] types = typeof(TModel).GetInterfaces();

            // Check if [Key] types are valid
            var propertyInfo = typeof(TModel).GetProperties().SingleOrDefault(x => x.Has<KeyAttribute>()) ?? throw new Exception($"Model without [Key] attribute defined.");
            var vciPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionsCollectionIdAttribute>());
            var vpPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionPropertyAttribute>());

            // Versionable
            if (types.Any(x => x == typeof(IVersionable)))
            {
                if (propertyInfo.PropertyType != typeof(Guid))
                {
                    throw new Exception($"Versioning is only valid for [Key] type as Guid.");
                }

                vciPropertyInfo.SetValue(model, Guid.NewGuid());
                vpPropertyInfo.SetValue(model, 1);
            }

            //SoftDeletable
            if (types.Any(x => x == typeof(ISoftDeletable)))
            {
                var deletedPropertyInfo = typeof(ISoftDeletable).GetProperties().SingleOrDefault(x => x.Has<SoftDeleteAttribute>());
                deletedPropertyInfo.SetValue(model, false);
            }


            // [Key] type check for Id definition
            if (propertyInfo.PropertyType == typeof(Guid))
            {
                var newId = Guid.NewGuid();
                propertyInfo.SetValue(model, newId);

                await _container.InsertOneAsync(model);

                if (types.Any(x => x == typeof(IVersionable)))
                {
                    return await GetById(vciPropertyInfo.GetValue(model));
                }

                return await GetById(propertyInfo.GetValue(model));
            }
            else if (propertyInfo.PropertyType == typeof(string))
            {
                var id = (string)propertyInfo.GetValue(model);

                if (string.IsNullOrEmpty(id))
                {
                    throw new Exception("[Key] property must not be null or empty.");
                }

                await _container.InsertOneAsync(model);

                return await GetById(id);
            }

            throw new Exception("[Key] property type not valid or implemented.");
        }

        public virtual async Task<bool> Delete(object id)
        {
            Type[] types = typeof(TModel).GetInterfaces();

            FilterDefinition<TModel> filter;

            //SoftDeletable
            if (types.Any(x => x == typeof(ISoftDeletable)))
            {
                if (types.Any(x => x == typeof(IVersionable)))
                {
                    filter = GetFilterForVersionsCollectionId((Guid)id, true);
                }
                else
                {
                    filter = GetFilterForId(id);
                }

                var modelList = await _container.FindAsync<TModel>(filter);

                var vpPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionPropertyAttribute>());
                ParameterExpression parameter = Expression.Parameter(typeof(TModel));

                var model = modelList.ToList().AsQueryable().MaxBy(Expression.Lambda<Func<TModel, int>>(
                                Expression.Property(parameter, vpPropertyInfo.Name),
                                parameter
                            ));

                var deletedPropertyInfo = typeof(ISoftDeletable).GetProperties().SingleOrDefault(x => x.Has<SoftDeleteAttribute>());

                UpdateDefinition<TModel> update = Builders<TModel>.Update.Set(Expression.Lambda<Func<TModel, bool>>(
                                                            Expression.Property(parameter, deletedPropertyInfo.Name),
                                                                            parameter
                                                                        ), true);

                var propertyInfo = typeof(TModel).GetProperties().SingleOrDefault(x => x.Has<KeyAttribute>());

                await _container.FindOneAndUpdateAsync(GetFilterForId(propertyInfo.GetValue(model)), update);
            }
            else
            {
                if (types.Any(x => x == typeof(IVersionable)))
                {
                    filter = GetFilterForVersionsCollectionId((Guid)id, false);
                }
                else
                {
                    filter = GetFilterForId(id);
                }

                await _container.FindOneAndDeleteAsync<TModel>(filter);
            }

            return true;
        }

        public virtual async Task<TModel>? GetById(object id)
        {
            Type[] types = typeof(TModel).GetInterfaces();

            if (types.Any(x => x == typeof(IVersionable)))
            {
                var filter = GetFilterForVersionsCollectionId((Guid)id, types.Any(x => x == typeof(ISoftDeletable)));

                var model = await _container.FindAsync<TModel>(filter);

                var vpPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionPropertyAttribute>());
                ParameterExpression parameter = Expression.Parameter(typeof(TModel));

                return model.ToList().AsQueryable().MaxBy(Expression.Lambda<Func<TModel, int>>(
                                Expression.Property(parameter, vpPropertyInfo.Name),
                                parameter
                            ));
            }
            else
            {
                var filter = GetFilterForId(id);

                var model = await _container.FindAsync<TModel>(filter);

                return model.SingleOrDefault();
            }
        }

        public virtual async Task<GridDataLoadResponse<TModel>> GetSortedFilteredPagedList(GridDataLoadOptions options)
        {
            var response = new GridDataLoadResponse<TModel>();

            var sortDef = Builders<TModel>.Sort;
            List<SortDefinition<TModel>> sortDefArray = new();

            var filterDef = Builders<TModel>.Filter;
            List<FilterDefinition<TModel>> filterDefArray = new();

            ParameterExpression parameter = Expression.Parameter(typeof(TModel));

            // Sort by specified properties
            if (options.SortingOptions != null && options.SortingOptions.Count > 0)
            {
                foreach (var sort in options.SortingOptions)
                {
                    PropertyInfo propertyInfo = typeof(TModel).GetProperty(sort.PropertyName) ?? throw new Exception($"Property {sort.PropertyName} does not exist in this model!");

                    sortDefArray.Add(sort.IsAscending
                    ? sortDef.Ascending(Expression.Lambda<Func<TModel, object>>(
                        Expression.Property(parameter, propertyInfo.Name),
                        parameter
                    ))
                    : sortDef.Descending(Expression.Lambda<Func<TModel, object>>(
                        Expression.Property(parameter, propertyInfo.Name),
                        parameter
                    )));
                }
            }

            // Filter by specified conditions
            if (options.FilteringOptions != null && options.FilteringOptions.Count > 0)
            {
                foreach (var filter in options.FilteringOptions)
                {
                    PropertyInfo propertyInfo = typeof(TModel).GetProperty(filter.PropertyName) ?? throw new Exception($"Property {filter.PropertyName} does not exist in this entity!");

                    object val = filter.Value;

                    // because property Value is an object, can be a JsonElement when arrives from a Web API
                    if (filter.Value.GetType() == typeof(JsonElement))
                    {
                        JsonElement jsonElement = (JsonElement)filter.Value;
                        val = Helpers.GetValueFromJsonElement(propertyInfo.PropertyType, jsonElement);
                    }

                    switch (filter.Condition)
                    {
                        case FilteringOptions.FilteringCondition.Contains:
                            if (propertyInfo.PropertyType != typeof(string)) throw new Exception("Contains condition must be used on string type properties.");
                            else
                            {
                                if (val.GetType() != typeof(string)) throw new Exception("Property must be of type string.");

                                filterDefArray.Add(filterDef.Regex(propertyInfo.Name, new BsonRegularExpression($".*{val}.*")));
                            }
                            break;
                        case FilteringOptions.FilteringCondition.Equals:

                            filterDefArray.Add(filterDef.Eq(propertyInfo.Name, val));

                            break;
                        case FilteringOptions.FilteringCondition.Greater:

                            filterDefArray.Add(filterDef.Gt(propertyInfo.Name, val));

                            break;
                        case FilteringOptions.FilteringCondition.Lesser:

                            filterDefArray.Add(filterDef.Lt(propertyInfo.Name, val));

                            break;
                        case FilteringOptions.FilteringCondition.LesserOrEquals:

                            filterDefArray.Add(filterDef.Lte(propertyInfo.Name, val));

                            break;
                        case FilteringOptions.FilteringCondition.GreaterOrEquals:

                            filterDefArray.Add(filterDef.Gte(propertyInfo.Name, val));

                            break;
                        case FilteringOptions.FilteringCondition.None:
                        default:
                            break;
                    }
                }
            }

            PipelineDefinition<TModel, TModel> pipeline = new EmptyPipelineDefinition<TModel>();

            //Initialize Pipeline to match everything
            //pipeline.Match(filterDef.Empty);

            if (filterDefArray.Count > 0)
            {
                pipeline = pipeline.Match(filterDef.And(filterDefArray));
            }
            if (sortDefArray.Count > 0)
            {
                pipeline = pipeline.Sort(sortDef.Combine(sortDefArray));
            }

            if (options.PageSize > 0)
            {
                var totalRecords = filterDefArray.Count > 0 ?
                        await _container.CountDocumentsAsync(filterDef.And(filterDefArray))
                        : await _container.CountDocumentsAsync(filterDef.Empty);

                if (totalRecords == response.PageCount)
                {
                    response.PageCount = 1;
                }
                else
                {
                    var count = (int)Math.Ceiling((decimal)totalRecords / (decimal)options.PageSize.Value); // decimal cast is necessary to avoid fraction discard
                    response.PageCount = count != 0 ? count : 1;
                }

                // Paginate the results
                if (options.PageSize.HasValue && options.PageIndex.HasValue)
                {
                    int skip = options.PageIndex.Value * options.PageSize.Value;
                    pipeline = pipeline.Skip(skip).Limit(options.PageSize.Value);
                }

                response.PageIndex = options.PageIndex ?? 0;
            }

            response.Models = (await _container.AggregateAsync(pipeline)).ToList();

            return response;
        }

        public virtual async Task<TModel> Update(TModel model)
        {
            var propertyInfo = typeof(TModel).GetProperties().SingleOrDefault(x => x.Has<KeyAttribute>()) ?? throw new Exception($"Model without [Key] attribute defined.");

            Type[] types = typeof(TModel).GetInterfaces();

            // Versionable
            if (types.Any(x => x == typeof(IVersionable)))
            {
                var lastVersion = await GetById(propertyInfo.GetValue(model));

                var vpPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionPropertyAttribute>());
                var version = (int)vpPropertyInfo.GetValue(lastVersion);

                version++;
                vpPropertyInfo.SetValue(model, version);

                var vciPropertyInfo = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionsCollectionIdAttribute>());
                vciPropertyInfo.SetValue(model, (Guid)vciPropertyInfo.GetValue(lastVersion));

                propertyInfo.SetValue(model, Guid.NewGuid());
                await _container.InsertOneAsync(model);
                return await GetById(vciPropertyInfo.GetValue(model));
            }
            else
            {
                var filter = GetFilterForId(propertyInfo.GetValue(model));

                UpdateDefinition<TModel> updateDefinition = Builders<TModel>.Update.Set(propertyInfo.Name, propertyInfo.GetValue(model)); // trick to get UpdateDefinition instance

                foreach (var pi in typeof(TModel).GetProperties().Where(x => !x.Has<KeyAttribute>()))
                {
                    updateDefinition = Builders<TModel>.Update.Combine(updateDefinition.Set(pi.Name, pi.GetValue(model)));
                }

                await _container.FindOneAndUpdateAsync(filter, updateDefinition);

                return await GetById(propertyInfo.GetValue(model));
            }
        }

        public async Task<ICollection<TModel>> GetAll(FilterDefinition<TModel> filter)
        {
            var cursor = await _container.FindAsync<TModel>(filter);

            return cursor.ToList();
        }

        private static FilterDefinition<TModel> GetFilterForId(object id)
        {
            var propertyInfo = typeof(TModel).GetProperties().SingleOrDefault(x => x.Has<KeyAttribute>());
            ParameterExpression parameter = Expression.Parameter(typeof(TModel));

            if (id.GetType() == typeof(Guid))
            {
                return Builders<TModel>.Filter.Eq(
                            Expression.Lambda<Func<TModel, Guid>>(
                                Expression.Property(parameter, propertyInfo.Name),
                                parameter
                            ), (Guid)id);
            }
            else if (id.GetType() == typeof(string))
            {
                return Builders<TModel>.Filter.Eq(
                            Expression.Lambda<Func<TModel, string>>(
                                Expression.Property(parameter, propertyInfo.Name),
                                parameter
                            ), (string)id);
            }

            throw new Exception("[Key] property type not valid or implemented.");
        }

        private static FilterDefinition<TModel> GetFilterForVersionsCollectionId(Guid id, bool softDeletable)
        {
            var propertyInfoVersionsCollectionId = typeof(IVersionable).GetProperties().SingleOrDefault(x => x.Has<VersionsCollectionIdAttribute>());
            var propertyInfoDelete = typeof(ISoftDeletable).GetProperties().SingleOrDefault(x => x.Has<SoftDeleteAttribute>());
            ParameterExpression parameter = Expression.Parameter(typeof(TModel));

            var filterBuilder = Builders<TModel>.Filter;

            var filterVersionsCollectionId = filterBuilder.Eq(
                            Expression.Lambda<Func<TModel, Guid>>(
                                Expression.Property(parameter, propertyInfoVersionsCollectionId.Name),
                                parameter
                            ), id);

            if (softDeletable)
            {
                var filterDelete = filterBuilder.Eq(
                            Expression.Lambda<Func<TModel, bool>>(
                                Expression.Property(parameter, propertyInfoDelete.Name),
                                parameter
                            ), false);

                return filterBuilder.And(filterVersionsCollectionId, filterDelete);
            }

            return filterVersionsCollectionId;
        }
    }
}
