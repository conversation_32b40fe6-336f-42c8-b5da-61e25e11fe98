﻿using OperatorProductionData.Models.Utilities;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{
    public class ProductionLossSummary
    {
        public ValidationResult<decimal> PlannedOilLoss_OFU { get; set; }

        public ValidationResult<decimal> UnplannedOilLoss_OFU { get; set; }

        public ValidationResult<decimal> PlannedOilLoss_SI { get; set; }

        public ValidationResult<decimal> UnplannedOilLoss_SI { get; set; }

        public string LossMotive { get; set; }
    }
}
