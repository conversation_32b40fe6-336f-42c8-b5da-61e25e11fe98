﻿using OperatorProductionData.Models.Utilities;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{
    public class DetailedGasInjectionForFacility : DetailedGasInjection
    {
        public string FacilityId { get; set; }
    }

    public class DetailedGasInjectionForFacilityAndField : DetailedGasInjection
    {
        public string FacilityId { get; set; }
        public string FieldId { get; set; }
    }

    public class DetailedGasInjection
    {
        public ValidationResult<decimal> GasInjected_OFU { get; set; }
        public ValidationResult<decimal> GasInjected_SI { get; set; }
    }
}


