[{"id": "OPD-VR001", "Number": 1, "Message": "The field is required", "Type": "E"}, {"id": "OPD-VR002", "Number": 2, "Message": "The field value is of the wrong type or exceeds the type capacity", "Type": "E"}, {"id": "OPD-VR003", "Number": 3, "Message": "The field value cannot be negative", "Type": "E"}, {"id": "OPD-VR004", "Number": 4, "Message": "The field value cannot be > 100%", "Type": "E"}, {"id": "OPD-VR005", "Number": 5, "Message": "The field value cannot be >= 100%", "Type": "E"}, {"id": "OPD-VR006", "Number": 6, "Message": "The field value must be between 0h and 24h", "Type": "E"}, {"id": "OPD-VR007", "Number": 7, "Message": "The field value must be between 0 days and 28/29/30/31 days", "Type": "E"}, {"id": "OPD-VR008", "Number": 8, "Message": "The filed value must be between 0 days and 365/366 days", "Type": "E"}, {"id": "OPD-VR009", "Number": 9, "Message": "The array cannot be empty", "Type": "E"}, {"id": "OPD-VR010", "Number": 10, "Message": "The Id does not exist in SMP", "Type": "E"}, {"id": "OPD-VR011", "Number": 1001, "Message": "The productionDate cannot be earlier than SMP inception date", "Type": "E"}, {"id": "OPD-VR012", "Number": 1002, "Message": "The productionDate cannot be in the future", "Type": "E"}, {"id": "OPD-VR013", "Number": 1003, "Message": "The productionMonth must be between 1 and 12", "Type": "E"}, {"id": "OPD-VR014", "Number": 1004, "Message": "The productionMonth + productionYear cannot be in the future", "Type": "E"}, {"id": "OPD-VR015", "Number": 1005, "Message": "The productionMonth + productionYear cannot be earlier than SMP inception month", "Type": "E"}, {"id": "OPD-VR016", "Number": 1006, "Message": "The complexId is not valid for block {0}", "Type": "E"}, {"id": "OPD-VR017", "Number": 1007, "Message": "The facilityId is not valid for complex {0}", "Type": "E"}, {"id": "OPD-VR018", "Number": 1008, "Message": "The fieldId is not valid for block {0}", "Type": "E"}, {"id": "OPD-VR019", "Number": 1009, "Message": "The systemId is not valid for facility {0}", "Type": "E"}, {"id": "OPD-VR020", "Number": 1010, "Message": "The systemId is null, so equipmentId must be null too", "Type": "E"}, {"id": "OPD-VR021", "Number": 1011, "Message": "The equipmentId is not valid for system {0}", "Type": "E"}, {"id": "OPD-VR022", "Number": 1012, "Message": "The commentDateTime must be within the reported productionDate", "Type": "E"}, {"id": "OPD-VR023", "Number": 1013, "Message": "The comments is null or empty and commentedBy has value", "Type": "E"}, {"id": "OPD-VR024", "Number": 1014, "Message": "The comments has value and commentedBy is null or empty", "Type": "E"}, {"id": "OPD-VR025", "Number": 1015, "Message": "The additional description is mandatory for the selected reason and reasonDescription is null or empty", "Type": "E"}, {"id": "OPD-VR026", "Number": 2001, "Message": "If expectedOilProd_OFU==0, then expectedOilProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR027", "Number": 2002, "Message": "If actualOilProd_OFU==0, then actualOilProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR028", "Number": 2003, "Message": "If gasProd_OFU==0, then gasProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR029", "Number": 2004, "Message": "If gasInjected_OFU==0, then gasInjected_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR030", "Number": 2005, "Message": "If gasExported_OFU==0, then gasExported_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR031", "Number": 2006, "Message": "If gasFlared_OFU==0, then gasFlared_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR032", "Number": 2007, "Message": "If gasFuel_OFU==0, then gasFuel_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR033", "Number": 2008, "Message": "If gasLift_OFU==0, then gasLift_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR034", "Number": 2009, "Message": "If waterProd_OFU==0, then waterProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR035", "Number": 2010, "Message": "If waterInjected_OFU==0, then waterInjected_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR036", "Number": 2011, "Message": "If waterDischarged_OFU==0, then waterDischarged_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR037", "Number": 2012, "Message": "If waterSlop_OFU==0, then waterSlop_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR038", "Number": 2013, "Message": "If gasOilRatio_OFU==0, then gasOilRatio_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR039", "Number": 2014, "Message": "If installedProdCapacity_OFU==0, then installedProdCapacity_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR040", "Number": 2015, "Message": "If plannedOilLoss_OFU==0, then plannedOilLoss_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR041", "Number": 2016, "Message": "If unplannedOilLoss_OFU==0, then unplannedOilLoss_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR042", "Number": 2017, "Message": "If oilInWater==0, then oilInWater_PPM must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR043", "Number": 2018, "Message": "If monthlyAvgOilInWater==0, then monthlyAvgOilInWater_PPM must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR044", "Number": 2019, "Message": "If wellHeadPressure_OFU==0, then wellHeadPressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR045", "Number": 2020, "Message": "If wellHeadTemperature_OFU==0, then wellHeadTemperature_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR046", "Number": 2021, "Message": "If bottomHolePressure_OFU==0, then bottomHolePressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR047", "Number": 2022, "Message": "If bottomHoleTemperature_OFU==0, then bottomHoleTemperature_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR048", "Number": 2023, "Message": "If downholeGasificationPressure_OFU==0, then downholeGasificationPressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR049", "Number": 2024, "Message": "If downholeGasificationTemperature_OFU==0, then downholeGasificationTemperature_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR050", "Number": 2025, "Message": "If casingPressure_OFU==0, then casingPressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR051", "Number": 2026, "Message": "If casingTemperature_OFU==0, then casingTemperature_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR052", "Number": 2027, "Message": "If flowingTubingPressure_OFU==0, then flowingTubingPressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR053", "Number": 2028, "Message": "If flowingTubingTemperature_OFU==0, then flowingTubingTemperature_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR054", "Number": 2029, "Message": "If expectedGasInjection_OFU==0, then expectedGasInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR055", "Number": 2030, "Message": "If actualGasInjection_OFU==0, then actualGasInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR056", "Number": 2031, "Message": "If injectionPressure_OFU==0, then injectionPressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR057", "Number": 2032, "Message": "If upstreamChokePressure_OFU==0, then upstreamChokePressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR058", "Number": 2033, "Message": "If upstreamChokeTemperature_OFU==0, then upstreamChokeTemperature_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR059", "Number": 2034, "Message": "If downstreamChokePressure_OFU==0, then upstreamChokePressure_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR060", "Number": 2035, "Message": "If expectedWaterInjection_OFU==0, then expectedWaterInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR061", "Number": 2036, "Message": "If actualWaterInjection_OFU==0, then actualWaterInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR062", "Number": 2037, "Message": "If initialStock_OFU==0, then initialStock_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR063", "Number": 2038, "Message": "If production_OFU==0, then production_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR064", "Number": 2039, "Message": "If liftings_OFU==0, then liftings_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR065", "Number": 2040, "Message": "If finalStock_OFU==0, then finalStock_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR066", "Number": 2041, "Message": "If quantityLoaded_OFU==0, then quantityLoaded_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR067", "Number": 2042, "Message": "If quantity_OFU==0, then quantity_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR068", "Number": 2043, "Message": "If monthOilProd_OFU==0, then monthOilProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR069", "Number": 2044, "Message": "If yearOilProd_OFU==0, then yearOilProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR070", "Number": 2045, "Message": "If monthWaterProd_OFU==0, then monthWaterProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR071", "Number": 2046, "Message": "If yearWaterProd_OFU==0, then yearWaterProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR072", "Number": 2047, "Message": "If monthGasProd_OFU==0, then monthGasProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR073", "Number": 2048, "Message": "If yearGasProd_OFU==0, then yearGasProd_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR074", "Number": 2049, "Message": "If monthGasLift_OFU==0, then monthGasLift_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR075", "Number": 2050, "Message": "If yearGasLift_OFU==0, then yearGasLift_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR076", "Number": 2051, "Message": "If dailyOilRate_OFU==0, then dailyOilRate_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR077", "Number": 2052, "Message": "If dailyGasRate_OFU==0, then dailyGasRate_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR078", "Number": 2053, "Message": "If dailyWaterRate_OFU==0, then dailyWaterRate_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR079", "Number": 2054, "Message": "If monthGasInjection_OFU==0, then monthGasInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR080", "Number": 2055, "Message": "If yearGasInjection_OFU==0, then yearGasInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR081", "Number": 2056, "Message": "If monthWaterInjection_OFU==0, then monthWaterInjection_SI must be 0 too, and vice-versaa", "Type": "E"}, {"id": "OPD-VR082", "Number": 2057, "Message": "If yearWaterInjection_OFU==0, then yearWaterInjection_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR083", "Number": 3001, "Message": "The startDate cannot be in the future", "Type": "E"}, {"id": "OPD-VR084", "Number": 3002, "Message": "The endDate cannot be in the future", "Type": "E"}, {"id": "OPD-VR085", "Number": 3003, "Message": "The startDate > endDate", "Type": "E"}, {"id": "OPD-VR086", "Number": 3004, "Message": "The startDate or endDate must be within productionDate", "Type": "E"}, {"id": "OPD-VR087", "Number": 3005, "Message": "The startDate or endDate must be within the reported production month", "Type": "E"}, {"id": "OPD-VR088", "Number": 3006, "Message": "The gravity value must be between 0 and 999.99999", "Type": "E"}, {"id": "OPD-VR089", "Number": 4001, "Message": "If initialStock_OFU==0, then initialStock_SI==0, initialStock_MT==0, and vice-versa", "Type": "E"}, {"id": "OPD-VR090", "Number": 4002, "Message": "If production_OFU==0, then production_SI==0, production_MT==0, and vice-versa", "Type": "E"}, {"id": "OPD-VR091", "Number": 4003, "Message": "If liftings_OFU==0, then liftings_SI==0, and liftings_MT==0, and vice-versa", "Type": "E"}, {"id": "OPD-VR092", "Number": 4004, "Message": "If finalStock_OFU==0, then finalStock_SI==0, and finalStock_MT==0, and vice-versa", "Type": "E"}, {"id": "OPD-VR093", "Number": 4005, "Message": "If initialStock_CO_OFU==0, then initialStock__CO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR094", "Number": 4006, "Message": "If initialStock_CO_OFU>0, then initialStock_CO_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR095", "Number": 4007, "Message": "If initialStock_CO_OFU<0, then intialStock_CO_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR096", "Number": 4008, "Message": "If initialStock_PO_OFU==0, then intialStock_PO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR097", "Number": 4009, "Message": "If initialStock_PO_OFU>0, then intialStock_PO_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR098", "Number": 4010, "Message": "If initialStock_PO_OFU<0, then intialStock_PO_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR099", "Number": 4011, "Message": "If production_CO_OFU==0, then production_CO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR100", "Number": 4012, "Message": "If production_PO_OFU==0, then production_PO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR101", "Number": 4013, "Message": "If liftings_CO_OFU==0, then liftings_CO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR102", "Number": 4014, "Message": "If liftings_PO_OFU==0, then liftings_PO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR103", "Number": 4015, "Message": "If finalStock_CO_OFU==0, then finalStock_CO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR104", "Number": 4016, "Message": "If finalStock_CO_OFU>0, then finalStock_CO_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR105", "Number": 4017, "Message": "If finalStock_CO_OFU<0, then finalStock_CO_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR106", "Number": 4018, "Message": "If finalStock_PO_OFU==0, then finalStock_PO_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR107", "Number": 4019, "Message": "If finalStock_PO_OFU>0, then finalStock_PO_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR108", "Number": 4020, "Message": "If finalStock_PO_OFU<0, then finalStock_PO_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR109", "Number": 4021, "Message": "If qualityBankAdjustments_OFU==0, then qualityBankAdjustments_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR110", "Number": 4022, "Message": "If qualityBankAdjustments_OFU>0, then qualityBankAdjustments_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR111", "Number": 4023, "Message": "If qualityBankAdjustments_OFU<0, then qualityBankAdjustments_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR112", "Number": 4024, "Message": "If complexRefineryAdjustments_OFU==0, then complexRefineryAdjustments_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR113", "Number": 4025, "Message": "If complexRefineryAdjustments_OFU>0, then complexRefineryAdjustments_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR114", "Number": 4026, "Message": "If complexRefineryAdjustments_OFU<0, then complexRefineryAdjustments_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR115", "Number": 4027, "Message": "If externalRefineryAdjustments_OFU==0, then externalRefineryAdjustments_SI==0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR116", "Number": 4028, "Message": "If externalRefineryAdjustments_OFU>0, then externalRefineryAdjustments_SI>0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR117", "Number": 4029, "Message": "If externalRefineryAdjustments_OFU<0, then externalRefineryAdjustments_SI<0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR118", "Number": 10001, "Message": "The mimeType is not valid", "Type": "E"}, {"id": "OPD-VR119", "Number": 10002, "Message": "The documentData is not a correct Base64 enIdd string", "Type": "E"}, {"id": "OPD-VR120", "Number": 10003, "Message": "The documentData is not a correct serialized document for the submitted mimeType", "Type": "E"}, {"id": "OPD-VR121", "Number": 10004, "Message": "The document is already submitted in the system", "Type": "E"}, {"id": "OPD-VR122", "Number": 10005, "Message": "The Id does not match any submitted documentId", "Type": "E"}, {"id": "OPD-VR123", "Number": 99, "Message": "The checksum computed from the body is different from the supplied checksum in the header", "Type": "E"}, {"id": "OPD-VR124", "Number": 98, "Message": "The field is not defined in the JSON structure for the service/verb", "Type": "E"}, {"id": "OPD-VR125", "Number": 125, "Message": "The submitted request body is not valid", "Type": "E"}, {"id": "OPD-VR126", "Number": 1016, "Message": "The well is not valid for reservoir {0}", "Type": "E"}, {"id": "OPD-VR127", "Number": 1017, "Message": "The well is not valid for field {0}", "Type": "E"}, {"id": "OPD-VR128", "Number": 1018, "Message": "Date format is not correct (yyyy-MM-dd)", "Type": "E"}, {"id": "OPD-VR129", "Number": 129, "Message": "The submitted request is duplicated for the given ProductionDate. Use PUT instead.", "Type": "E"}, {"id": "OPD-VR130", "Number": 130, "Message": "The blockId is not operated by the company.", "Type": "E"}, {"id": "OPD-VR131", "Number": 131, "Message": "Date format is not correct (yyyy-MM-dd hh:mm:ss)", "Type": "E"}, {"id": "OPD-VR132", "Number": 132, "Message": "The reservoirId is not valid for field {0}", "Type": "E"}, {"id": "OPD-VR133", "Number": 133, "Message": "The complexId is null, so facilityId must be null too", "Type": "E"}, {"id": "OPD-VR134", "Number": 134, "Message": "The country is not ISO 3166 alpha-2 compliant", "Type": "E"}, {"id": "OPD-VR135", "Number": 135, "Message": "If gasInjected_OFU is not null, then gasInjected_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR136", "Number": 136, "Message": "If WaterInjected_OFU is not null, then WaterInjected_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR137", "Number": 137, "Message": "If WaterDischarge_OFU is not null, then WaterDischarge_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR138", "Number": 138, "Message": "If WaterSlop_OFU is not null, then WaterSlop_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR139", "Number": 139, "Message": "If WellHeadPressure_OFU is not null, then WellHeadPressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR140", "Number": 140, "Message": "If WellHeadTemperature_OFU is not null, then WellHeadTemperature_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR141", "Number": 141, "Message": "If BottomHolePressure_OFU is not null, then BottomHolePressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR142", "Number": 142, "Message": "If BottomHoleTemperature_OFU is not null, then BottomHoleTemperature_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR143", "Number": 143, "Message": "If DownholeGasificationPressure_OFU is not null, then DownholeGasificationPressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR144", "Number": 144, "Message": "If DownholeGasificationTemperature_OFU is not null, then DownholeGasificationTemperature_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR145", "Number": 145, "Message": "If CasingPressure_OFU is not null, then CasingPressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR146", "Number": 146, "Message": "If CasingTemperature_OFU is not null, then CasingTemperature_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR147", "Number": 147, "Message": "If FlowingTubingPressure_OFU is not null, then FlowingTubingPressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR148", "Number": 148, "Message": "If FlowingTubingTemperature_OFU is not null, then FlowingTubingTemperature_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR149", "Number": 149, "Message": "If InjectionPressure_OFU is not null, then InjectionPressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR150", "Number": 150, "Message": "If UpstreamChokePressure_OFU is not null, then UpstreamChokePressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR151", "Number": 151, "Message": "If UpstreamChokeTemperature_OFU is not null, then UpstreamChokeTemperature_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR152", "Number": 152, "Message": "If DownstreamChokePressure_OFU is not null, then DownstreamChokePressure_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR153", "Number": 153, "Message": "If GasImport_OFU==0, then GasImport_SI must be 0 too, and vice-versa", "Type": "E"}, {"id": "OPD-VR154", "Number": 154, "Message": "If GasImport_OFU is not null, then GasImport_SI must be not null too, and vice-versa", "Type": "E"}, {"id": "OPD-VR155", "Number": 155, "Message": "The submitted update request is not valid for the given ProductionDate", "Type": "E"}, {"id": "OPD-VR156", "Number": 156, "Message": "The complexId is not operated by the company.", "Type": "E"}, {"id": "OPD-VR157", "Number": 153, "Message": "The Online Production Date Timestamp is not between the request DateFrom and DateTo", "Type": "E"}, {"id": "OPD-VR158", "Number": 3007, "Message": "When LossMotive == 'Other', the Comments field is mandatory.", "Type": "E"}]