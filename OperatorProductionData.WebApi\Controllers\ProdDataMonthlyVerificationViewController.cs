using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataMonthlyVerificationViewController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataMonthlyVerificationViewService _service { get; set; }
        private IMapper _mapper { get; set; }

        public ProdDataMonthlyVerificationViewController(
                ILogger<ProdDataDailyController> logger,
                ProdDataMonthlyVerificationViewService verificationViewService,
                IMapper mapper
        )
        {
            _logger = logger;
            _service = verificationViewService;
            _mapper = mapper;
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetVerificationViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataMonthly Post - getverificationviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>> res = new ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>> apiResponse = res;
                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error getting sorted, filtered and paged verification view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Post - getverificationviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getallbyproductiondaterange")]
        public async Task<IActionResult> GetAllByProductionDateRange([FromQuery] int productionMonthStart, [FromQuery] int productionMonthEnd, [FromQuery] int productionYearStart, [FromQuery] int productionYearEnd)
        {
            _logger.LogInformation("Enter ProdDataMonthly - GetAllByProductionDate");
            ApiResponse<ICollection<ProdDataMonthlyVerificationView>> res = new ApiResponse<ICollection<ProdDataMonthlyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdDataMonthlyVerificationView>> apiResponse = res;
                apiResponse.Response = await _service.GetAllByProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataMonthly - Error GetAllByProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataMonthly Get - GetAllByProductionDate");
                return Ok(res);
            }
        }
    }
}
