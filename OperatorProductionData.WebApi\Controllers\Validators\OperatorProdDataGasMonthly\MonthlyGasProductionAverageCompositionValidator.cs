﻿using FluentValidation;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Services.Services;
using Upstream.Models.Models;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataGasMonthly
{

    public class MonthlyGasProductionAverageCompositionValidator : AbstractValidator<MonthlyGasProductionAverageComposition>
    {

        public MonthlyGasProductionAverageCompositionValidator(string complexId, GasReferenceData referenceData, GasValidationRuleCoreService validationRuleService)
        {

            var vr001 = validationRuleService.GetValidationRule("OPG-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPG-VR003");
            var vr004 = validationRuleService.GetValidationRule("OPG-VR004");
            var vr010 = validationRuleService.GetValidationRule("OPG-VR010");
            var vr027 = validationRuleService.GetValidationRule("OPG-VR027");
            

            var facilities = referenceData?.Complexes?.FirstOrDefault(x => x.Id == complexId)?.Facilities;

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString())
                .Must(x => facilities.Any(y => y.Id == x))
                    .WithMessage(string.Format(vr027.Message, complexId))
                    .WithErrorCode(vr027.Number.ToString())
                    .WithName(vr027.Type.ToString());

            RuleFor(x => x.GasTypeId)
               .Cascade(CascadeMode.Stop)
               .NotEmpty()
                   .WithMessage(vr001.Message)
                   .WithErrorCode(vr001.Number.ToString())
                   .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.Complexes.SelectMany(c => c.Facilities).SelectMany(c => c.GasTypes).Any(a => a.Id == x)))
                   .WithMessage(string.Format(vr010.Message))
                   .WithErrorCode(vr010.Number.ToString());

            RuleFor(x => x.GasCompositionId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => (referenceData.ReferenceTables.GasCompositions.Any(a => a.Id == x)))
                    .WithMessage(string.Format(vr010.Message))
                    .WithErrorCode(vr010.Number.ToString());

            RuleFor(x => x.MonthlyAverageGasComposition)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .LessThanOrEqualTo(100)
                    .WithMessage(vr004.Message)
                    .WithErrorCode(vr004.Number.ToString())
                    .WithName(vr004.Type.ToString());
        }


    }


}