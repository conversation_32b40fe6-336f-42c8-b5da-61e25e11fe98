using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;
using OperatorProductionData.Services.Services;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class ProdDataGasMonthlyVerificationViewController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private ProdDataGasMonthlyVerificationViewService _service { get; set; }
        private IMapper _mapper { get; set; }

        public ProdDataGasMonthlyVerificationViewController(
                ILogger<ProdDataDailyController> logger,
                ProdDataGasMonthlyVerificationViewService verificationViewService,
                IMapper mapper
        )
        {
            _logger = logger;
            _service = verificationViewService;
            _mapper = mapper;
        }

        [HttpPost]
        [Route("getlist")]
        public async Task<IActionResult> GetVerificationViewList([FromBody] GridDataLoadOptions options)
        {
            _logger.LogInformation("Enter ProdDataGasMonthly Post - getverificationviewlist");
            ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyVerificationView>> res = new ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<GridDataLoadResponse<ProdDataGasMonthlyVerificationView>> apiResponse = res;
                apiResponse.Response = await _service.GetSortedFilteredPagedList(options);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthly - Error getting sorted, filtered and paged verification view list");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthly Post - getverificationviewlist");
                return Ok(res);
            }
        }

        [HttpGet]
        [Route("getallbyproductiondaterange")]
        public async Task<IActionResult> GetAllByProductionDateRange([FromQuery] int productionMonthStart, [FromQuery] int productionMonthEnd, [FromQuery] int productionYearStart, [FromQuery] int productionYearEnd)
        {
            _logger.LogInformation("Enter ProdDataGasMonthly - GetAllByProductionDate");
            ApiResponse<ICollection<ProdDataGasMonthlyVerificationView>> res = new ApiResponse<ICollection<ProdDataGasMonthlyVerificationView>>
            {
                Status = ApiResponseStatus.Success
            };
            try
            {
                ApiResponse<ICollection<ProdDataGasMonthlyVerificationView>> apiResponse = res;
                apiResponse.Response = await _service.GetAllByProductionDateRange(productionMonthStart, productionMonthEnd, productionYearStart, productionYearEnd);
                return Ok(res);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ProdDataGasMonthly - Error GetAllByProductionDate");
                List<ApiProblemDetails> problems = new List<ApiProblemDetails>
                {
                    new ApiProblemDetails
                    {
                        Status = StatusCodes.Status400BadRequest,
                        Title = ex.Message,
                        Detail = ex.StackTrace
                    }
                };
                res.Status = ApiResponseStatus.Error;
                res.Problems = problems;
                _logger.LogInformation("Exit ProdDataGasMonthly Get - GetAllByProductionDate");
                return Ok(res);
            }
        }
    }
}
