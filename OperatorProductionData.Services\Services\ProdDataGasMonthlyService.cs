﻿using MongoDB.Bson;
using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataGasMonthly;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataGasMonthlyService : BaseRepository<ProdDataGasMonthly>
    {
        public ProdDataGasMonthlyService(string connectionString, string databaseName) : base(connectionString, databaseName, "ProdDataGasMonthly")
        {
        }

        /// <summary>
        /// Method to get Info about existing ProdDataGasMonthly's for the given ComplexId and ProductionYear and ProductionMonth
        /// </summary>
        /// <param name="complexId"></param>
        /// <param name="productionYear"></param>
        /// <param name="productionMonth"></param>
        /// <returns></returns>
        public async Task<Guid?> GetByComplexAndProductionDate(string complexId, int productionYear, int productionMonth)
        {
            var filterBuilder = Builders<ProdDataGasMonthly>.Filter;
            var filter = filterBuilder.Eq(x => x.ComplexId, complexId)
                            & filterBuilder.Eq(x => x.ProductionYear, productionYear)
                            & filterBuilder.Eq(x => x.ProductionMonth, productionMonth)
                            ;

            var prodDataGasMonthly = await GetAll(filter);

            return prodDataGasMonthly
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefault()
                    ?.VersionsCollectionId;
        }

        public async Task UpdateProdDataGasMonthlyRejectedMaterializedView(int productionMonthFilter, int productionYearFilter)
        {
           var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionMonth", productionMonthFilter },
                    { "ProductionYear", productionYearFilter }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "ProblemDetails", BsonNull.Value }
                }),
                new BsonDocument("$unionWith", new BsonDocument
                {
                    { "coll", "ProdDataGasMonthlyRejected" },
                     { "pipeline",
                        new BsonArray
                        {
                            new BsonDocument
                            {
                                { "$match", new BsonDocument
                                    {
                                        { "ProductionMonth", productionMonthFilter },
                                        { "ProductionYear", productionYearFilter }
                                    }
                                }
                            }
                        }
                    }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "CreatedDate", 1 },
                    { "ComplexId", "$ComplexId"},
                    { "ProductionYear", "$ProductionYear"},
                    { "ProductionMonth", "$ProductionMonth"},
                    { "Version", "$Version"},
                    { "Json", "$$ROOT"},
                    { "ErrorCount",
                        new BsonDocument
                        {
                            { "$cond",
                                new BsonArray{
                                    new BsonDocument
                                    {
                                        { "$isArray", "$ProblemDetails" }
                                    },
                                    //new BsonDocument
                                    //{
                                    //    { "$size", "$ProblemDetails" }
                                    //},
                                    new BsonDocument("$size", new BsonDocument
                                    {
                                        { "$filter", new BsonDocument
                                            {
                                                { "input", "$ProblemDetails" },
                                                { "as", "item" },
                                                { "cond", new BsonDocument("$eq", new BsonArray { "$$item.ProblemType", "E" }) }
                                            }
                                        }
                                    }),
                                    0
                                }
                            }

                        }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataGasMonthlyView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }

        public async Task UpdateProdDataGasMonthlyVerificationMaterializedView(int productionMonthFilter, int productionYearFilter)
        {
            var pipeline = new[]
             {
                 new BsonDocument("$match", new BsonDocument
                {
                    { "ProductionMonth", productionMonthFilter },
                    { "ProductionYear", productionYearFilter }
                }),
                new BsonDocument("$sort", new BsonDocument
                {
                    { "ProductionYear", -1 },
                    { "ProductionMonth", -1 }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id",  new BsonDocument{
                            {"ComplexId", "$ComplexId"},
                            {"ProductionYear", "$ProductionYear"},
                            {"ProductionMonth", "$ProductionMonth"}
                        }
                    },
                    { "MaxCreatedDate",
                        new BsonDocument
                        {
                            {
                                "$max", "$CreatedDate"
                            }
                        }
                    },
                    { "Items",
                        new BsonDocument
                        {
                            {
                                "$push", "$$ROOT"
                            }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "MaxCreatedDate", 1 },
                    { "ComplexId", "$_id.ComplexId" },
                    { "ProductionYear", "$_id.ProductionYear" },
                    { "ProductionMonth", "$_id.ProductionMonth" },
                    { "Json",
                    new BsonDocument
                        {{"$first",
                        new BsonDocument
                        {
                            {
                                "$slice", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                        "$filter",
                                        new BsonDocument
                                        {
                                            { "input", "$Items" },
                                            { "cond", new BsonDocument
                                            {
                                                { "$eq", new BsonArray
                                                {
                                                         "$$this.CreatedDate", "$MaxCreatedDate"
                                                }
                                            }}},
                                        }
                                        }
                                    },1
                                }
                            }
                        }
                        } }
                    }
                }),
                new BsonDocument("$merge", new BsonDocument
                {
                    {"into", "ProdDataGasMonthlyVerificationView" },
                    {"whenMatched", "replace" },
                    {"whenNotMatched", "insert" }
                })
            };

            var aggregateOptions = new AggregateOptions { AllowDiskUse = true };

            await _container.AggregateAsync<BsonDocument>(pipeline, aggregateOptions);
        }
    }
}
