using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataMonthlyVerificationViewWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataMonthlyVerificationViewWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>>> GetVerificationViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatamonthlyverificationview/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting verification view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataMonthlyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting verification view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataMonthlyVerificationView>>> GetAllByProductionDateRange(int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionMonthStart", productionMonthStart.ToString() },
                { "productionMonthEnd", productionMonthEnd.ToString() },
                { "productionYearStart", productionYearStart.ToString() },
                { "productionYearEnd", productionYearEnd.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthlyverificationview/getallbyproductiondaterange" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataMonthlyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetAllByProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataMonthlyVerificationView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataMonthlyVerificationView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataMonthlyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting monthly GetAllByProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }
    }
}
