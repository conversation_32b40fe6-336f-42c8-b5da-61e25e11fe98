﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyStorageLoadingSummary
    {
        public string FacilityId { get; set; }
        public string GasTypeId { get; set; }
        public decimal TotalGasTypeStorageTankLevel { get; set; }
        public decimal TotalGasTypeStorageTankVolume_OFU { get; set; }
        public decimal TotalGasTypeStorageTankVolume_SI { get; set; }
        public decimal TotalGasTypeStorageTankEnergy_OFU { get; set; }
        public decimal TotalGasTypeStorageTankEnergy_SI { get; set; }
        public decimal TotalGasTypeStorageOpeningInventoryVolume_OFU { get; set; }
        public decimal TotalGasTypeStorageOpeningInventoryVolume_SI { get; set; }
        public decimal TotalGasTypeStorageClosingInventoryVolume_OFU { get; set; }
        public decimal TotalGasTypeStorageClosingInventoryVolume_SI { get; set; }
        public decimal TotalGasTypeStorageAvailableVolume_OFU { get; set; }
        public decimal TotalGasTypeStorageAvailableVolume_SI { get; set; }
        public decimal TotalGasTypeStorageLoadingAvailableVolume_OFU { get; set; }
        public decimal TotalGasTypeStorageLoadingAvailableVolume_SI { get; set; }
        public decimal TotalGasTypeStorageEstimatedEnergy_BOE { get; set; }
        public decimal TotalStorageTankVolume_OFU { get; set; }
        public decimal TotalStorageTankVolume_SI { get; set; }
        public decimal TotalStorageTankEnergy_OFU { get; set; }
        public decimal TotalStorageTankEnergy_SI { get; set; }
        public decimal TotalStorageOpeningInventoryVolume_OFU { get; set; }
        public decimal TotalStorageOpeningInventoryVolume_SI { get; set; }
        public decimal TotalStorageClosingInventoryVolume_OFU { get; set; }
        public decimal TotalStorageClosingInventoryVolume_SI { get; set; }
        public decimal TotalStorageAvailableVolume_OFU { get; set; }
        public decimal TotalStorageAvailableVolume_SI { get; set; }
        public decimal TotalStorageLoadingAvailableVolume_OFU { get; set; }
        public decimal TotalStorageLoadingAvailableVolume_SI { get; set; }
        public decimal TotalStorageEstimatedEnergy_BOE { get; set; }

    }
}
