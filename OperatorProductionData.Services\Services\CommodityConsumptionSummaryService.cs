﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class CommodityConsumptionSummaryService : RepositoryBase<CommodityConsumptionSummaryEntity, CommodityConsumptionSummary>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public CommodityConsumptionSummaryService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitCommodityConsumptionSummary(CommodityConsumptionSummary CommodityConsumptionSummary)
        {
            _dbContext.CommodityConsumptionSummary.Add(
                _mapper.Map<CommodityConsumptionSummary, CommodityConsumptionSummaryEntity>(CommodityConsumptionSummary)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
