﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Middleware.Services;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using OperatorProductionData.Models.Models.Documents;
using OperatorProductionData.Services.Interfaces;
using OperatorProductionData.WebApi.Controllers.Validators;
using Document = OperatorProductionData.Models.Models.Documents.Document;

namespace OperatorProductionData.WebApi.Controllers
{
    [Route("[controller]")]
    public class DocumentController : ControllerBase
    {
        private ILogger _logger { get; set; }
        private IMapper _mapper { get; set; }
        private IDocumentService _service { get; set; }
        private ValidationRuleCoreService _validationRuleService { get; set; }

        public DocumentController(ILogger<DocumentController> logger, IDocumentService service, ValidationRuleCoreService validationRuleService, IMapper mapper)
        {
            _logger = logger;
            _service = service;
            _validationRuleService = validationRuleService;
            this._mapper = mapper;
        }

        [HttpPost]
        [Route("SubmitDocument")]
        public async Task<IActionResult> SubmitDocument([FromBody]Document document)
        {
            var apiResponse = new ApiResponse<IdGuidResponse>
            {
                Status = ApiResponseStatus.Success
            };

            try
            {
                var validator = new DocumentValidator(_validationRuleService);
                var result = validator.Validate(document);
                if (!result.IsValid)
                {
                    result.Errors.ForEach(x =>
                        apiResponse.Problems.Add(
                            new ApiProblemDetails()
                            {
                                Instance = x.PropertyName,
                                Detail = x.ErrorMessage,
                                ProblemType = x.PropertyName, // value forced to be Type ('E' or 'W')
                                ProblemCode = x.ErrorCode
                            })
                    );
                }
                else
                {
                    var id = await _service.AddDocumentByFileAsync(document);
                    apiResponse.Response = new IdGuidResponse { Id = id };
                }
            }
            catch (Exception ex)
            {
                var errorTitle = "Problem submiting Document.";
                _logger.LogError(ex, errorTitle);
                apiResponse.Status = ApiResponseStatus.Error;
                apiResponse.Problems.Add(
                        new ApiProblemDetails()
                        {
                            Status = StatusCodes.Status400BadRequest,
                            Title = errorTitle,
                            Detail = ex?.Message
                        }
                    );
            }

            return Ok(apiResponse);
        }

        [HttpGet]
        [Route("{companyCode}/{documentId}")]
        public async Task<IActionResult> GetDocument([FromRoute] string companyCode, [FromRoute] Guid documentId)
        {
            var apiResponse = new ApiResponse<DocumentDetails>();

            try
            {
                var file = await _service.GetDocumentAsync(documentId, companyCode);

                if (file != null)
                {
                    return file;
                }
                else
                {
                    return NotFound();
                }
            }
            catch
            {
                apiResponse.Status = ApiResponseStatus.Error;
                return BadRequest(apiResponse);
            }
        }

        [HttpGet]
        [Route("DocumentDetails/{companyCode}/{documentId}")]
        public async Task<IActionResult> GetDocumentDetails([FromRoute] string companyCode, [FromRoute] Guid documentId)
        {
            var apiResponse = new ApiResponse<DocumentDetails>();

            try
            {
                var fileDetails = await _service.GetDocumentDetailsAsync(documentId, companyCode);

                if (fileDetails != null)
                {
                    apiResponse.Response = _mapper.Map<DocumentDetails>(fileDetails);
                    apiResponse.Status = ApiResponseStatus.Success;
                }
                else
                {
                    return NotFound();
                }
            }
            catch
            {
                apiResponse.Status = ApiResponseStatus.Error;
                return BadRequest(apiResponse);
            }

            return Ok(apiResponse);
        }

        [HttpGet]
        [Route("DocumentsDetails/{companyCode}")]
        public async Task<IActionResult> GetDocumentsDetails([FromRoute] string companyCode, [FromQuery] List<Guid> ids)
        {
            var apiResponse = new ApiResponse<List<DocumentDetails>>();

            try
            {
                var documentsDetails = await _service.GetDocumentsDetailsAsync(ids, companyCode);

                if (documentsDetails != null)
                {
                    apiResponse.Response = _mapper.Map<List<DocumentDetails>>(documentsDetails);
                    apiResponse.Status = ApiResponseStatus.Success;
                }
                else
                {
                    apiResponse.Response = new List<DocumentDetails>();
                    apiResponse.Status = ApiResponseStatus.Success;
                }
            }
            catch
            {
                apiResponse.Status = ApiResponseStatus.Error;
                return BadRequest(apiResponse);
            }

            return Ok(apiResponse);
        }
    }
}
