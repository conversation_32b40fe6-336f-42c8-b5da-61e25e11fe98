﻿using System;

namespace OperatorProductionData.Models.Models.Dashboard
{
    public class OnlineDashboardDataAccumulated
    {
        public string BlockId { get; set; }
        public string? ComplexId { get; set; }
        public string? FacilityId { get; set; }
        public string? WellId { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public decimal OilProd_OFU { get; set; }
        public decimal OilProd_SI { get; set; }
        public decimal GasProd_OFU { get; set; }
        public decimal GasProd_SI { get; set; }
        public decimal WaterProd_OFU { get; set; }
        public decimal WaterProd_SI { get; set; }
    }
}
