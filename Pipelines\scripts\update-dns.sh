#!/bin/bash

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --record-set-name)
            recordSetName="$2"
            shift 2
            ;;
        --container-group)
            containerGroup="$2"
            shift 2
            ;;
        --resource-group)
            resourceGroup="$2"
            shift 2
            ;;
        --dns-zone)
            dnsZone="$2"
            shift 2
            ;;
        *)
            echo "Unknown argument: $1"
            exit 1
            ;;
    esac
done

# Ensure all required parameters are provided
if [ -z "$recordSetName" ] || [ -z "$containerGroup" ] || [ -z "$resourceGroup" ] || [ -z "$dnsZone" ]; then
    echo "Usage: $0 --record-set-name <RecordSetName> --container-group <ContainerGroupName> --resource-group <ResourceGroupName> --dns-zone <DnsZone>"
    exit 1
fi

# Get current IP of ACI
aciIP=$(az container show --name "$containerGroup" --resource-group "$resourceGroup" --query ipAddress.ip --output tsv)

# Attempt to update the A record
updateResult=$(az network private-dns record-set a update --name "$recordSetName" --resource-group "$resourceGroup" --zone-name "$dnsZone" --set "aRecords[0].ipv4Address=$aciIP" 2>&1)

if [[ $updateResult == *"does not exist"* ]]; then
    # Record set does not exist, create it and add an A record
    az network private-dns record-set a create --name "$recordSetName" --resource-group "$resourceGroup" --zone-name "$dnsZone"
    az network private-dns record-set a add-record --record-set-name "$recordSetName" --resource-group "$resourceGroup" --zone-name "$dnsZone" --ipv4-address "$aciIP"
fi