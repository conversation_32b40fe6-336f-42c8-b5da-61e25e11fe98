parameters:
- name: aciManifestLocation
  type: string
  default: ''

- name: recordSetName
  type: string
  default: ''

- name: containerGroup
  type: string
  default: ''

- name: resourceGroupName
  type: string
  default: ''

- name: dnsZone
  type: string
  default: ''

steps:
  - task: qetza.replacetokens.replacetokens-task.replacetokens@3
    displayName: 'Replace tokens'
    inputs:
        rootDirectory: ''
        targetFiles: |
          **/*.yaml
        encoding: 'auto'
        tokenPattern: 'custom'
        tokenPrefix: '#{'
        tokenSuffix: '}#'
        writeBOM: true
        escapeType: 'auto'
        verbosity: 'normal'

  - task: AzureCLI@2
    displayName: 'Deploy ACI to Azure'
    inputs:
       azureSubscription: 'Ae Subscription(8019a741-43f9-4d3c-a9cc-f9fcec2540e3)'
       scriptType: 'bash'
       scriptLocation: 'inlineScript'
       inlineScript: 'az container create --resource-group ${{parameters.resourceGroupName}} --file ${{parameters.aciManifestLocation}}'
       workingDirectory: '$(System.DefaultWorkingDirectory)'

  - script: |
      az login --service-principal --username $(AEEconomics.AzureDevOpsPrincipal.AppID) --password $(AEEconomics.AzureDevOpsPrincipal.Password) --tenant $(AEEconomics.AzureDevOpsPrincipal.Tenant)
      sed -i 's/\r$//' scripts/update-dns.sh
      chmod +x scripts/update-dns.sh
      ./scripts/update-dns.sh --record-set-name ${{parameters.recordSetName}} --container-group ${{parameters.containerGroup}} --resource-group ${{parameters.resourceGroupName}} --dns-zone ${{parameters.dnsZone}}
    displayName: 'Update ACI DNS'
    workingDirectory: $(Build.SourcesDirectory)/Pipelines