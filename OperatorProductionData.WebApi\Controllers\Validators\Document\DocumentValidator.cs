﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using OperatorProductionData.Models.Models.Documents;

namespace OperatorProductionData.WebApi.Controllers.Validators
{
    public class DocumentValidator : AbstractValidator<Document>
    {
        public DocumentValidator(ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");

            RuleFor(x => x.MimeType)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.Filename)
               .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());


        }
    }
}
