﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using OperatorProductionData.Models.Utilities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class ProdDataDaily : IVersionable
    {
        [Key]
        public Guid Id { get; set; }
        public string BlockId { get; set; }
        public string ProductionDate { get; set; }
        public virtual DateTime ParsedProductionDate {
            set {
                _tempProductionDate = DateUtils.ConvertDateTimeString(ProductionDate, "yyyy-MM-dd");
            }

            get
            {
                return _tempProductionDate;
            }
        }

        private DateTime _tempProductionDate;
        
        public string? OperatorReference { get; set; }
        public ICollection<DailyOperationSummaryByFacility> OperationSummaryByFacility { get; set; }
        public ICollection<DailyOperationSummaryByField> OperationSummaryByField { get; set; }
        public ICollection<DailyProductionLoss>? ProductionLoss { get; set; }
        public ICollection<DailyProductionLossSummary>? ProductionLossSummary { get; set; }
        public ICollection<DailyWaterQuality> WaterQuality { get; set; }
        public ICollection<DailyCommodityConsumptionSummary> CommodityConsumptionSummary { get; set; }
        public ICollection<DailyEquipmentStatus>? EquipmentStatus { get; set; }
        public ICollection<DailyWellStatus>? WellStatus { get; set; }
        public ICollection<DailyOperationComments>? OperationComments { get; set; }
        public ICollection<DailyDetailedProduction> DetailedProduction { get; set; }
        public ICollection<DailyDetailedGasInjection>? DetailedGasInjection { get; set; }
        public ICollection<DailyDetailedWaterInjection>? DetailedWaterInjection { get; set; }
        public ICollection<DailyOilStorageSummary>? OilStorageSummary { get; set; }
        public ICollection<DailyLiftings>? Liftings { get; set; }
        public ICollection<DailySubmittedDocuments>? SubmittedDocuments { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public string ReasonId { get; set; }
        public string ReasonDescription { get; set; }
        public Guid VersionsCollectionId { get; set; }
        public bool Active { get; set; } = true; //TODO :: THIS HAS TO COME FROM STRATUS IVersionable Interface
        public int Version { get; set; }
    }
}
