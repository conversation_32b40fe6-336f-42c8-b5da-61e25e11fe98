﻿namespace OperatorProductionData.Services.Interfaces
{
    using Microsoft.AspNetCore.Mvc;
    using OperatorProductionData.Models.Models.Documents;
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    public interface IDocumentService
    {
        public Task<Guid> AddDocumentByFileAsync(Document document);

        public Task<FileContentResult?> GetDocumentAsync(Guid id, string companyCode = "");

        public Task<DocumentDetails?> GetDocumentDetailsAsync(Guid id, string companyCode = "");

        public Task<List<DocumentDetails>> GetDocumentsDetailsAsync(List<Guid> ids, string companyCode = "");
    }
}