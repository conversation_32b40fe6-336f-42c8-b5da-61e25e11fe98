﻿using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.ApiModels;
using OperatorProductionData.Models.Models.Dashboard;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataMonthlyWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataMonthlyWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> SubmitProdDataMonthly(ReferenceData infrastructure, ProdDataMonthly prodDataMonthly)
        {
            var obj = new InfrastructureProdDataMonthly
            {
                Infrastructure = infrastructure,
                ProdDataMonthly = prodDataMonthly
            };

            HttpResponseMessage response = await _client.PostAsJsonAsync("proddatamonthly/SubmitProdDataMonthly", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error getting model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error getting model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IdGuidResponse>> ChangeProdDataMonthly(ReferenceData infrastructure, ProdDataMonthly prodDataMonthly)
        {
            var obj = new InfrastructureProdDataMonthly
            {
                Infrastructure = infrastructure,
                ProdDataMonthly = prodDataMonthly
            };

            HttpResponseMessage response = await _client.PutAsJsonAsync("proddatamonthly/ChangeProdDataMonthly", obj);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                    new ApiProblemDetails()
                        {
                            Status = (int?)response.StatusCode,
                            Title = "Error changing model",
                            Detail = response.ReasonPhrase
                        }
                    }
                };

                return errorRes;
            }

            var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<IdGuidResponse>>();
            if (apiResponse == null)
            {
                var errorRes = new ApiResponse<IdGuidResponse>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails> {
                        new ApiProblemDetails()
                            {
                                Title = "Error changing model",
                                Detail = "Null response"
                            }
                        }
                };
                return errorRes;
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataMonthly>>> GetList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatamonthly/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataMonthly>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataMonthly>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataMonthly>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataMonthly>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>>> GetViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatamonthly/getviewlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataMonthlyView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataMonthlyViewDetails>>> GetViewDetailsList(string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getviewdetailslist");
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataMonthlyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataMonthlyViewDetails>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataMonthlyViewDetails>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataMonthlyViewDetails>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataMonthlyGeneric>> GetProdDataMonthlyViewJson(Guid id)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "id", id.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getproddatamonthlyviewjson" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataMonthlyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting monthly view data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataMonthlyGeneric> apiResponse = await httpResponseMessage
                                            .Content
                                            .ReadFromJsonAsync<ApiResponse<ProdDataMonthlyGeneric>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataMonthlyGeneric>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting monthly view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<ProductionDashboardDto>>> GetMonthlyDashboardData(
            string productionYear,
            string productionMonth, 
            string[] blockIds)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionYear", productionYear },
                { "productionMonth", productionMonth },
                { "blockIds", blockIds }
            };


            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getmonthlydashboarddata" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<ProductionDashboardDto>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting monthly dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<ProductionDashboardDto>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<ProductionDashboardDto>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<ProductionDashboardDto>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<QuarterlyDashboardData>>> GetQuarterlyDashboardData(string[] blockIds)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "blockIds", blockIds }
            };


            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getquarterlydashboarddata" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<QuarterlyDashboardData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting quarterly dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<QuarterlyDashboardData>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<QuarterlyDashboardData>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<QuarterlyDashboardData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<IEnumerable<YearlyDashboardData>>> GetYearlyDashboardData(string[] blockIds)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "blockIds", blockIds }
            };


            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getyearlydashboarddata" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<IEnumerable<YearlyDashboardData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting yearly dashboard data",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<IEnumerable<YearlyDashboardData>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<YearlyDashboardData>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<IEnumerable<YearlyDashboardData>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataMonthlyVerificationView>>> GetMonthlyDashboardDataByRange(
            int productionMonthStart, int productionMonthEnd, int productionYearStart, int productionYearEnd, string[] blockIds)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionMonthStart", productionMonthStart.ToString() },
                { "productionMonthEnd", productionMonthEnd.ToString() },
                { "productionYearStart", productionYearStart.ToString() },
                { "productionYearEnd", productionYearEnd.ToString() },
                { "blockIds", blockIds }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getmonthlydashboarddatabyrange" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataMonthlyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting monthly dashboard data by range",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataMonthlyVerificationView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataMonthlyVerificationView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataMonthlyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting monthly dashboard data by range",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ProdDataInfo?>> GetByBlockAndProductionDate(string blockId, int productionYear, int productionMonth)
        {

            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "blockId", blockId },
                { "productionYear", productionYear.ToString() },
                { "productionMonth", productionMonth.ToString() }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getbyblockandproductiondate" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataInfo?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetByBlockAndProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataInfo?> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ProdDataInfo?>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataInfo?>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting monthly GetByBlockAndProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;

        }

        public virtual async Task<ApiResponse<ProdDataMonthlyView>> GetById(string blockId, string productionYear, string productionMonth, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            var queryParams = new QueryBuilder
            {
                { "blockId", blockId },
                { "productionYear", productionYear },
                { "productionMonth", productionMonth }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatamonthly/getbyid" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ProdDataMonthlyView>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting montlhy production data by ID",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ProdDataMonthlyView> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ProdDataMonthlyView>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ProdDataMonthlyView>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting montlhy production data by ID",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }
    }
}
