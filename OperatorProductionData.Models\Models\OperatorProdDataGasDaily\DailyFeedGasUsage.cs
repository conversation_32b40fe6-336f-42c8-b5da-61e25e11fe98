﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyFeedGasUsage
    {
        public string FacilityId { get; set; }
        public string GasFeedBlockId { get; set; }
        public decimal FeedGasUsageEnergy_OFU { get; set; }
        public decimal FeedGasUsageEnergy_SI { get; set; }
        public decimal FeedGasUsageVolume_OFU { get; set; }
        public decimal FeedGasUsageVolume_SI { get; set; }
        public decimal FeedGasUsageMonthToDateEnergy_OFU { get; set; }
        public decimal FeedGasUsageMonthToDateEnergy_SI { get; set; }
        public decimal FeedGasUsageMonthToDateVolume_OFU { get; set; }
        public decimal FeedGasUsageMonthToDateVolume_SI { get; set; }
        public decimal FeedGasUsageGRHV { get; set; }
    }
}
