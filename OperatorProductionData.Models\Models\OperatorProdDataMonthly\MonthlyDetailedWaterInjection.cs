﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyDetailedWaterInjection
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? FieldId { get; set; }
        public string? ReservoirId { get; set; }
        public string? WellId { get; set; }
        public decimal ExpectedWaterInjection_OFU { get; set; }
        public decimal ExpectedWaterInjection_SI { get; set; }
        public decimal MonthWaterInjection_OFU { get; set; }
        public decimal MonthWaterInjection_SI { get; set; }
        public decimal YearWaterInjection_OFU { get; set; }
        public decimal YearWaterInjection_SI { get; set; }
        public decimal MonthOperationDays { get; set; }
        public decimal YearOperationDays { get; set; }
        public decimal? Choke { get; set; }
        public decimal? InjectionPressure_OFU { get; set; }
        public decimal? InjectionPressure_SI { get; set; }
        public decimal? WellHeadPressure_OFU { get; set; }
        public decimal? WellHeadPressure_SI { get; set; }
        public decimal? WellHeadTemperature_OFU { get; set; }
        public decimal? WellHeadTemperature_SI { get; set; }
        public decimal? BottomHolePressure_OFU { get; set; }
        public decimal? BottomHolePressure_SI { get; set; }
        public decimal? BottomHoleTemperature_OFU { get; set; }
        public decimal? BottomHoleTemperature_SI { get; set; }
        public decimal? DownholeGasificationPressure_OFU { get; set; }
        public decimal? DownholeGasificationPressure_SI { get; set; }
        public decimal? DownholeGasificationTemperature_OFU { get; set; }
        public decimal? DownholeGasificationTemperature_SI { get; set; }
        public decimal? UpstreamChokePressure_OFU { get; set; }
        public decimal? UpstreamChokePressure_SI { get; set; }
        public decimal? UpstreamChokeTemperature_OFU { get; set; }
        public decimal? UpstreamChokeTemperature_SI { get; set; }
        public decimal? DownstreamChokePressure_OFU { get; set; }
        public decimal? DownstreamChokePressure_SI { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
