﻿using MongoDB.Driver;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class ProdDataDailyViewService : BaseRepository<ProdDataDailyView>
    {
        public ProdDataDailyViewService(IMongoCollection<ProdDataDailyView> container) : base(container)
        {
        }

        public async Task<ProdDataDailyGeneric?> GetJson(Guid id)
        {
            List<ProdDataDailyView> returnList =
                    (await _container.FindAsync(
                            x => x.Id == id
                        )
                    )
                    .ToList();

            return returnList.SingleOrDefault()?.Json;
        }

        public async Task<ICollection<ProdDataDailyViewDetails>> GetAllViewDetails()
        {
            var filterBuilder = Builders<ProdDataDailyView>.Filter;
            var filterDefinition = filterBuilder.Empty;

            var list = _container.Find(filterDefinition)
                .Project(x => new ProdDataDailyViewDetails
                {
                    BlockId = x.BlockId,
                    ProductionDate = x.ProductionDate,
                    CreatedDate = x.CreatedDate,
                    ErrorCount = x.ErrorCount,
                    ErrorCountStr = x.ErrorCount > 0 ? x.ErrorCount.ToString() : string.Empty,
                    Status = x.ErrorCount > 0 ? "Recebido / Não Aceite" : "Recebido",
                    Version = x.Version,
                    OperatorReference = x.Json.OperatorReference,
                    ParsedProductionDate = x.Json.ParsedProductionDate,
                    Id = x.Id
                })
                .ToList();

            return list;
        }
    }
}
