[{"id": "OPG-VR001", "Number": 1, "Message": "The field is required", "Type": "E"}, {"id": "OPG-VR002", "Number": 2, "Message": "The field value is of the wrong type or exceeds the type capacity", "Type": "E"}, {"id": "OPG-VR003", "Number": 3, "Message": "The field value cannot be negative", "Type": "E"}, {"id": "OPG-VR004", "Number": 4, "Message": "The field value cannot be >100%", "Type": "E"}, {"id": "OPG-VR005", "Number": 5, "Message": "The field value cannot be >=100%", "Type": "E"}, {"id": "OPG-VR006", "Number": 6, "Message": "The field value must be between 0h and 24h", "Type": "E"}, {"id": "OPG-VR007", "Number": 7, "Message": "The field value must be between 0 days and 28/29/30/31 days", "Type": "E"}, {"id": "OPG-VR008", "Number": 8, "Message": "The field value must be between 0 days and 365/366 days", "Type": "E"}, {"id": "OPG-VR009", "Number": 9, "Message": "The array cannot be empty", "Type": "E"}, {"id": "OPG-VR010", "Number": 10, "Message": "The Id does not exist in SMP", "Type": "E"}, {"id": "OPG-VR011", "Number": 10001, "Message": "The mimeType is not valid", "Type": "E"}, {"id": "OPG-VR012", "Number": 10002, "Message": "The documentData is not a correct Base64 encoded string", "Type": "E"}, {"id": "OPG-VR013", "Number": 10003, "Message": "The documentData is not a correct serialized document for the submitted mimeType", "Type": "E"}, {"id": "OPG-VR014", "Number": 10004, "Message": "The document is already submitted in the system", "Type": "E"}, {"id": "OPG-VR015", "Number": 10005, "Message": "The Id does not match any submitted documentId", "Type": "E"}, {"id": "OPG-VR016", "Number": 99, "Message": "The checksum computed from the body is different from the supplied checksum in the header", "Type": "E"}, {"id": "OPG-VR017", "Number": 98, "Message": "The field is not defined in the JSON structure for the service/verb", "Type": "E"}, {"id": "OPG-VR018", "Number": 1001, "Message": "The productionDate cannot be earlier than SMP inception date", "Type": "E"}, {"id": "OPG-VR019", "Number": 1002, "Message": "The productionDate cannot be in the future", "Type": "E"}, {"id": "OPG-VR020", "Number": 1003, "Message": "The productionMonth must be between 1 and 12", "Type": "E"}, {"id": "OPG-VR021", "Number": 1004, "Message": "The productionMonth + productionYear cannot be in the future", "Type": "E"}, {"id": "OPG-VR022", "Number": 1005, "Message": "The productionMonth + productionYear cannot be earlier than SMP inception month", "Type": "E"}, {"id": "OPG-VR023", "Number": 1006, "Message": "The commentDateTime must be within the reported productionDate", "Type": "E"}, {"id": "OPG-VR024", "Number": 1007, "Message": "The comments is null or empty and commentedBy has value", "Type": "E"}, {"id": "OPG-VR025", "Number": 1008, "Message": "The comments has value and commentedBy is null or empty", "Type": "E"}, {"id": "OPG-VR026", "Number": 1009, "Message": "The additional description is mandatory for the selected reason and reasonDescription is null or empty", "Type": "E"}, {"id": "OPG-VR027", "Number": 1010, "Message": "The facilityId is not valid for complex {0}", "Type": "E"}, {"id": "OPG-VR028", "Number": 1011, "Message": "The gasFeedBlocksId is not valid for facility {0}", "Type": "E"}, {"id": "OPG-VR029", "Number": 1012, "Message": "The gasTanksId is not valid for facility and gasTypes{0}", "Type": "E"}, {"id": "OPG-VR030", "Number": 1013, "Message": "The domesticGasSupplySourcesId is not valid for facility {0}", "Type": "E"}, {"id": "OPG-VR031", "Number": 1014, "Message": "The domesticGasDeliveryPointsId is not valid for facility {0}", "Type": "E"}, {"id": "OPG-VR032", "Number": 1015, "Message": "The domesticGasSupplySystemId is not valid for facility {0}", "Type": "E"}, {"id": "OPG-VR033", "Number": 1017, "Message": "The vesselIMO does not exist in SMP", "Type": "E"}, {"id": "OPG-VR034", "Number": 34, "Message": "Date format is not correct (yyyy-MM-dd)", "Type": "E"}, {"id": "OPG-VR035", "Number": 35, "Message": "The field value cannot be > 101", "Type": "E"}, {"id": "OPG-VR036", "Number": 36, "Message": "The field value cannot be < 99", "Type": "E"}]