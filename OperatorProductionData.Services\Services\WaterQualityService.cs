﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities.OperatorProductionDataDaily;

namespace OperatorProductionData.Services.Services
{
    public class WaterQualityService : RepositoryBase<MonthlyWaterQualityEntity, WaterQuality>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public WaterQualityService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitWaterQuality(WaterQuality WaterQuality)
        {
            _dbContext.WaterQuality.Add(
                _mapper.Map<WaterQuality, MonthlyWaterQualityEntity>(WaterQuality)
            );
            return await _dbContext.SaveChangesAsync();
        }
    }
}
