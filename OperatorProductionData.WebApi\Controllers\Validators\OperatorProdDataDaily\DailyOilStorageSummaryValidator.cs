﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataDaily;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataDaily
{
    public class DailyOilStorageSummaryValidator : AbstractValidator<DailyOilStorageSummary>
    {
        public DailyOilStorageSummaryValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr016 = validationRuleService.GetValidationRule("OPD-VR016");
            var vr017 = validationRuleService.GetValidationRule("OPD-VR017");
            var vr019 = validationRuleService.GetValidationRule("OPD-VR019");
            var vr020 = validationRuleService.GetValidationRule("OPD-VR020");
            var vr021 = validationRuleService.GetValidationRule("OPD-VR021");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr037 = validationRuleService.GetValidationRule("OPD-VR037");
            var vr038 = validationRuleService.GetValidationRule("OPD-VR038");
            var vr039 = validationRuleService.GetValidationRule("OPD-VR039");
            var vr040 = validationRuleService.GetValidationRule("OPD-VR040");
            var vr062 = validationRuleService.GetValidationRule("OPD-VR062");
            var vr063 = validationRuleService.GetValidationRule("OPD-VR063");
            var vr064 = validationRuleService.GetValidationRule("OPD-VR064");
            var vr065 = validationRuleService.GetValidationRule("OPD-VR065");
            var vr133 = validationRuleService.GetValidationRule("OPD-VR133");

            var complexes = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes;

            var wells = infrastructure?.Blocks?
                .Where(b => b.Id.Equals(blockId))
                .SelectMany(b => b.DevelopmentAreas
                    .SelectMany(da => da.Fields
                        .SelectMany(f => f.Reservoirs.
                            SelectMany(f => f.Wells.Select(f => f.Id)))));


            RuleFor(x => x.ComplexId)
                .Cascade(CascadeMode.Stop)
                .Must(x => complexes != null && complexes.Any(y => y.Id == x))
                    .When(x => !string.IsNullOrEmpty(x.ComplexId), ApplyConditionTo.CurrentValidator)
                        .WithMessage(string.Format(vr016.Message, blockId))
                        .WithErrorCode(vr016.Number.ToString())
                        .WithName(vr016.Type.ToString());

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var facilities = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities;
                    if (!string.IsNullOrEmpty(x) && (facilities != null && !facilities.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr017.Message, c.InstanceToValidate.ComplexId),
                            ErrorCode = vr017.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                    if (string.IsNullOrEmpty(x) && !string.IsNullOrEmpty(c.InstanceToValidate.ComplexId))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr133.Message,
                            ErrorCode = vr133.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    };
                })
                .When(x => !string.IsNullOrEmpty(x.FacilityId) && (complexes != null && complexes.Any(y => y.Id == x.ComplexId)), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.SystemId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var systems = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems.ToList();
                    if (!string.IsNullOrEmpty(x) && (systems != null && !systems.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr019.Message, c.InstanceToValidate.FacilityId),
                            ErrorCode = vr019.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }

                    if (string.IsNullOrEmpty(x) && !string.IsNullOrEmpty(c.InstanceToValidate.EquipmentId))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = vr020.Message,
                            ErrorCode = vr020.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    };
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId)), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.EquipmentId)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    var equipments = complexes?.FirstOrDefault(y => y.Id == c.InstanceToValidate.ComplexId)?.Facilities?.FirstOrDefault(z => z.Id == c.InstanceToValidate.FacilityId)?.Systems?.
                                                     FirstOrDefault(s => s.Id == c.InstanceToValidate.SystemId)?.Equipment.ToList();
                    if (!string.IsNullOrEmpty(x) && (equipments != null && !equipments.Any(y => y.Id == x)))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr021.Message, c.InstanceToValidate.SystemId),
                            ErrorCode = vr021.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                }).When(x => complexes.Any(y => y.Id == x.ComplexId && y.Facilities.Any(z => z.Id == x.FacilityId && z.Systems.Any(s => s.Id == x.SystemId))), ApplyConditionTo.CurrentValidator);

            RuleFor(x => x.InitialStock_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr062.Message)
                        .WithErrorCode(vr062.Number.ToString())
                        .WithName(vr062.Type.ToString());

            RuleFor(x => x.InitialStock_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.InitialStock_OFU == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr062.Message)
                       .WithErrorCode(vr062.Number.ToString())
                       .WithName(vr062.Type.ToString());

            RuleFor(x => x.Production_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.Production_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr063.Message)
                        .WithErrorCode(vr063.Number.ToString())
                        .WithName(vr063.Type.ToString());

            RuleFor(x => x.Production_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.Production_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr063.Message)
                        .WithErrorCode(vr063.Number.ToString())
                        .WithName(vr063.Type.ToString());

            RuleFor(x => x.Liftings_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                    .WithMessage(vr003.Message)
                    .WithErrorCode(vr003.Number.ToString())
                    .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Liftings_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr064.Message)
                        .WithErrorCode(vr064.Number.ToString())
                        .WithName(vr064.Type.ToString());

            RuleFor(x => x.Liftings_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                    .When(x => x.Liftings_OFU == 0, ApplyConditionTo.CurrentValidator)
                       .WithMessage(vr064.Message)
                       .WithErrorCode(vr064.Number.ToString())
                       .WithName(vr064.Type.ToString());

            RuleFor(x => x.FinalStock_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.FinalStock_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr065.Message)
                        .WithErrorCode(vr065.Number.ToString())
                        .WithName(vr065.Type.ToString());

            RuleFor(x => x.FinalStock_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Equal(0)
                    .When(x => x.FinalStock_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr065.Message)
                        .WithErrorCode(vr065.Number.ToString())
                        .WithName(vr065.Type.ToString());

            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                   .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                       .WithMessage(vr023.Message)
                       .WithErrorCode(vr023.Number.ToString())
                       .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.Comments))
                        .WithMessage(vr024.Message)
                        .WithErrorCode(vr024.Number.ToString())
                        .WithName(vr024.Type.ToString());
        }
    }
}
