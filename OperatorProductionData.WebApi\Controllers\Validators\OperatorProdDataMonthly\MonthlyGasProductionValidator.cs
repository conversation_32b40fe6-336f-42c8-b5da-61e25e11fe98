﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly
{

    public class MonthlyGasProductionValidator : AbstractValidator<MonthlyGasProduction>
    {
        public MonthlyGasProductionValidator(string blockId, ReferenceData infrastructure, ValidationRuleCoreService validationRuleService)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");

            var facilities = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Complexes?.SelectMany(a => a.Facilities);
            var lpgType = infrastructure?.ReferenceTables.LpgProductTypes;

            RuleFor(x => x.FacilityId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (facilities == null || !facilities.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr010.Message),
                            ErrorCode = vr010.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.LpgTypeId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Custom((x, c) =>
                {
                    if (lpgType == null || !lpgType.Any(y => y.Id == x))
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr010.Message),
                            ErrorCode = vr010.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });
        }
    }
}
