FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

WORKDIR /app

COPY . .

RUN for file in $(ls */*.csproj); do dotnet restore "$file"; done

RUN dotnet build -c Release -o /app/build -nowarn:*

RUN dotnet publish -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime

WORKDIR /app

COPY --from=build /app/publish .

EXPOSE 80

ENTRYPOINT ["dotnet", "OperatorProductionData.WebApi.dll"]