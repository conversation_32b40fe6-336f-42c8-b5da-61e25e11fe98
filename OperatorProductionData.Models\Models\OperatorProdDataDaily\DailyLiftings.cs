﻿using OperatorProductionData.Models.Utilities;
using System;
using System.Collections.Generic;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyLiftings
    {
        public string StartDate { get; set; }
        public virtual DateTime ParsedStartDate
        {
            set
            {
                _tempStartDate = DateUtils.ConvertDateTimeString(StartDate, "yyyy-MM-dd HH:mm:ss");
            }

            get
            {
                return _tempStartDate;
            }
        }

        private DateTime _tempStartDate;
        public string EndDate { get; set; }
        public virtual DateTime ParsedEndDate
        {
            set
            {
                _tempEndDate = DateUtils.ConvertDateTimeString(EndDate, "yyyy-MM-dd HH:mm:ss"); ;
            }

            get
            {
                return _tempEndDate;
            }
        }

        private DateTime _tempEndDate;
        public string OperatorReference { get; set; }
        public string VesselImo { get; set; }
        public string VesselName { get; set; }
        public bool IsInternal { get; set; }
        public string BlendId { get; set; }
        public decimal QuantityLoaded_OFU { get; set; }
        public decimal QuantityLoaded_SI { get; set; }
        public decimal Gravity { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public decimal? Salinity { get; set; }
        public decimal? Salinity_MgL { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
        public ICollection<DailyAllocations> Allocations { get; set; }



    }
}
