﻿namespace OperatorProductionData.Models.Models.OperatorProdDataMonthly
{
    public class MonthlyOperationSummaryByField
    {
        public string? ComplexId { get; set; }
        public string? FacilityId { get; set; }
        public string FieldId { get; set; }
        public decimal ExpectedOilProd_OFU { get; set; }
        public decimal ExpectedOilProd_SI { get; set; }
        public decimal ActualOilProd_OFU { get; set; }
        public decimal ActualOilProd_SI { get; set; }
        public decimal GasProd_OFU { get; set; }
        public decimal GasProd_SI { get; set; }
        public decimal? GasInjected_OFU { get; set; }
        public decimal? GasInjected_SI { get; set; }
        public decimal GasExported_OFU { get; set; }
        public decimal GasExported_SI { get; set; }
        public decimal GasFlared_OFU { get; set; }
        public decimal GasFlared_SI { get; set; }
        public decimal? GasImport_OFU { get; set; }
        public decimal? GasImport_SI { get; set; }
        public decimal GasFuel_OFU { get; set; }
        public decimal GasFuel_SI { get; set; }
        public decimal GasLift_OFU { get; set; }
        public decimal GasLift_SI { get; set; }
        public decimal WaterProd_OFU { get; set; }
        public decimal WaterProd_SI { get; set; }
        public decimal? WaterInjected_OFU { get; set; }
        public decimal? WaterInjected_SI { get; set; }
        public decimal? WaterDischarge_OFU { get; set; }
        public decimal? WaterDischarge_SI { get; set; }
        public decimal? WaterSlop_OFU { get; set; }
        public decimal? WaterSlop_SI { get; set; }
        public decimal WaterCut { get; set; }
        public decimal BasicSedimentWater { get; set; }
        public decimal? VoidageReplacementRatio { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
