﻿using Ae.Stratus.Core.Middleware.Services;
using FluentValidation;
using FluentValidation.Results;
using OperatorProductionData.Models.Models.OperatorProdDataMonthly;
using System.Globalization;
using Upstream.Models.Models;

namespace OperatorProductionData.WebApi.Controllers.Validators.OperatorProdDataMonthly
{
    public class MonthlyAllocationsValidator : AbstractValidator<MonthlyAllocations>
    {
        public MonthlyAllocationsValidator(string blockId, string blendId, ValidationRuleCoreService validationRuleService, ReferenceData infrastructure)
        {
            var vr001 = validationRuleService.GetValidationRule("OPD-VR001");
            var vr003 = validationRuleService.GetValidationRule("OPD-VR003");
            var vr010 = validationRuleService.GetValidationRule("OPD-VR010");
            var vr023 = validationRuleService.GetValidationRule("OPD-VR023");
            var vr024 = validationRuleService.GetValidationRule("OPD-VR024");
            var vr067 = validationRuleService.GetValidationRule("OPD-VR067");
            var vr134 = validationRuleService.GetValidationRule("OPD-VR134");

            var participants = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Participants;
            var blends = infrastructure?.Blocks.FirstOrDefault(x => x.Id == blockId)?.Blends;
            var products = infrastructure?.Blocks?.FirstOrDefault(x => x.Id == blockId)?.Blends?.FirstOrDefault(x => x.Id == blendId)?.Products;

            RuleFor(x => x.AllocationReference)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString());

            RuleFor(x => x.ExporterNif)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                    .WithName(vr001.Type.ToString())
                .Must(x => participants != null && participants.Any(y => y.Nif == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.Quantity_OFU)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                     .WithMessage(vr001.Message)
                     .WithErrorCode(vr001.Number.ToString())
                     .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.Quantity_SI == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr067.Message)
                        .WithErrorCode(vr067.Number.ToString())
                        .WithName(vr067.Type.ToString());

            RuleFor(x => x.Quantity_SI)
                .Cascade(CascadeMode.Stop)
                .NotNull()
                     .WithMessage(vr001.Message)
                     .WithErrorCode(vr001.Number.ToString())
                     .WithName(vr001.Type.ToString())
                .GreaterThanOrEqualTo(0)
                     .WithMessage(vr003.Message)
                     .WithErrorCode(vr003.Number.ToString())
                     .WithName(vr003.Type.ToString())
                .Equal(0)
                     .When(x => x.Quantity_OFU == 0, ApplyConditionTo.CurrentValidator)
                        .WithMessage(vr067.Message)
                        .WithErrorCode(vr067.Number.ToString())
                        .WithName(vr067.Type.ToString());

            RuleFor(x => x.ProductId)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .WithMessage(vr001.Message)
                    .WithErrorCode(vr001.Number.ToString())
                .Must(x => products != null && products.Count > 0 && products.Any(y => y.Id == x))
                    .WithMessage(vr010.Message)
                    .WithErrorCode(vr010.Number.ToString())
                    .WithName(vr010.Type.ToString());

            RuleFor(x => x.DestinationCountry)
                .Cascade(CascadeMode.Stop)
                .Custom((x, c) =>
                {
                    bool Iso3166Compliant;

                    try
                    {
                        RegionInfo info = new RegionInfo(x);
                        Iso3166Compliant = info.TwoLetterISORegionName.Equals(x);
                    }
                    catch (ArgumentException ex)
                    {
                        Iso3166Compliant = false;
                        // The code was not a valid country code
                    }

                    if (!string.IsNullOrEmpty(x) && !Iso3166Compliant)
                    {
                        c.AddFailure(new ValidationFailure
                        {
                            ErrorMessage = string.Format(vr134.Message),
                            ErrorCode = vr134.Number.ToString(),
                            PropertyName = c.PropertyPath
                        });
                    }
                });

            RuleFor(x => x.Comments)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.CommentedBy))
                        .WithMessage(vr023.Message)
                        .WithErrorCode(vr023.Number.ToString())
                        .WithName(vr023.Type.ToString());

            RuleFor(x => x.CommentedBy)
                .Cascade(CascadeMode.Stop)
                .NotEmpty()
                    .When(x => !string.IsNullOrEmpty(x.Comments))
                        .WithMessage(vr024.Message)
                        .WithErrorCode(vr024.Number.ToString())
                        .WithName(vr024.Type.ToString());
        }
    }
}
