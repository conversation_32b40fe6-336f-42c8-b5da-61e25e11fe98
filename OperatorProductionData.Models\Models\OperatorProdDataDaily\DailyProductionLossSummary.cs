﻿namespace OperatorProductionData.Models.Models.OperatorProdDataDaily
{
    public class DailyProductionLossSummary
    {
        public string ComplexId { get; set; }
        public string FacilityId { get; set; }
        public string? SystemId { get; set; }
        public string? EquipmentId { get; set; }
        public string? WellId { get; set; }
        public decimal PlannedOilLoss_OFU { get; set; }
        public decimal PlannedOilLoss_SI { get; set; }
        public decimal UnplannedOilLoss_OFU { get; set; }
        public decimal UnplannedOilLoss_SI { get; set; }
        public string LossMotiveId { get; set; }
        public string? Comments { get; set; }
        public string? CommentedBy { get; set; }
    }
}
