﻿using Ae.Stratus.Core.Backend.NoSQL.Interfaces;
using Ae.Stratus.Core.Common.Api;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class ProdDataGasDailyVerificationView
    {
        [Key]
        public ProdDataGasDailyVerificationViewId Id { get; set; }
        public DateTime MaxCreatedDate { get; set; }
        public string ComplexId { get; set; }
        public string ProductionDate { get; set; }
        public DateTime ParsedProductionDate { get; set; }
        public ProdDataGasDaily Json { get; set; }
    }
}