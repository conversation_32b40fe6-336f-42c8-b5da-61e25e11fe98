using Ae.Stratus.Core.Common.Api;
using Ae.Stratus.Core.Common.Enums;
using Ae.Stratus.Core.Common.GridDataLoad;
using Microsoft.AspNetCore.Http.Extensions;
using OperatorProductionData.Models.Models.OperatorProdDataGasDaily;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace OperatorProductionData.WebApiServices
{
    public class ProdDataGasDailyVerificationViewWebApiService
    {
        private HttpClient _client { get; set; }

        public ProdDataGasDailyVerificationViewWebApiService(string BaseURL)
        {
            _client = new HttpClient
            {
                BaseAddress = new Uri(BaseURL)
            };
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        public virtual async Task<ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>> GetVerificationViewList(GridDataLoadOptions options, string accessToken = "")
        {
            _client.DefaultRequestHeaders.Clear();
            if (!string.IsNullOrEmpty(accessToken))
            {
                _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + accessToken);
            }

            HttpResponseMessage httpResponseMessage = await _client.PostAsJsonAsync("proddatagasdailyverificationview/getlist", options);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting verification view list",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<GridDataLoadResponse<ProdDataGasDailyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting verification view list",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }

        public virtual async Task<ApiResponse<ICollection<ProdDataGasDailyVerificationView>>> GetAllByProductionDateRange(string productionDateStart, string productionDateEnd)
        {
            _client.DefaultRequestHeaders.Clear();

            var queryParams = new QueryBuilder
            {
                { "productionDateStart", productionDateStart },
                { "productionDateEnd", productionDateEnd }
            };

            HttpResponseMessage httpResponseMessage = await _client.GetAsync("proddatagasdailyverificationview/getallbyproductiondaterange" + queryParams);
            if (httpResponseMessage.StatusCode != HttpStatusCode.OK)
            {
                return new ApiResponse<ICollection<ProdDataGasDailyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Status = (int)httpResponseMessage.StatusCode,
                            Title = "Error getting GetAllByProductionDate",
                            Detail = httpResponseMessage.ReasonPhrase
                        }
                    }
                };
            }

            ApiResponse<ICollection<ProdDataGasDailyVerificationView>> apiResponse = await httpResponseMessage.Content.ReadFromJsonAsync<ApiResponse<ICollection<ProdDataGasDailyVerificationView>>>();
            if (apiResponse == null)
            {
                return new ApiResponse<ICollection<ProdDataGasDailyVerificationView>>
                {
                    Status = ApiResponseStatus.Error,
                    Problems = new List<ApiProblemDetails>
                    {
                        new ApiProblemDetails
                        {
                            Title = "Error getting daily GetAllByProductionDate",
                            Detail = "Null response"
                        }
                    }
                };
            }

            return apiResponse;
        }
    }
}
