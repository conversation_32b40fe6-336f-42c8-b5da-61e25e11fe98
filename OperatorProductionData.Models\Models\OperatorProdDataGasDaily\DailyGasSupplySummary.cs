﻿namespace OperatorProductionData.Models.Models.OperatorProdDataGasDaily
{
    public class DailyGasSupplySummary
    {
        public string FacilityId { get; set; }
        public decimal TotalDomesticGasSupplyVolume_OFU { get; set; }
        public decimal TotalDomesticGasSupplyVolume_SI { get; set; }
        public decimal TotalDomesticGasSupplyEnergy_OFU { get; set; }
        public decimal TotalDomesticGasSupplyEnergy_SI { get; set; }
        public decimal TotalDomesticGasSupplyEnergy_Boe { get; set; }
        public decimal TotalDomesticGasSupplyMass {  get; set; }
        public decimal TotalDomesticGasSupplyGRHV { get; set; }

    }
}
