{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Debug",
      "Microsoft.EntityFrameworkCore.Database.Command": "Debug"
    }
  },
  "ConnectionStrings": {
    "OperatorProductionDataAuditDB": "Server=ae-photon-01;Database=SMP.OperatorProductionAudit;User ID=sa;Password=*********;MultipleActiveResultSets=true;Encrypt=True;TrustServerCertificate=True",
    "OperatorProductionDataMongoDB": "************************************************",
    "OperatorProductionDataDBName": "OperatorProductionDataDB_STG_2024_11_18"
  },
  "FileServiceBaseURL": "https://smp.aeeconomics.com/api/fileManager/",
  "SwaggerConfigs": {
    "SwaggerUi": true,
    "Scopes": [ "operator_api" ],
    "Title": "Operator Prodution Data",
    "Description": "An external API used by oil & gas Block Operators to submit the Production data into SMP.",
    "Versions": [ 1 ],
    "AuthorityUrl": "https://smp.aeeconomics.com/identity/sts",
    //    "BasePath": "/api/operator/prod-management"
    "BasePath": "",
    "EnableRequestResponseLogging": true
  }
}