﻿using OperatorProductionData.Models.Utilities;
using System;
using System.Collections.Generic;

namespace OperatorProductionData.Models.Models.OperatorProdDataDaily.ReportValidations
{
    public class LiftingAndAllocations
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Vessel { get; set; }
        public ValidationResult<decimal> QuantityLoaded_OFU { get; set; }
        public ValidationResult<decimal> QuantityLoaded_SI { get; set; }
        public List<Allocation> Allocations { get; set; }
    }
}
