﻿using Ae.Stratus.Core.EntityFramework.Base;
using AutoMapper;
using OperatorProductionData.Models.Models;
using OperatorProductionData.Services.Database;
using OperatorProductionData.Services.Database.Entities;

namespace OperatorProductionData.Services.Services
{
    public class OperationSummaryService : RepositoryBase<OperationSummaryEntity, OperationSummary>
    {
        private new readonly OperatorProductionDataDbContext _dbContext;

        public OperationSummaryService(OperatorProductionDataDbContext dbContext, IMapper mapper) : base(dbContext, mapper)
        {
            _dbContext = dbContext;
        }

        public async Task<int> SubmitOperationSummary(OperationSummary OperationSummary)
        {
            _dbContext.OperationSummary.Add(
                _mapper.Map<OperationSummary, OperationSummaryEntity>(OperationSummary)
            );
            return await _dbContext.SaveChangesAsync();
        }

       
    }
}
